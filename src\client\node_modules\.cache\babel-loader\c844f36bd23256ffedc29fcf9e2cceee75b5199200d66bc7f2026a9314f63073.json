{"ast": null, "code": "'use strict';\n\n/**\n * Wrap substitutions\n */\nmodule.exports = function wrap(substitutions) {\n  let left = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '{{';\n  let right = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '}}';\n  //Process arrays\n  if (Array.isArray(substitutions)) {\n    return substitutions.map(subs => wrap(subs, left, right));\n  }\n\n  //Initialize new wrapped object\n  const wrapped = {};\n\n  //Map substitutions and ensure string for value\n  for (const key in substitutions) {\n    //istanbul ignore else\n    if (substitutions.hasOwnProperty(key)) {\n      wrapped[left + key + right] = String(substitutions[key]);\n    }\n  }\n\n  //Return wrapped substitutions\n  return wrapped;\n};", "map": {"version": 3, "names": ["module", "exports", "wrap", "substitutions", "left", "arguments", "length", "undefined", "right", "Array", "isArray", "map", "subs", "wrapped", "key", "hasOwnProperty", "String"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js"], "sourcesContent": ["'use strict';\n\n/**\n * Wrap substitutions\n */\nmodule.exports = function wrap(substitutions, left = '{{', right = '}}') {\n\n  //Process arrays\n  if (Array.isArray(substitutions)) {\n    return substitutions.map(subs => wrap(subs, left, right));\n  }\n\n  //Initialize new wrapped object\n  const wrapped = {};\n\n  //Map substitutions and ensure string for value\n  for (const key in substitutions) {\n    //istanbul ignore else\n    if (substitutions.hasOwnProperty(key)) {\n      wrapped[left + key + right] = String(substitutions[key]);\n    }\n  }\n\n  //Return wrapped substitutions\n  return wrapped;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,aAAa,EAA6B;EAAA,IAA3BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAErE;EACA,IAAII,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,EAAE;IAChC,OAAOA,aAAa,CAACQ,GAAG,CAACC,IAAI,IAAIV,IAAI,CAACU,IAAI,EAAER,IAAI,EAAEI,KAAK,CAAC,CAAC;EAC3D;;EAEA;EACA,MAAMK,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA,KAAK,MAAMC,GAAG,IAAIX,aAAa,EAAE;IAC/B;IACA,IAAIA,aAAa,CAACY,cAAc,CAACD,GAAG,CAAC,EAAE;MACrCD,OAAO,CAACT,IAAI,GAAGU,GAAG,GAAGN,KAAK,CAAC,GAAGQ,MAAM,CAACb,aAAa,CAACW,GAAG,CAAC,CAAC;IAC1D;EACF;;EAEA;EACA,OAAOD,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}