{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst EmailAddress = require('./email-address');\nconst toCamelCase = require('../helpers/to-camel-case');\nconst toSnakeCase = require('../helpers/to-snake-case');\nconst deepClone = require('../helpers/deep-clone');\nconst deepMerge = require('deepmerge');\nconst wrapSubstitutions = require('../helpers/wrap-substitutions');\n\n/**\n * Personalization class\n */\nclass Personalization {\n  /**\n   * Constructor\n   */\n  constructor(data) {\n    //Init array and object placeholders\n    this.to = [];\n    this.cc = [];\n    this.bcc = [];\n    this.headers = {};\n    this.customArgs = {};\n    this.substitutions = {};\n    this.substitutionWrappers = ['{{', '}}'];\n    this.dynamicTemplateData = {};\n\n    //Build from data if given\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n\n    //Extract properties from data\n    const {\n      to,\n      from,\n      cc,\n      bcc,\n      subject,\n      headers,\n      customArgs,\n      sendAt,\n      substitutions,\n      substitutionWrappers,\n      dynamicTemplateData\n    } = data;\n\n    //Set data\n    this.setTo(to);\n    this.setFrom(from);\n    this.setCc(cc);\n    this.setBcc(bcc);\n    this.setSubject(subject);\n    this.setHeaders(headers);\n    this.setSubstitutions(substitutions);\n    this.setSubstitutionWrappers(substitutionWrappers);\n    this.setCustomArgs(customArgs);\n    this.setDynamicTemplateData(dynamicTemplateData);\n    this.setSendAt(sendAt);\n  }\n\n  /**\n   * Set subject\n   */\n  setSubject(subject) {\n    if (typeof subject === 'undefined') {\n      return;\n    }\n    if (typeof subject !== 'string') {\n      throw new Error('String expected for `subject`');\n    }\n    this.subject = subject;\n  }\n\n  /**\n   * Set send at\n   */\n  setSendAt(sendAt) {\n    if (typeof sendAt === 'undefined') {\n      return;\n    }\n    if (!Number.isInteger(sendAt)) {\n      throw new Error('Integer expected for `sendAt`');\n    }\n    this.sendAt = sendAt;\n  }\n\n  /**\n   * Set to\n   */\n  setTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(to)) {\n      to = [to];\n    }\n    this.to = EmailAddress.create(to);\n  }\n\n  /**\n   * Set from\n   * */\n  setFrom(from) {\n    if (typeof from === 'undefined') {\n      return;\n    }\n    this.from = EmailAddress.create(from);\n  }\n\n  /**\n   * Add a single to\n   */\n  addTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    this.to.push(EmailAddress.create(to));\n  }\n\n  /**\n   * Set cc\n   */\n  setCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(cc)) {\n      cc = [cc];\n    }\n    this.cc = EmailAddress.create(cc);\n  }\n\n  /**\n   * Add a single cc\n   */\n  addCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    this.cc.push(EmailAddress.create(cc));\n  }\n\n  /**\n   * Set bcc\n   */\n  setBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(bcc)) {\n      bcc = [bcc];\n    }\n    this.bcc = EmailAddress.create(bcc);\n  }\n\n  /**\n   * Add a single bcc\n   */\n  addBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    this.bcc.push(EmailAddress.create(bcc));\n  }\n\n  /**\n   * Set headers\n   */\n  setHeaders(headers) {\n    if (typeof headers === 'undefined') {\n      return;\n    }\n    if (typeof headers !== 'object' || headers === null) {\n      throw new Error('Object expected for `headers`');\n    }\n    this.headers = headers;\n  }\n\n  /**\n   * Add a header\n   */\n  addHeader(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for header key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for header value');\n    }\n    this.headers[key] = value;\n  }\n\n  /**\n   * Set custom args\n   */\n  setCustomArgs(customArgs) {\n    if (typeof customArgs === 'undefined') {\n      return;\n    }\n    if (typeof customArgs !== 'object' || customArgs === null) {\n      throw new Error('Object expected for `customArgs`');\n    }\n    this.customArgs = customArgs;\n  }\n\n  /**\n   * Add a custom arg\n   */\n  addCustomArg(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for custom arg key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for custom arg value');\n    }\n    this.customArgs[key] = value;\n  }\n\n  /**\n   * Set substitutions\n   */\n  setSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined') {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error('Object expected for `substitutions`');\n    }\n    this.substitutions = substitutions;\n  }\n\n  /**\n   * Add a substitution\n   */\n  addSubstitution(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for substitution key');\n    }\n    if (typeof value !== 'string' && typeof value !== 'number') {\n      throw new Error('String or Number expected for substitution value');\n    }\n    this.substitutions[key] = value;\n  }\n\n  /**\n   * Reverse merge substitutions, preserving existing ones\n   */\n  reverseMergeSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined' || substitutions === null) {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error('Object expected for `substitutions` in reverseMergeSubstitutions');\n    }\n    this.substitutions = Object.assign({}, substitutions, this.substitutions);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(wrappers) {\n    if (typeof wrappers === 'undefined' || wrappers === null) {\n      return;\n    }\n    if (!Array.isArray(wrappers) || wrappers.length !== 2) {\n      throw new Error('Array expected with two elements for `substitutionWrappers`');\n    }\n    this.substitutionWrappers = wrappers;\n  }\n\n  /**\n   * Reverse merge dynamic template data, preserving existing ones\n   */\n  deepMergeDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined' || dynamicTemplateData === null) {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error('Object expected for `dynamicTemplateData` in deepMergeDynamicTemplateData');\n    }\n    this.dynamicTemplateData = deepMerge(dynamicTemplateData, this.dynamicTemplateData);\n  }\n\n  /**\n   * Set dynamic template data\n   */\n  setDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined') {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error('Object expected for `dynamicTemplateData`');\n    }\n    this.dynamicTemplateData = dynamicTemplateData;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n    //Get data from self\n    const {\n      to,\n      from,\n      cc,\n      bcc,\n      subject,\n      headers,\n      customArgs,\n      sendAt,\n      substitutions,\n      substitutionWrappers,\n      dynamicTemplateData\n    } = this;\n\n    //Initialize with mandatory values\n    const json = {\n      to\n    };\n\n    //Arrays\n    if (Array.isArray(cc) && cc.length > 0) {\n      json.cc = cc;\n    }\n    if (Array.isArray(bcc) && bcc.length > 0) {\n      json.bcc = bcc;\n    }\n\n    //Objects\n    if (Object.keys(headers).length > 0) {\n      json.headers = headers;\n    }\n    if (substitutions && Object.keys(substitutions).length > 0) {\n      const [left, right] = substitutionWrappers;\n      json.substitutions = wrapSubstitutions(substitutions, left, right);\n    }\n    if (Object.keys(customArgs).length > 0) {\n      json.customArgs = customArgs;\n    }\n    if (dynamicTemplateData && Object.keys(dynamicTemplateData).length > 0) {\n      json.dynamicTemplateData = dynamicTemplateData;\n    }\n\n    //Simple properties\n    if (typeof subject !== 'undefined') {\n      json.subject = subject;\n    }\n    if (typeof sendAt !== 'undefined') {\n      json.sendAt = sendAt;\n    }\n    if (typeof from !== 'undefined') {\n      json.from = from;\n    }\n\n    //Return as snake cased object\n    return toSnakeCase(json, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n  }\n}\n\n//Export class\nmodule.exports = Personalization;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "toCamelCase", "toSnakeCase", "deepClone", "deepMerge", "wrapSubstitutions", "Personalization", "constructor", "data", "to", "cc", "bcc", "headers", "customArgs", "substitutions", "substitutionWrappers", "dynamicTemplateData", "fromData", "Error", "from", "subject", "sendAt", "setTo", "setFrom", "setCc", "setBcc", "setSubject", "setHeaders", "setSubstitutions", "setSubstitutionWrappers", "setCustomArgs", "setDynamicTemplateData", "setSendAt", "Number", "isInteger", "Array", "isArray", "create", "addTo", "push", "addCc", "addBcc", "addHeader", "key", "value", "addCustomArg", "addSubstitution", "reverseMergeSubstitutions", "Object", "assign", "wrappers", "length", "deepMergeDynamicTemplateData", "toJSON", "json", "keys", "left", "right", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/personalization.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst EmailAddress = require('./email-address');\nconst toCamelCase = require('../helpers/to-camel-case');\nconst toSnakeCase = require('../helpers/to-snake-case');\nconst deepClone = require('../helpers/deep-clone');\nconst deepMerge = require('deepmerge');\nconst wrapSubstitutions = require('../helpers/wrap-substitutions');\n\n/**\n * Personalization class\n */\nclass Personalization {\n\n  /**\n   * Constructor\n   */\n  constructor(data) {\n\n    //Init array and object placeholders\n    this.to = [];\n    this.cc = [];\n    this.bcc = [];\n    this.headers = {};\n    this.customArgs = {};\n    this.substitutions = {};\n    this.substitutionWrappers = ['{{', '}}'];\n    this.dynamicTemplateData = {};\n\n    //Build from data if given\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n\n    //Extract properties from data\n    const {\n      to, from, cc, bcc, subject, headers, customArgs, sendAt,\n      substitutions, substitutionWrappers, dynamicTemplateData,\n    } = data;\n\n    //Set data\n    this.setTo(to);\n    this.setFrom(from);\n    this.setCc(cc);\n    this.setBcc(bcc);\n    this.setSubject(subject);\n    this.setHeaders(headers);\n    this.setSubstitutions(substitutions);\n    this.setSubstitutionWrappers(substitutionWrappers);\n    this.setCustomArgs(customArgs);\n    this.setDynamicTemplateData(dynamicTemplateData);\n    this.setSendAt(sendAt);\n  }\n\n  /**\n   * Set subject\n   */\n  setSubject(subject) {\n    if (typeof subject === 'undefined') {\n      return;\n    }\n    if (typeof subject !== 'string') {\n      throw new Error('String expected for `subject`');\n    }\n    this.subject = subject;\n  }\n\n  /**\n   * Set send at\n   */\n  setSendAt(sendAt) {\n    if (typeof sendAt === 'undefined') {\n      return;\n    }\n    if (!Number.isInteger(sendAt)) {\n      throw new Error('Integer expected for `sendAt`');\n    }\n    this.sendAt = sendAt;\n  }\n\n  /**\n   * Set to\n   */\n  setTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(to)) {\n      to = [to];\n    }\n    this.to = EmailAddress.create(to);\n  }\n\n  /**\n   * Set from\n   * */\n  setFrom(from) {\n    if (typeof from === 'undefined') {\n      return;\n    }\n    this.from = EmailAddress.create(from);\n  }\n\n  /**\n   * Add a single to\n   */\n  addTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    this.to.push(EmailAddress.create(to));\n  }\n\n  /**\n   * Set cc\n   */\n  setCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(cc)) {\n      cc = [cc];\n    }\n    this.cc = EmailAddress.create(cc);\n  }\n\n  /**\n   * Add a single cc\n   */\n  addCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    this.cc.push(EmailAddress.create(cc));\n  }\n\n  /**\n   * Set bcc\n   */\n  setBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(bcc)) {\n      bcc = [bcc];\n    }\n    this.bcc = EmailAddress.create(bcc);\n  }\n\n  /**\n   * Add a single bcc\n   */\n  addBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    this.bcc.push(EmailAddress.create(bcc));\n  }\n\n  /**\n   * Set headers\n   */\n  setHeaders(headers) {\n    if (typeof headers === 'undefined') {\n      return;\n    }\n    if (typeof headers !== 'object' || headers === null) {\n      throw new Error('Object expected for `headers`');\n    }\n    this.headers = headers;\n  }\n\n  /**\n   * Add a header\n   */\n  addHeader(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for header key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for header value');\n    }\n    this.headers[key] = value;\n  }\n\n  /**\n   * Set custom args\n   */\n  setCustomArgs(customArgs) {\n    if (typeof customArgs === 'undefined') {\n      return;\n    }\n    if (typeof customArgs !== 'object' || customArgs === null) {\n      throw new Error('Object expected for `customArgs`');\n    }\n    this.customArgs = customArgs;\n  }\n\n  /**\n   * Add a custom arg\n   */\n  addCustomArg(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for custom arg key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for custom arg value');\n    }\n    this.customArgs[key] = value;\n  }\n\n  /**\n   * Set substitutions\n   */\n  setSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined') {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error('Object expected for `substitutions`');\n    }\n    this.substitutions = substitutions;\n  }\n\n  /**\n   * Add a substitution\n   */\n  addSubstitution(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for substitution key');\n    }\n    if (typeof value !== 'string' && typeof value !== 'number') {\n      throw new Error('String or Number expected for substitution value');\n    }\n    this.substitutions[key] = value;\n  }\n\n  /**\n   * Reverse merge substitutions, preserving existing ones\n   */\n  reverseMergeSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined' || substitutions === null) {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error(\n        'Object expected for `substitutions` in reverseMergeSubstitutions'\n      );\n    }\n    this.substitutions = Object.assign({}, substitutions, this.substitutions);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(wrappers) {\n    if (typeof wrappers === 'undefined' || wrappers === null) {\n      return;\n    }\n\n    if (!Array.isArray(wrappers) || wrappers.length !== 2) {\n      throw new Error(\n        'Array expected with two elements for `substitutionWrappers`'\n      );\n    }\n    this.substitutionWrappers = wrappers;\n  }\n\n  /**\n   * Reverse merge dynamic template data, preserving existing ones\n   */\n  deepMergeDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined' || dynamicTemplateData === null) {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error(\n        'Object expected for `dynamicTemplateData` in deepMergeDynamicTemplateData'\n      );\n    }\n    this.dynamicTemplateData = deepMerge(dynamicTemplateData, this.dynamicTemplateData);\n  }\n\n  /**\n   * Set dynamic template data\n   */\n  setDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined') {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error('Object expected for `dynamicTemplateData`');\n    }\n    this.dynamicTemplateData = dynamicTemplateData;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n\n    //Get data from self\n    const {\n      to, from, cc, bcc, subject, headers, customArgs, sendAt,\n      substitutions, substitutionWrappers, dynamicTemplateData,\n    } = this;\n\n    //Initialize with mandatory values\n    const json = {to};\n\n    //Arrays\n    if (Array.isArray(cc) && cc.length > 0) {\n      json.cc = cc;\n    }\n    if (Array.isArray(bcc) && bcc.length > 0) {\n      json.bcc = bcc;\n    }\n\n    //Objects\n    if (Object.keys(headers).length > 0) {\n      json.headers = headers;\n    }\n    if (substitutions && Object.keys(substitutions).length > 0) {\n      const [left, right] = substitutionWrappers;\n      json.substitutions = wrapSubstitutions(substitutions, left, right);\n    }\n    if (Object.keys(customArgs).length > 0) {\n      json.customArgs = customArgs;\n    }\n\n    if (dynamicTemplateData && Object.keys(dynamicTemplateData).length > 0) {\n      json.dynamicTemplateData = dynamicTemplateData;\n    }\n\n    //Simple properties\n    if (typeof subject !== 'undefined') {\n      json.subject = subject;\n    }\n    if (typeof sendAt !== 'undefined') {\n      json.sendAt = sendAt;\n    }\n    if (typeof from !== 'undefined') {\n      json.from = from;\n    }\n\n    //Return as snake cased object\n    return toSnakeCase(json, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n  }\n}\n\n//Export class\nmodule.exports = Personalization;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMC,WAAW,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACvD,MAAME,WAAW,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACvD,MAAMG,SAAS,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAClD,MAAMI,SAAS,GAAGJ,OAAO,CAAC,WAAW,CAAC;AACtC,MAAMK,iBAAiB,GAAGL,OAAO,CAAC,+BAA+B,CAAC;;AAElE;AACA;AACA;AACA,MAAMM,eAAe,CAAC;EAEpB;AACF;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAEhB;IACA,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,oBAAoB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAIR,IAAI,EAAE;MACR,IAAI,CAACS,QAAQ,CAACT,IAAI,CAAC;IACrB;EACF;;EAEA;AACF;AACA;EACES,QAAQA,CAACT,IAAI,EAAE;IAEb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIU,KAAK,CAAC,gCAAgC,CAAC;IACnD;;IAEA;IACA;IACAV,IAAI,GAAGL,SAAS,CAACK,IAAI,CAAC;IACtBA,IAAI,GAAGP,WAAW,CAACO,IAAI,EAAE,CAAC,eAAe,EAAE,qBAAqB,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;;IAE3F;IACA,MAAM;MACJC,EAAE;MAAEU,IAAI;MAAET,EAAE;MAAEC,GAAG;MAAES,OAAO;MAAER,OAAO;MAAEC,UAAU;MAAEQ,MAAM;MACvDP,aAAa;MAAEC,oBAAoB;MAAEC;IACvC,CAAC,GAAGR,IAAI;;IAER;IACA,IAAI,CAACc,KAAK,CAACb,EAAE,CAAC;IACd,IAAI,CAACc,OAAO,CAACJ,IAAI,CAAC;IAClB,IAAI,CAACK,KAAK,CAACd,EAAE,CAAC;IACd,IAAI,CAACe,MAAM,CAACd,GAAG,CAAC;IAChB,IAAI,CAACe,UAAU,CAACN,OAAO,CAAC;IACxB,IAAI,CAACO,UAAU,CAACf,OAAO,CAAC;IACxB,IAAI,CAACgB,gBAAgB,CAACd,aAAa,CAAC;IACpC,IAAI,CAACe,uBAAuB,CAACd,oBAAoB,CAAC;IAClD,IAAI,CAACe,aAAa,CAACjB,UAAU,CAAC;IAC9B,IAAI,CAACkB,sBAAsB,CAACf,mBAAmB,CAAC;IAChD,IAAI,CAACgB,SAAS,CAACX,MAAM,CAAC;EACxB;;EAEA;AACF;AACA;EACEK,UAAUA,CAACN,OAAO,EAAE;IAClB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClC;IACF;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAM,IAAIF,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACA,IAAI,CAACE,OAAO,GAAGA,OAAO;EACxB;;EAEA;AACF;AACA;EACEY,SAASA,CAACX,MAAM,EAAE;IAChB,IAAI,OAAOA,MAAM,KAAK,WAAW,EAAE;MACjC;IACF;IACA,IAAI,CAACY,MAAM,CAACC,SAAS,CAACb,MAAM,CAAC,EAAE;MAC7B,MAAM,IAAIH,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACA,IAAI,CAACG,MAAM,GAAGA,MAAM;EACtB;;EAEA;AACF;AACA;EACEC,KAAKA,CAACb,EAAE,EAAE;IACR,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7B;IACF;IACA,IAAI,CAAC0B,KAAK,CAACC,OAAO,CAAC3B,EAAE,CAAC,EAAE;MACtBA,EAAE,GAAG,CAACA,EAAE,CAAC;IACX;IACA,IAAI,CAACA,EAAE,GAAGV,YAAY,CAACsC,MAAM,CAAC5B,EAAE,CAAC;EACnC;;EAEA;AACF;AACA;EACEc,OAAOA,CAACJ,IAAI,EAAE;IACZ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC/B;IACF;IACA,IAAI,CAACA,IAAI,GAAGpB,YAAY,CAACsC,MAAM,CAAClB,IAAI,CAAC;EACvC;;EAEA;AACF;AACA;EACEmB,KAAKA,CAAC7B,EAAE,EAAE;IACR,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7B;IACF;IACA,IAAI,CAACA,EAAE,CAAC8B,IAAI,CAACxC,YAAY,CAACsC,MAAM,CAAC5B,EAAE,CAAC,CAAC;EACvC;;EAEA;AACF;AACA;EACEe,KAAKA,CAACd,EAAE,EAAE;IACR,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7B;IACF;IACA,IAAI,CAACyB,KAAK,CAACC,OAAO,CAAC1B,EAAE,CAAC,EAAE;MACtBA,EAAE,GAAG,CAACA,EAAE,CAAC;IACX;IACA,IAAI,CAACA,EAAE,GAAGX,YAAY,CAACsC,MAAM,CAAC3B,EAAE,CAAC;EACnC;;EAEA;AACF;AACA;EACE8B,KAAKA,CAAC9B,EAAE,EAAE;IACR,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7B;IACF;IACA,IAAI,CAACA,EAAE,CAAC6B,IAAI,CAACxC,YAAY,CAACsC,MAAM,CAAC3B,EAAE,CAAC,CAAC;EACvC;;EAEA;AACF;AACA;EACEe,MAAMA,CAACd,GAAG,EAAE;IACV,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;MAC9B;IACF;IACA,IAAI,CAACwB,KAAK,CAACC,OAAO,CAACzB,GAAG,CAAC,EAAE;MACvBA,GAAG,GAAG,CAACA,GAAG,CAAC;IACb;IACA,IAAI,CAACA,GAAG,GAAGZ,YAAY,CAACsC,MAAM,CAAC1B,GAAG,CAAC;EACrC;;EAEA;AACF;AACA;EACE8B,MAAMA,CAAC9B,GAAG,EAAE;IACV,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;MAC9B;IACF;IACA,IAAI,CAACA,GAAG,CAAC4B,IAAI,CAACxC,YAAY,CAACsC,MAAM,CAAC1B,GAAG,CAAC,CAAC;EACzC;;EAEA;AACF;AACA;EACEgB,UAAUA,CAACf,OAAO,EAAE;IAClB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClC;IACF;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;MACnD,MAAM,IAAIM,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACA,IAAI,CAACN,OAAO,GAAGA,OAAO;EACxB;;EAEA;AACF;AACA;EACE8B,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACpB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAIzB,KAAK,CAAC,gCAAgC,CAAC;IACnD;IACA,IAAI,OAAO0B,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAI1B,KAAK,CAAC,kCAAkC,CAAC;IACrD;IACA,IAAI,CAACN,OAAO,CAAC+B,GAAG,CAAC,GAAGC,KAAK;EAC3B;;EAEA;AACF;AACA;EACEd,aAAaA,CAACjB,UAAU,EAAE;IACxB,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;MACrC;IACF;IACA,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,IAAI,EAAE;MACzD,MAAM,IAAIK,KAAK,CAAC,kCAAkC,CAAC;IACrD;IACA,IAAI,CAACL,UAAU,GAAGA,UAAU;EAC9B;;EAEA;AACF;AACA;EACEgC,YAAYA,CAACF,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAIzB,KAAK,CAAC,oCAAoC,CAAC;IACvD;IACA,IAAI,OAAO0B,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAI1B,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACA,IAAI,CAACL,UAAU,CAAC8B,GAAG,CAAC,GAAGC,KAAK;EAC9B;;EAEA;AACF;AACA;EACEhB,gBAAgBA,CAACd,aAAa,EAAE;IAC9B,IAAI,OAAOA,aAAa,KAAK,WAAW,EAAE;MACxC;IACF;IACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACrC,MAAM,IAAII,KAAK,CAAC,qCAAqC,CAAC;IACxD;IACA,IAAI,CAACJ,aAAa,GAAGA,aAAa;EACpC;;EAEA;AACF;AACA;EACEgC,eAAeA,CAACH,GAAG,EAAEC,KAAK,EAAE;IAC1B,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAIzB,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACA,IAAI,OAAO0B,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC1D,MAAM,IAAI1B,KAAK,CAAC,kDAAkD,CAAC;IACrE;IACA,IAAI,CAACJ,aAAa,CAAC6B,GAAG,CAAC,GAAGC,KAAK;EACjC;;EAEA;AACF;AACA;EACEG,yBAAyBA,CAACjC,aAAa,EAAE;IACvC,IAAI,OAAOA,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;MAClE;IACF;IACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACrC,MAAM,IAAII,KAAK,CACb,kEACF,CAAC;IACH;IACA,IAAI,CAACJ,aAAa,GAAGkC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,aAAa,EAAE,IAAI,CAACA,aAAa,CAAC;EAC3E;;EAEA;AACF;AACA;EACEe,uBAAuBA,CAACqB,QAAQ,EAAE;IAChC,IAAI,OAAOA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACxD;IACF;IAEA,IAAI,CAACf,KAAK,CAACC,OAAO,CAACc,QAAQ,CAAC,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACrD,MAAM,IAAIjC,KAAK,CACb,6DACF,CAAC;IACH;IACA,IAAI,CAACH,oBAAoB,GAAGmC,QAAQ;EACtC;;EAEA;AACF;AACA;EACEE,4BAA4BA,CAACpC,mBAAmB,EAAE;IAChD,IAAI,OAAOA,mBAAmB,KAAK,WAAW,IAAIA,mBAAmB,KAAK,IAAI,EAAE;MAC9E;IACF;IACA,IAAI,OAAOA,mBAAmB,KAAK,QAAQ,EAAE;MAC3C,MAAM,IAAIE,KAAK,CACb,2EACF,CAAC;IACH;IACA,IAAI,CAACF,mBAAmB,GAAGZ,SAAS,CAACY,mBAAmB,EAAE,IAAI,CAACA,mBAAmB,CAAC;EACrF;;EAEA;AACF;AACA;EACEe,sBAAsBA,CAACf,mBAAmB,EAAE;IAC1C,IAAI,OAAOA,mBAAmB,KAAK,WAAW,EAAE;MAC9C;IACF;IACA,IAAI,OAAOA,mBAAmB,KAAK,QAAQ,EAAE;MAC3C,MAAM,IAAIE,KAAK,CAAC,2CAA2C,CAAC;IAC9D;IACA,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB;EAChD;;EAEA;AACF;AACA;EACEqC,MAAMA,CAAA,EAAG;IAEP;IACA,MAAM;MACJ5C,EAAE;MAAEU,IAAI;MAAET,EAAE;MAAEC,GAAG;MAAES,OAAO;MAAER,OAAO;MAAEC,UAAU;MAAEQ,MAAM;MACvDP,aAAa;MAAEC,oBAAoB;MAAEC;IACvC,CAAC,GAAG,IAAI;;IAER;IACA,MAAMsC,IAAI,GAAG;MAAC7C;IAAE,CAAC;;IAEjB;IACA,IAAI0B,KAAK,CAACC,OAAO,CAAC1B,EAAE,CAAC,IAAIA,EAAE,CAACyC,MAAM,GAAG,CAAC,EAAE;MACtCG,IAAI,CAAC5C,EAAE,GAAGA,EAAE;IACd;IACA,IAAIyB,KAAK,CAACC,OAAO,CAACzB,GAAG,CAAC,IAAIA,GAAG,CAACwC,MAAM,GAAG,CAAC,EAAE;MACxCG,IAAI,CAAC3C,GAAG,GAAGA,GAAG;IAChB;;IAEA;IACA,IAAIqC,MAAM,CAACO,IAAI,CAAC3C,OAAO,CAAC,CAACuC,MAAM,GAAG,CAAC,EAAE;MACnCG,IAAI,CAAC1C,OAAO,GAAGA,OAAO;IACxB;IACA,IAAIE,aAAa,IAAIkC,MAAM,CAACO,IAAI,CAACzC,aAAa,CAAC,CAACqC,MAAM,GAAG,CAAC,EAAE;MAC1D,MAAM,CAACK,IAAI,EAAEC,KAAK,CAAC,GAAG1C,oBAAoB;MAC1CuC,IAAI,CAACxC,aAAa,GAAGT,iBAAiB,CAACS,aAAa,EAAE0C,IAAI,EAAEC,KAAK,CAAC;IACpE;IACA,IAAIT,MAAM,CAACO,IAAI,CAAC1C,UAAU,CAAC,CAACsC,MAAM,GAAG,CAAC,EAAE;MACtCG,IAAI,CAACzC,UAAU,GAAGA,UAAU;IAC9B;IAEA,IAAIG,mBAAmB,IAAIgC,MAAM,CAACO,IAAI,CAACvC,mBAAmB,CAAC,CAACmC,MAAM,GAAG,CAAC,EAAE;MACtEG,IAAI,CAACtC,mBAAmB,GAAGA,mBAAmB;IAChD;;IAEA;IACA,IAAI,OAAOI,OAAO,KAAK,WAAW,EAAE;MAClCkC,IAAI,CAAClC,OAAO,GAAGA,OAAO;IACxB;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MACjCiC,IAAI,CAACjC,MAAM,GAAGA,MAAM;IACtB;IACA,IAAI,OAAOF,IAAI,KAAK,WAAW,EAAE;MAC/BmC,IAAI,CAACnC,IAAI,GAAGA,IAAI;IAClB;;IAEA;IACA,OAAOjB,WAAW,CAACoD,IAAI,EAAE,CAAC,eAAe,EAAE,qBAAqB,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;EAC7F;AACF;;AAEA;AACAI,MAAM,CAACC,OAAO,GAAGrD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}