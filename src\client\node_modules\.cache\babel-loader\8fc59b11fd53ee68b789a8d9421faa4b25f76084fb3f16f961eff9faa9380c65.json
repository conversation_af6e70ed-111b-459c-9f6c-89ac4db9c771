{"ast": null, "code": "const isMotionValue = value => Boolean(value && value.getVelocity);\nexport { isMotionValue };", "map": {"version": 3, "names": ["isMotionValue", "value", "Boolean", "getVelocity"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs"], "sourcesContent": ["const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAIC,KAAK,IAAKC,OAAO,CAACD,KAAK,IAAIA,KAAK,CAACE,WAAW,CAAC;AAEpE,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}