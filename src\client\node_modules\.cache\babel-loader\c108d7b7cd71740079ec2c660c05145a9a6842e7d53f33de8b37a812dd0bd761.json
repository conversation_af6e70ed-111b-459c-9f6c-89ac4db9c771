{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation(_ref, key) {\n  let {\n    protectedKeys,\n    needsAnimating\n  } = _ref;\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n  const current = value.get();\n  if (Array.isArray(target)) {\n    for (let i = 0; i < target.length; i++) {\n      if (target[i] !== current) return true;\n    }\n  } else {\n    return current !== target;\n  }\n}\nfunction animateTarget(visualElement, definition) {\n  let {\n    delay = 0,\n    transitionOverride,\n    type\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = visualElement.makeTargetAnimatable(definition);\n  const willChange = visualElement.getValue(\"willChange\");\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key);\n    const valueTarget = target[key];\n    if (!value || valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = {\n      delay,\n      elapsed: 0,\n      ...getValueTransition(transition || {}, key)\n    };\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    if (window.HandoffAppearAnimations) {\n      const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n      if (appearId) {\n        const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n        if (elapsed !== null) {\n          valueTransition.elapsed = elapsed;\n          valueTransition.isHandoff = true;\n        }\n      }\n    }\n    let canSkip = !valueTransition.isHandoff && !hasKeyframesChanged(value, valueTarget);\n    if (valueTransition.type === \"spring\" && (value.getVelocity() || valueTransition.velocity)) {\n      canSkip = false;\n    }\n    /**\n     * Temporarily disable skipping animations if there's an animation in\n     * progress. Better would be to track the current target of a value\n     * and compare that against valueTarget.\n     */\n    if (value.animation) {\n      canSkip = false;\n    }\n    if (canSkip) continue;\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key) ? {\n      type: false\n    } : valueTransition));\n    const animation = value.animation;\n    if (isWillChangeMotionValue(willChange)) {\n      willChange.add(key);\n      animation.then(() => willChange.remove(key));\n    }\n    animations.push(animation);\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      transitionEnd && setTarget(visualElement, transitionEnd);\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["transformProps", "optimizedAppearDataAttribute", "animateMotionValue", "isWillChangeMotionValue", "<PERSON><PERSON><PERSON><PERSON>", "getValueTransition", "frame", "shouldBlockAnimation", "_ref", "key", "protected<PERSON><PERSON>s", "needsAnimating", "shouldBlock", "hasOwnProperty", "hasKeyframesChanged", "value", "target", "current", "get", "Array", "isArray", "i", "length", "animate<PERSON>arget", "visualElement", "definition", "delay", "transitionOverride", "type", "arguments", "undefined", "transition", "getDefaultTransition", "transitionEnd", "makeTargetAnimatable", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "animations", "animationTypeState", "animationState", "getState", "valueTarget", "valueTransition", "elapsed", "window", "HandoffAppearAnimations", "appearId", "getProps", "<PERSON><PERSON><PERSON><PERSON>", "canSkip", "getVelocity", "velocity", "animation", "start", "shouldReduceMotion", "has", "add", "then", "remove", "push", "Promise", "all"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n    const current = value.get();\n    if (Array.isArray(target)) {\n        for (let i = 0; i < target.length; i++) {\n            if (target[i] !== current)\n                return true;\n        }\n    }\n    else {\n        return current !== target;\n    }\n}\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            elapsed: 0,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (window.HandoffAppearAnimations) {\n            const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n            if (appearId) {\n                const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n                if (elapsed !== null) {\n                    valueTransition.elapsed = elapsed;\n                    valueTransition.isHandoff = true;\n                }\n            }\n        }\n        let canSkip = !valueTransition.isHandoff &&\n            !hasKeyframesChanged(value, valueTarget);\n        if (valueTransition.type === \"spring\" &&\n            (value.getVelocity() || valueTransition.velocity)) {\n            canSkip = false;\n        }\n        /**\n         * Temporarily disable skipping animations if there's an animation in\n         * progress. Better would be to track the current target of a value\n         * and compare that against valueTarget.\n         */\n        if (value.animation) {\n            canSkip = false;\n        }\n        if (canSkip)\n            continue;\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key)\n            ? { type: false }\n            : valueTransition));\n        const animation = value.animation;\n        if (isWillChangeMotionValue(willChange)) {\n            willChange.add(key);\n            animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            transitionEnd && setTarget(visualElement, transitionEnd);\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAAC,IAAA,EAAoCC,GAAG,EAAE;EAAA,IAAxC;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAAH,IAAA;EAC3D,MAAMI,WAAW,GAAGF,aAAa,CAACG,cAAc,CAACJ,GAAG,CAAC,IAAIE,cAAc,CAACF,GAAG,CAAC,KAAK,IAAI;EACrFE,cAAc,CAACF,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOG,WAAW;AACtB;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACxC,MAAMC,OAAO,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;EAC3B,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAIL,MAAM,CAACK,CAAC,CAAC,KAAKJ,OAAO,EACrB,OAAO,IAAI;IACnB;EACJ,CAAC,MACI;IACD,OAAOA,OAAO,KAAKD,MAAM;EAC7B;AACJ;AACA,SAASO,aAAaA,CAACC,aAAa,EAAEC,UAAU,EAAgD;EAAA,IAA9C;IAAEC,KAAK,GAAG,CAAC;IAAEC,kBAAkB;IAAEC;EAAK,CAAC,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC1F,IAAI;IAAEE,UAAU,GAAGP,aAAa,CAACQ,oBAAoB,CAAC,CAAC;IAAEC,aAAa;IAAE,GAAGjB;EAAO,CAAC,GAAGQ,aAAa,CAACU,oBAAoB,CAACT,UAAU,CAAC;EACpI,MAAMU,UAAU,GAAGX,aAAa,CAACY,QAAQ,CAAC,YAAY,CAAC;EACvD,IAAIT,kBAAkB,EAClBI,UAAU,GAAGJ,kBAAkB;EACnC,MAAMU,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGV,IAAI,IAC3BJ,aAAa,CAACe,cAAc,IAC5Bf,aAAa,CAACe,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACZ,IAAI,CAAC;EACjD,KAAK,MAAMnB,GAAG,IAAIO,MAAM,EAAE;IACtB,MAAMD,KAAK,GAAGS,aAAa,CAACY,QAAQ,CAAC3B,GAAG,CAAC;IACzC,MAAMgC,WAAW,GAAGzB,MAAM,CAACP,GAAG,CAAC;IAC/B,IAAI,CAACM,KAAK,IACN0B,WAAW,KAAKX,SAAS,IACxBQ,kBAAkB,IACf/B,oBAAoB,CAAC+B,kBAAkB,EAAE7B,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAMiC,eAAe,GAAG;MACpBhB,KAAK;MACLiB,OAAO,EAAE,CAAC;MACV,GAAGtC,kBAAkB,CAAC0B,UAAU,IAAI,CAAC,CAAC,EAAEtB,GAAG;IAC/C,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAImC,MAAM,CAACC,uBAAuB,EAAE;MAChC,MAAMC,QAAQ,GAAGtB,aAAa,CAACuB,QAAQ,CAAC,CAAC,CAAC9C,4BAA4B,CAAC;MACvE,IAAI6C,QAAQ,EAAE;QACV,MAAMH,OAAO,GAAGC,MAAM,CAACC,uBAAuB,CAACC,QAAQ,EAAErC,GAAG,EAAEM,KAAK,EAAET,KAAK,CAAC;QAC3E,IAAIqC,OAAO,KAAK,IAAI,EAAE;UAClBD,eAAe,CAACC,OAAO,GAAGA,OAAO;UACjCD,eAAe,CAACM,SAAS,GAAG,IAAI;QACpC;MACJ;IACJ;IACA,IAAIC,OAAO,GAAG,CAACP,eAAe,CAACM,SAAS,IACpC,CAAClC,mBAAmB,CAACC,KAAK,EAAE0B,WAAW,CAAC;IAC5C,IAAIC,eAAe,CAACd,IAAI,KAAK,QAAQ,KAChCb,KAAK,CAACmC,WAAW,CAAC,CAAC,IAAIR,eAAe,CAACS,QAAQ,CAAC,EAAE;MACnDF,OAAO,GAAG,KAAK;IACnB;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIlC,KAAK,CAACqC,SAAS,EAAE;MACjBH,OAAO,GAAG,KAAK;IACnB;IACA,IAAIA,OAAO,EACP;IACJlC,KAAK,CAACsC,KAAK,CAACnD,kBAAkB,CAACO,GAAG,EAAEM,KAAK,EAAE0B,WAAW,EAAEjB,aAAa,CAAC8B,kBAAkB,IAAItD,cAAc,CAACuD,GAAG,CAAC9C,GAAG,CAAC,GAC7G;MAAEmB,IAAI,EAAE;IAAM,CAAC,GACfc,eAAe,CAAC,CAAC;IACvB,MAAMU,SAAS,GAAGrC,KAAK,CAACqC,SAAS;IACjC,IAAIjD,uBAAuB,CAACgC,UAAU,CAAC,EAAE;MACrCA,UAAU,CAACqB,GAAG,CAAC/C,GAAG,CAAC;MACnB2C,SAAS,CAACK,IAAI,CAAC,MAAMtB,UAAU,CAACuB,MAAM,CAACjD,GAAG,CAAC,CAAC;IAChD;IACA4B,UAAU,CAACsB,IAAI,CAACP,SAAS,CAAC;EAC9B;EACA,IAAInB,aAAa,EAAE;IACf2B,OAAO,CAACC,GAAG,CAACxB,UAAU,CAAC,CAACoB,IAAI,CAAC,MAAM;MAC/BxB,aAAa,IAAI7B,SAAS,CAACoB,aAAa,EAAES,aAAa,CAAC;IAC5D,CAAC,CAAC;EACN;EACA,OAAOI,UAAU;AACrB;AAEA,SAASd,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}