{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\LandingPage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-900 text-white min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-5xl font-bold mb-4\",\n          children: \"Welcome to Blaze Trade\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-400 mb-8\",\n          children: \"Your partner in innovative trading solutions. We provide cutting-edge tools and services to help you navigate the financial markets with confidence.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"bg-gray-700 hover:bg-gray-800 text-white font-bold py-3 px-6 rounded-lg transition duration-300\",\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-20\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-center mb-10\",\n          children: \"Our Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-800 p-8 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-3\",\n              children: \"Advanced Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Leverage our powerful analytics tools to gain deep insights into market trends and make data-driven decisions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-800 p-8 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-3\",\n              children: \"Secure Trading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Trade with peace of mind. Our platform is built with state-of-the-art security to protect your assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-800 p-8 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-3\",\n              children: \"24/7 Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Our dedicated support team is available around the clock to assist you with any questions or issues.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "LandingPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/LandingPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst LandingPage = () => {\n  return (\n    <div className=\"bg-gray-900 text-white min-h-screen\">\n      <main className=\"container mx-auto px-6 py-12\">\n        <section className=\"text-center\">\n          <h1 className=\"text-5xl font-bold mb-4\">Welcome to Blaze Trade</h1>\n          <p className=\"text-lg text-gray-400 mb-8\">Your partner in innovative trading solutions. We provide cutting-edge tools and services to help you navigate the financial markets with confidence.</p>\n          <div className=\"space-x-4\">\n            <Link to=\"/login\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\">Login</Link>\n            <Link to=\"/signup\" className=\"bg-gray-700 hover:bg-gray-800 text-white font-bold py-3 px-6 rounded-lg transition duration-300\">Sign Up</Link>\n          </div>\n        </section>\n\n        <section className=\"mt-20\">\n          <h2 className=\"text-4xl font-bold text-center mb-10\">Our Services</h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-gray-800 p-8 rounded-lg\">\n              <h3 className=\"text-2xl font-bold mb-3\">Advanced Analytics</h3>\n              <p className=\"text-gray-400\">Leverage our powerful analytics tools to gain deep insights into market trends and make data-driven decisions.</p>\n            </div>\n            <div className=\"bg-gray-800 p-8 rounded-lg\">\n              <h3 className=\"text-2xl font-bold mb-3\">Secure Trading</h3>\n              <p className=\"text-gray-400\">Trade with peace of mind. Our platform is built with state-of-the-art security to protect your assets.</p>\n            </div>\n            <div className=\"bg-gray-800 p-8 rounded-lg\">\n              <h3 className=\"text-2xl font-bold mb-3\">24/7 Support</h3>\n              <p className=\"text-gray-400\">Our dedicated support team is available around the clock to assist you with any questions or issues.</p>\n            </div>\n          </div>\n        </section>\n      </main>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACED,OAAA;IAAKE,SAAS,EAAC,qCAAqC;IAAAC,QAAA,eAClDH,OAAA;MAAME,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC5CH,OAAA;QAASE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC9BH,OAAA;UAAIE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEP,OAAA;UAAGE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAoJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClMP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,QAAQ;YAACN,SAAS,EAAC,iGAAiG;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1IP,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,SAAS;YAACN,SAAS,EAAC,iGAAiG;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVP,OAAA;QAASE,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACxBH,OAAA;UAAIE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEP,OAAA;UAAKE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCH,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAIE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA8G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5I,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAIE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAIE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACE,EAAA,GAjCIR,WAAW;AAmCjB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}