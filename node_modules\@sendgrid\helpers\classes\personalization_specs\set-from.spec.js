'use strict';

/**
 * Dependencies
 */
const Personalization = require('../personalization');
const EmailAddress = require('../email-address');

/**
 * Tests
 */
describe('Personalization', function() {

  //Create new personalization before each test
  let p;
  beforeEach(function() {
    p = new Personalization();
  });

  //Set from
  describe('setFrom()', function() {
    it('should accept string values', function() {
      p.setFrom('<EMAIL>');
      expect(p.from).to.be.an.instanceof(EmailAddress);
      expect(p.from.email).to.equal('<EMAIL>');
    });
    it('should properly update from value', function() {
      p.setFrom('<EMAIL>');
      p.setFrom('<EMAIL>');
      p.setFrom('<EMAIL>');
      p.setFrom('<EMAIL>');
      expect(p.from.email).to.equal('<EMAIL>');
    });
    it('should accept no input', function() {
      expect(function() {
        p.setFrom();
      }).not.to.throw(Error);
    });
    it('should not overwrite value with no input', function() {
      p.setFrom('<EMAIL>');
      p.setFrom();
      expect(p.from.email).to.equal('<EMAIL>');
    });
  });
});
