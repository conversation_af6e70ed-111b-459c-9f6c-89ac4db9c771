{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\nimport LandingPage from './pages/LandingPage';\nimport CheckEmail from './pages/CheckEmail';\nimport EmailVerification from './pages/EmailVerification';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport AuthFooter from './components/AuthFooter';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children\n}) => {\n  const token = localStorage.getItem('token');\n  return token ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 29\n  }, this);\n};\n_c = PrivateRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  const isAuthenticated = !!localStorage.getItem('token');\n  return isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 28\n  }, this) : children;\n};\n_c2 = PublicRoute;\nconst RedirectLegacyRoutes = () => {\n  const isAuthenticated = !!localStorage.getItem('token');\n  return isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 28\n  }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 67\n  }, this);\n};\n_c3 = RedirectLegacyRoutes;\nconst Layout = () => {\n  _s();\n  const location = useLocation();\n  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';\n  const isLandingPage = location.pathname === '/';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col min-h-screen bg-gray-900\",\n    children: [!isAuthPage && /*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n      className: \"flex-grow\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n            children: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(RedirectLegacyRoutes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/services\",\n          element: /*#__PURE__*/_jsxDEV(RedirectLegacyRoutes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/contact\",\n          element: /*#__PURE__*/_jsxDEV(RedirectLegacyRoutes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/signup\",\n          element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/check-email\",\n          element: /*#__PURE__*/_jsxDEV(CheckEmail, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/verify-email/:token\",\n          element: /*#__PURE__*/_jsxDEV(EmailVerification, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), !isAuthPage && /*#__PURE__*/_jsxDEV(Chatbot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 23\n    }, this), !isAuthPage && !isLandingPage && /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 41\n    }, this), isAuthPage && /*#__PURE__*/_jsxDEV(AuthFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 22\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c4 = Layout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n}\n_c5 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PrivateRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"RedirectLegacyRoutes\");\n$RefreshReg$(_c4, \"Layout\");\n$RefreshReg$(_c5, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "Navigate", "motion", "Home", "Signup", "<PERSON><PERSON>", "LandingPage", "CheckEmail", "EmailVerification", "<PERSON><PERSON><PERSON>", "Footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ScrollToTop", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "token", "localStorage", "getItem", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PublicRoute", "isAuthenticated", "_c2", "RedirectLegacyRoutes", "replace", "_c3", "Layout", "_s", "location", "isAuthPage", "pathname", "isLandingPage", "className", "main", "initial", "opacity", "animate", "transition", "duration", "path", "element", "_c4", "App", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\nimport LandingPage from './pages/LandingPage';\nimport CheckEmail from './pages/CheckEmail';\nimport EmailVerification from './pages/EmailVerification';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport AuthFooter from './components/AuthFooter';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\n\nconst PrivateRoute = ({ children }) => {\n  const token = localStorage.getItem('token');\n  return token ? children : <Navigate to=\"/\" />;\n};\n\nconst PublicRoute = ({ children }) => {\n  const isAuthenticated = !!localStorage.getItem('token');\n  return isAuthenticated ? <Navigate to=\"/dashboard\" /> : children;\n};\n\nconst RedirectLegacyRoutes = () => {\n  const isAuthenticated = !!localStorage.getItem('token');\n  return isAuthenticated ? <Navigate to=\"/dashboard\" replace /> : <Navigate to=\"/\" replace />;\n};\n\nconst Layout = () => {\n  const location = useLocation();\n  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';\n  const isLandingPage = location.pathname === '/';\n\n  return (\n    <div className=\"flex flex-col min-h-screen bg-gray-900\">\n      {!isAuthPage && <Navbar />}\n      <motion.main\n        className=\"flex-grow\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Routes>\n          <Route path=\"/\" element={<PublicRoute><LandingPage /></PublicRoute>} />\n          <Route path=\"/dashboard\" element={<PrivateRoute><Home /></PrivateRoute>} />\n          <Route path=\"/about\" element={<RedirectLegacyRoutes />} />\n          <Route path=\"/services\" element={<RedirectLegacyRoutes />} />\n          <Route path=\"/contact\" element={<RedirectLegacyRoutes />} />\n          <Route path=\"/signup\" element={<Signup />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/check-email\" element={<CheckEmail />} />\n          <Route path=\"/verify-email/:token\" element={<EmailVerification />} />\n        </Routes>\n      </motion.main>\n      {!isAuthPage && <Chatbot />}\n      {!isAuthPage && !isLandingPage && <Footer />}\n      {isAuthPage && <AuthFooter />}\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <ScrollToTop />\n      <Layout />\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,kBAAkB;AAChG,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,iBAAiB,MAAM,2BAA2B;;AAEzD;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,GAAGD,QAAQ,gBAAGF,OAAA,CAACd,QAAQ;IAACoB,EAAE,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;AAACC,EAAA,GAHIV,YAAY;AAKlB,MAAMW,WAAW,GAAGA,CAAC;EAAEV;AAAS,CAAC,KAAK;EACpC,MAAMW,eAAe,GAAG,CAAC,CAACT,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACvD,OAAOQ,eAAe,gBAAGb,OAAA,CAACd,QAAQ;IAACoB,EAAE,EAAC;EAAY;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,GAAGR,QAAQ;AAClE,CAAC;AAACY,GAAA,GAHIF,WAAW;AAKjB,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAMF,eAAe,GAAG,CAAC,CAACT,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACvD,OAAOQ,eAAe,gBAAGb,OAAA,CAACd,QAAQ;IAACoB,EAAE,EAAC,YAAY;IAACU,OAAO;EAAA;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGV,OAAA,CAACd,QAAQ;IAACoB,EAAE,EAAC,GAAG;IAACU,OAAO;EAAA;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7F,CAAC;AAACO,GAAA,GAHIF,oBAAoB;AAK1B,MAAMG,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,UAAU,GAAGD,QAAQ,CAACE,QAAQ,KAAK,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,KAAK,SAAS;EACpF,MAAMC,aAAa,GAAGH,QAAQ,CAACE,QAAQ,KAAK,GAAG;EAE/C,oBACEtB,OAAA;IAAKwB,SAAS,EAAC,wCAAwC;IAAAtB,QAAA,GACpD,CAACmB,UAAU,iBAAIrB,OAAA,CAACN,MAAM;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1BV,OAAA,CAACb,MAAM,CAACsC,IAAI;MACVD,SAAS,EAAC,WAAW;MACrBE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAA5B,QAAA,eAE9BF,OAAA,CAACjB,MAAM;QAAAmB,QAAA,gBACLF,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEhC,OAAA,CAACY,WAAW;YAAAV,QAAA,eAACF,OAAA,CAACT,WAAW;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEhC,OAAA,CAACC,YAAY;YAAAC,QAAA,eAACF,OAAA,CAACZ,IAAI;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3EV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEhC,OAAA,CAACe,oBAAoB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEhC,OAAA,CAACe,oBAAoB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEhC,OAAA,CAACe,oBAAoB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEhC,OAAA,CAACX,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEhC,OAAA,CAACV,KAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEhC,OAAA,CAACR,UAAU;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDV,OAAA,CAAChB,KAAK;UAAC+C,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEhC,OAAA,CAACP,iBAAiB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACb,CAACW,UAAU,iBAAIrB,OAAA,CAACH,OAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1B,CAACW,UAAU,IAAI,CAACE,aAAa,iBAAIvB,OAAA,CAACL,MAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC3CW,UAAU,iBAAIrB,OAAA,CAACJ,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1B,CAAC;AAEV,CAAC;AAACS,EAAA,CA/BID,MAAM;EAAA,QACOjC,WAAW;AAAA;AAAAgD,GAAA,GADxBf,MAAM;AAiCZ,SAASgB,GAAGA,CAAA,EAAG;EACb,oBACElC,OAAA,CAAClB,MAAM;IAAAoB,QAAA,gBACLF,OAAA,CAACF,WAAW;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfV,OAAA,CAACkB,MAAM;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAACyB,GAAA,GAPQD,GAAG;AASZ,eAAeA,GAAG;AAAC,IAAAvB,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAgB,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}