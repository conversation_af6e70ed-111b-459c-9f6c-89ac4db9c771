{"ast": null, "code": "'use strict';\n\n/**\n * Expose helpers\n */\nconst arrayToJSON = require('./array-to-json');\nconst convertKeys = require('./convert-keys');\nconst deepClone = require('./deep-clone');\nconst mergeData = require('./merge-data');\nconst splitNameEmail = require('./split-name-email');\nconst toCamelCase = require('./to-camel-case');\nconst toSnakeCase = require('./to-snake-case');\nconst wrapSubstitutions = require('./wrap-substitutions');\n\n/**\n * Export\n */\nmodule.exports = {\n  arrayToJSON,\n  convertKeys,\n  deepClone,\n  mergeData,\n  splitNameEmail,\n  toCamelCase,\n  toSnakeCase,\n  wrapSubstitutions\n};", "map": {"version": 3, "names": ["arrayToJSON", "require", "convertKeys", "deepClone", "mergeData", "splitNameEmail", "toCamelCase", "toSnakeCase", "wrapSubstitutions", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Expose helpers\n */\nconst arrayToJSON = require('./array-to-json');\nconst convertKeys = require('./convert-keys');\nconst deepClone = require('./deep-clone');\nconst mergeData = require('./merge-data');\nconst splitNameEmail = require('./split-name-email');\nconst toCamelCase = require('./to-camel-case');\nconst toSnakeCase = require('./to-snake-case');\nconst wrapSubstitutions = require('./wrap-substitutions');\n\n/**\n * Export\n */\nmodule.exports = {\n  arrayToJSON,\n  convertKeys,\n  deepClone,\n  mergeData,\n  splitNameEmail,\n  toCamelCase,\n  toSnakeCase,\n  wrapSubstitutions,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC9C,MAAMC,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMG,SAAS,GAAGH,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMI,cAAc,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AACpD,MAAMK,WAAW,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAC9C,MAAMM,WAAW,GAAGN,OAAO,CAAC,iBAAiB,CAAC;AAC9C,MAAMO,iBAAiB,GAAGP,OAAO,CAAC,sBAAsB,CAAC;;AAEzD;AACA;AACA;AACAQ,MAAM,CAACC,OAAO,GAAG;EACfV,WAAW;EACXE,WAAW;EACXC,SAAS;EACTC,SAAS;EACTC,cAAc;EACdC,WAAW;EACXC,WAAW;EACXC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}