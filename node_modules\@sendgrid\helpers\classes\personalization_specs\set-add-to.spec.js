'use strict';

/**
 * Dependencies
 */
const Personalization = require('../personalization');
const EmailAddress = require('../email-address');

/**
 * Tests
 */
describe('Personalization', function() {

  //Create new personalization before each test
  let p;
  beforeEach(function() {
    p = new Personalization();
  });

  //Add to
  describe('addTo()', function() {
    it('should add the item', function() {
      p.addTo('<EMAIL>');
      expect(p.to).to.be.an.instanceof(Array);
      expect(p.to).to.have.a.lengthOf(1);
      expect(p.to[0]).to.be.an.instanceof(EmailAddress);
      expect(p.to[0].email).to.equal('<EMAIL>');
    });
    it('should handle multiple values', function() {
      p.addTo('<EMAIL>');
      p.addTo('<EMAIL>');
      expect(p.to).to.be.an.instanceof(Array);
      expect(p.to).to.have.a.lengthOf(2);
      expect(p.to[0]).to.be.an.instanceof(<PERSON>ailAddress);
      expect(p.to[0].email).to.equal('<EMAIL>');
      expect(p.to[1]).to.be.an.instanceof(EmailAddress);
      expect(p.to[1].email).to.equal('<EMAIL>');
    });
    it('should accept no input', function() {
      expect(function() {
        p.addTo();
      }).not.to.throw(Error);
    });
    it('should not overwrite with no input', function() {
      p.addTo('<EMAIL>');
      p.addTo();
      expect(p.to).to.be.an.instanceof(Array);
      expect(p.to).to.have.a.lengthOf(1);
      expect(p.to[0]).to.be.an.instanceof(EmailAddress);
      expect(p.to[0].email).to.equal('<EMAIL>');
    });
  });
});
