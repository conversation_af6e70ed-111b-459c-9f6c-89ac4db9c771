{"ast": null, "code": "const DYNAMIC_TEMPLATE_CHAR_WARNING = `\nContent with characters ', \" or & may need to be escaped with three brackets\n{{{ content }}}\nSee https://sendgrid.com/docs/for-developers/sending-email/using-handlebars/ for more information.`;\nmodule.exports = {\n  DYNAMIC_TEMPLATE_CHAR_WARNING\n};", "map": {"version": 3, "names": ["DYNAMIC_TEMPLATE_CHAR_WARNING", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/constants/index.js"], "sourcesContent": ["const DYNAMIC_TEMPLATE_CHAR_WARNING = `\nContent with characters ', \" or & may need to be escaped with three brackets\n{{{ content }}}\nSee https://sendgrid.com/docs/for-developers/sending-email/using-handlebars/ for more information.`;\n\nmodule.exports = {\n  DYNAMIC_TEMPLATE_CHAR_WARNING,\n};\n"], "mappings": "AAAA,MAAMA,6BAA6B,GAAG;AACtC;AACA;AACA,mGAAmG;AAEnGC,MAAM,CAACC,OAAO,GAAG;EACfF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}