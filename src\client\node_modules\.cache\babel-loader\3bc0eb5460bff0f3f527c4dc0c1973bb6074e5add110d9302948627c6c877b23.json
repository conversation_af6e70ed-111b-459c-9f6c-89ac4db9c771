{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst MailService = require('./classes/mail-service');\n\n//Export singleton instance\nmodule.exports = new MailService();", "map": {"version": 3, "names": ["MailService", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/mail/src/mail.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst MailService = require('./classes/mail-service');\n\n//Export singleton instance\nmodule.exports = new MailService();\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,wBAAwB,CAAC;;AAErD;AACAC,MAAM,CAACC,OAAO,GAAG,IAAIH,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}