{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\LandingPageFooter.js\";\nimport React from 'react';\nimport { FaInstagram, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPageFooter = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-primary-dark text-white py-10\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center md:items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: blazeTradeLogo,\n              alt: \"BlazeTrade Logo\",\n              className: \"w-7 h-7 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold\",\n              children: \"BlazeTrade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm max-w-xs\",\n            children: \"Your trusted partner for Bitcoin exchange and trading services.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-3\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center justify-center md:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"hover:text-accent\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center justify-center md:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+2348163309355\",\n                className: \"hover:text-accent\",\n                children: \"+234 ************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-3\",\n            children: \"Find Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-300\",\n            children: /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center justify-center md:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Victoria Island, Lagos, Nigeria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"inline-flex items-center text-gray-300 hover:text-accent\",\n              children: [/*#__PURE__*/_jsxDEV(FaInstagram, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 21\n              }, this), \"@blaze__trade\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400\",\n          children: [\"\\xA9 \", currentYear, \" BlazeTrade. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPageFooter;\nexport default LandingPageFooter;\nvar _c;\n$RefreshReg$(_c, \"LandingPageFooter\");", "map": {"version": 3, "names": ["React", "FaInstagram", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/LandingPageFooter.js"], "sourcesContent": ["import React from 'react';\nimport { FaInstagram, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst LandingPageFooter = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-primary-dark text-white py-10\">\n      <div className=\"container-custom\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left\">\n          \n          {/* Company Info */}\n          <div className=\"flex flex-col items-center md:items-start\">\n            <div className=\"flex items-center space-x-2 mb-3\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"w-7 h-7 rounded\" />\n              <span className=\"text-2xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-400 text-sm max-w-xs\">\n              Your trusted partner for Bitcoin exchange and trading services.\n            </p>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaEnvelope className=\"mr-2\" />\n                <a href=\"mailto:<EMAIL>\" className=\"hover:text-accent\"><EMAIL></a>\n              </li>\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaPhone className=\"mr-2\" />\n                <a href=\"tel:+2348163309355\" className=\"hover:text-accent\">+234 ************</a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Location & Social */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">Find Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaMapMarkerAlt className=\"mr-2\" />\n                <span>Victoria Island, Lagos, Nigeria</span>\n              </li>\n            </ul>\n            <div className=\"mt-4\">\n                <a href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"inline-flex items-center text-gray-300 hover:text-accent\">\n                    <FaInstagram className=\"mr-2\" />\n                    @blaze__trade\n                </a>\n            </div>\n          </div>\n\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-8 pt-6 text-center\">\n          <p className=\"text-sm text-gray-400\">\n            &copy; {currentYear} BlazeTrade. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default LandingPageFooter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAEC,cAAc,QAAQ,gBAAgB;AACjF,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eAClDN,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BN,OAAA;QAAKK,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAG7EN,OAAA;UAAKK,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDN,OAAA;YAAKK,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CN,OAAA;cAAKO,GAAG,EAAET,cAAe;cAACU,GAAG,EAAC,iBAAiB;cAACH,SAAS,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9EZ,OAAA;cAAMK,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNZ,OAAA;YAAGK,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAE9C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DZ,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCN,OAAA;cAAIK,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC/DN,OAAA,CAACL,UAAU;gBAACU,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BZ,OAAA;gBAAGa,IAAI,EAAC,+BAA+B;gBAACR,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACLZ,OAAA;cAAIK,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC/DN,OAAA,CAACJ,OAAO;gBAACS,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BZ,OAAA;gBAAGa,IAAI,EAAC,oBAAoB;gBAACR,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDZ,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACrCN,OAAA;cAAIK,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC/DN,OAAA,CAACH,cAAc;gBAACQ,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCZ,OAAA;gBAAAM,QAAA,EAAM;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLZ,OAAA;YAAKK,SAAS,EAAC,MAAM;YAAAC,QAAA,eACjBN,OAAA;cAAGa,IAAI,EAAC,sDAAsD;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAACV,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACzKN,OAAA,CAACN,WAAW;gBAACW,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAEpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CAAC,eAENZ,OAAA;QAAKK,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DN,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OAC5B,EAACJ,WAAW,EAAC,mCACtB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GA7DIf,iBAAiB;AA+DvB,eAAeA,iBAAiB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}