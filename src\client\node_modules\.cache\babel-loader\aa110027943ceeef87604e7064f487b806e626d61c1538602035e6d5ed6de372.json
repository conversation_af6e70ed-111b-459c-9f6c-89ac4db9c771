{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Signup = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const [activeTab, setActiveTab] = useState('Email');\n  const onSubmit = async data => {\n    try {\n      const response = await axios.post('/api/auth/signup', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Signup failed:', error);\n    }\n  };\n  const renderForm = () => {\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-400\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...register('email', {\n            required: 'Email is required',\n            pattern: {\n              value: /\\S+@\\S+\\.\\S+/,\n              message: 'Email is invalid'\n            }\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-400\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...register('password', {\n            required: 'Password is required',\n            minLength: {\n              value: 6,\n              message: 'Password must be at least 6 characters'\n            }\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Create a password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"terms\",\n          type: \"checkbox\",\n          ...register('terms', {\n            required: 'You must agree to the terms'\n          }),\n          className: \"h-4 w-4 mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"terms\",\n          className: \"ml-3 block text-sm text-gray-400\",\n          children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/user-agreement\",\n            className: \"font-medium text-blue-400 hover:text-blue-300\",\n            children: \"User Agreement\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), ' and ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/privacy-policy\",\n            className: \"font-medium text-blue-400 hover:text-blue-300\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), errors.terms && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"-mt-4 text-sm text-red-500\",\n        children: errors.terms.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-blue-950 text-white flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:block text-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-6xl font-bold leading-tight\",\n          children: [\"Trade Crypto\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-400\",\n            children: \"Like a Pro.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://i.imgur.com/YjA0m5I.png\",\n          alt: \"Crypto Mascot\",\n          className: \"mt-8 w-2/3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold mb-4\",\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center border-b border-blue-800 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex\",\n            children: ['Email', 'Mobile'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab),\n              className: `py-2 px-4 text-lg font-semibold transition-colors duration-300 ${activeTab === tab ? 'text-white border-b-2 border-blue-500' : 'text-gray-500 hover:text-white'}`,\n              children: tab\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/institutional-signup\",\n            className: \"text-sm text-blue-400 hover:text-blue-300\",\n            children: \"Institutional account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), serverError && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",\n          children: serverError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 27\n        }, this), renderForm(), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-6 text-sm text-center text-gray-400\",\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-blue-400 hover:text-blue-300\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"JIcjob1GcCKZtNQ5moutsLYU0jY=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useState", "useForm", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Signup", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "serverError", "setServerError", "activeTab", "setActiveTab", "onSubmit", "data", "response", "post", "localStorage", "setItem", "token", "error", "msg", "console", "renderForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "pattern", "value", "message", "placeholder", "email", "<PERSON><PERSON><PERSON><PERSON>", "password", "id", "htmlFor", "to", "terms", "src", "alt", "map", "tab", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Signup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst Signup = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const [activeTab, setActiveTab] = useState('Email');\n\n  const onSubmit = async (data) => {\n    try {\n      const response = await axios.post('/api/auth/signup', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Signup failed:', error);\n    }\n  };\n\n  const renderForm = () => {\n    return (\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Email</label>\n          <input\n            type=\"email\"\n            {...register('email', { required: 'Email is required', pattern: { value: /\\S+@\\S+\\.\\S+/, message: 'Email is invalid' } })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter your email\"\n          />\n          {errors.email && <p className=\"mt-2 text-sm text-red-500\">{errors.email.message}</p>}\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n          <input\n            type=\"password\"\n            {...register('password', { required: 'Password is required', minLength: { value: 6, message: 'Password must be at least 6 characters' } })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Create a password\"\n          />\n          {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n        </div>\n        <div className=\"flex items-start\">\n          <input\n            id=\"terms\"\n            type=\"checkbox\"\n            {...register('terms', { required: 'You must agree to the terms' })}\n            className=\"h-4 w-4 mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n          />\n          <label htmlFor=\"terms\" className=\"ml-3 block text-sm text-gray-400\">\n            I agree to the{' '}\n            <Link to=\"/user-agreement\" className=\"font-medium text-blue-400 hover:text-blue-300\">User Agreement</Link>\n            {' and '}\n            <Link to=\"/privacy-policy\" className=\"font-medium text-blue-400 hover:text-blue-300\">Privacy Policy</Link>.\n          </label>\n        </div>\n        {errors.terms && <p className=\"-mt-4 text-sm text-red-500\">{errors.terms.message}</p>}\n        <div>\n          <button\n            type=\"submit\"\n            className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n          >\n            Sign Up\n          </button>\n        </div>\n      </form>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-blue-950 text-white flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n        \n        {/* Left Section */}\n        <div className=\"hidden md:block text-left\">\n            <h1 className=\"text-6xl font-bold leading-tight\">\n                Trade Crypto\n                <br />\n                <span className=\"text-blue-400\">Like a Pro.</span>\n            </h1>\n            <img src=\"https://i.imgur.com/YjA0m5I.png\" alt=\"Crypto Mascot\" className=\"mt-8 w-2/3\"/>\n        </div>\n\n        {/* Form Section */}\n        <div className=\"w-full max-w-md\">\n          <h1 className=\"text-4xl font-bold mb-4\">Sign Up</h1>\n          <div className=\"flex justify-between items-center border-b border-blue-800 mb-6\">\n            <div className=\"flex\">\n                {['Email', 'Mobile'].map(tab => (\n                <button \n                    key={tab} \n                    onClick={() => setActiveTab(tab)}\n                    className={`py-2 px-4 text-lg font-semibold transition-colors duration-300 ${activeTab === tab ? 'text-white border-b-2 border-blue-500' : 'text-gray-500 hover:text-white'}`}>\n                    {tab}\n                  </button>\n                ))}\n            </div>\n            <Link to=\"/institutional-signup\" className=\"text-sm text-blue-400 hover:text-blue-300\">Institutional account</Link>\n          </div>\n          {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n          {renderForm()}\n          <p className=\"mt-6 text-sm text-center text-gray-400\">\n            Already have an account?{' '}\n            <Link to=\"/login\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n              Log in\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGX,OAAO,CAAC,CAAC;EACnE,MAAMY,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EAEnD,MAAMkB,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,KAAK,CAACmB,IAAI,CAAC,kBAAkB,EAAEF,IAAI,CAAC;MAC3DG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACD,IAAI,CAACK,KAAK,CAAC;MAClDX,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOY,KAAK,EAAE;MACd,IAAIA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,EAAE;QAC7CX,cAAc,CAACU,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,CAAC;MACzC,CAAC,MAAM;QACLX,cAAc,CAAC,iDAAiD,CAAC;MACnE;MACAY,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,oBACEtB,OAAA;MAAMY,QAAQ,EAAER,YAAY,CAACQ,QAAQ,CAAE;MAACW,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3DxB,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAOuB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxE5B,OAAA;UACE6B,IAAI,EAAC,OAAO;UAAA,GACR1B,QAAQ,CAAC,OAAO,EAAE;YAAE2B,QAAQ,EAAE,mBAAmB;YAAEC,OAAO,EAAE;cAAEC,KAAK,EAAE,cAAc;cAAEC,OAAO,EAAE;YAAmB;UAAE,CAAC,CAAC;UACzHV,SAAS,EAAC,oIAAoI;UAC9IW,WAAW,EAAC;QAAkB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDtB,MAAM,CAAC6B,KAAK,iBAAInC,OAAA;UAAGuB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAElB,MAAM,CAAC6B,KAAK,CAACF;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eACN5B,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAOuB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3E5B,OAAA;UACE6B,IAAI,EAAC,UAAU;UAAA,GACX1B,QAAQ,CAAC,UAAU,EAAE;YAAE2B,QAAQ,EAAE,sBAAsB;YAAEM,SAAS,EAAE;cAAEJ,KAAK,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAyC;UAAE,CAAC,CAAC;UAC1IV,SAAS,EAAC,oIAAoI;UAC9IW,WAAW,EAAC;QAAmB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,EACDtB,MAAM,CAAC+B,QAAQ,iBAAIrC,OAAA;UAAGuB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAElB,MAAM,CAAC+B,QAAQ,CAACJ;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACN5B,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BxB,OAAA;UACEsC,EAAE,EAAC,OAAO;UACVT,IAAI,EAAC,UAAU;UAAA,GACX1B,QAAQ,CAAC,OAAO,EAAE;YAAE2B,QAAQ,EAAE;UAA8B,CAAC,CAAC;UAClEP,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACF5B,OAAA;UAAOuC,OAAO,EAAC,OAAO;UAAChB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAAC,gBACpD,EAAC,GAAG,eAClBxB,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,iBAAiB;YAACjB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACzG,OAAO,eACR5B,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,iBAAiB;YAACjB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAC5G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EACLtB,MAAM,CAACmC,KAAK,iBAAIzC,OAAA;QAAGuB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAElB,MAAM,CAACmC,KAAK,CAACR;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrF5B,OAAA;QAAAwB,QAAA,eACExB,OAAA;UACE6B,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,2MAA2M;UAAAC,QAAA,EACtN;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;EAED,oBACE5B,OAAA;IAAKuB,SAAS,EAAC,0EAA0E;IAAAC,QAAA,eACvFxB,OAAA;MAAKuB,SAAS,EAAC,kEAAkE;MAAAC,QAAA,gBAG/ExB,OAAA;QAAKuB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACtCxB,OAAA;UAAIuB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAAC,cAE7C,eAAAxB,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5B,OAAA;YAAMuB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACL5B,OAAA;UAAK0C,GAAG,EAAC,iCAAiC;UAACC,GAAG,EAAC,eAAe;UAACpB,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eAGN5B,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAIuB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD5B,OAAA;UAAKuB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC9ExB,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAChB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACoB,GAAG,CAACC,GAAG,iBAC5B7C,OAAA;cAEI8C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAACkC,GAAG,CAAE;cACjCtB,SAAS,EAAE,kEAAkEb,SAAS,KAAKmC,GAAG,GAAG,uCAAuC,GAAG,gCAAgC,EAAG;cAAArB,QAAA,EAC7KqB;YAAG,GAHCA,GAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIF,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN5B,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,uBAAuB;YAACjB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChH,CAAC,EACLpB,WAAW,iBAAIR,OAAA;UAAGuB,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAAEhB;QAAW;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClHN,UAAU,CAAC,CAAC,eACbtB,OAAA;UAAGuB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,0BAC5B,EAAC,GAAG,eAC5BxB,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,QAAQ;YAACjB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAjHID,MAAM;EAAA,QACgDN,OAAO,EAChDG,WAAW;AAAA;AAAAiD,EAAA,GAFxB9C,MAAM;AAmHZ,eAAeA,MAAM;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}