{"ast": null, "code": "'use strict';\n\n/**\n * Load support assets\n */\nconst classes = require('./classes');\nconst helpers = require('./helpers');\n\n/**\n * Export\n */\nmodule.exports = {\n  classes,\n  helpers\n};", "map": {"version": 3, "names": ["classes", "require", "helpers", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Load support assets\n */\nconst classes = require('./classes');\nconst helpers = require('./helpers');\n\n/**\n * Export\n */\nmodule.exports = {classes, helpers};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;;AAEpC;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG;EAACJ,OAAO;EAAEE;AAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}