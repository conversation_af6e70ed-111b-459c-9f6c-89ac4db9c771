{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\LandingPageFooter.js\";\nimport React from 'react';\nimport { FaInstagram } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SocialIcon = ({\n  icon,\n  href\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"a\", {\n    href: href,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\",\n    children: icon\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = SocialIcon;\nconst LandingPageFooter = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-transparent py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom text-center text-xs text-gray-500 flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\xA9 \", currentYear, \" BlazeTrade. All Rights Reserved.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: /*#__PURE__*/_jsxDEV(SocialIcon, {\n          icon: /*#__PURE__*/_jsxDEV(FaInstagram, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 29\n          }, this),\n          href: \"https://www.instagram.com/blazetrade\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c2 = LandingPageFooter;\nexport default LandingPageFooter;\nvar _c, _c2;\n$RefreshReg$(_c, \"SocialIcon\");\n$RefreshReg$(_c2, \"LandingPageFooter\");", "map": {"version": 3, "names": ["React", "FaInstagram", "jsxDEV", "_jsxDEV", "SocialIcon", "icon", "href", "target", "rel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "currentYear", "Date", "getFullYear", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/LandingPageFooter.js"], "sourcesContent": ["import React from 'react';\nimport { FaInstagram } from 'react-icons/fa';\n\nconst SocialIcon = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nconst LandingPageFooter = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-transparent py-4\">\n      <div className=\"container-custom text-center text-xs text-gray-500 flex justify-between items-center\">\n        <p>&copy; {currentYear} BlazeTrade. All Rights Reserved.</p>\n        <div className=\"flex space-x-4\">\n          <SocialIcon icon={<FaInstagram />} href=\"https://www.instagram.com/blazetrade\" />\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default LandingPageFooter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EACrC,oBACEH,OAAA;IACEG,IAAI,EAAEA,IAAK;IACXC,MAAM,EAAC,QAAQ;IACfC,GAAG,EAAC,qBAAqB;IACzBC,SAAS,EAAC,kHAAkH;IAAAC,QAAA,EAE3HL;EAAI;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAER,CAAC;AAACC,EAAA,GAXIX,UAAU;AAahB,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEhB,OAAA;IAAQM,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eACrCP,OAAA;MAAKM,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBACnGP,OAAA;QAAAO,QAAA,GAAG,OAAO,EAACO,WAAW,EAAC,mCAAiC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5DX,OAAA;QAAKM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BP,OAAA,CAACC,UAAU;UAACC,IAAI,eAAEF,OAAA,CAACF,WAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACR,IAAI,EAAC;QAAsC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACM,GAAA,GAbIJ,iBAAiB;AAevB,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}