{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Signup = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const onSubmit = async data => {\n    try {\n      const response = await axios.post('/api/auth/signup', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/');\n    } catch (error) {\n      console.error('Signup failed:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md p-8 space-y-6 bg-gray-800 rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: blazeTradeLogo,\n          alt: \"BlazeTrade Logo\",\n          className: \"w-32 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold\",\n          children: \"Create an Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-400\",\n          children: \"To start trading your cryptocurrencies with Blaze Trade, create an account to get started.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            ...register('fullName', {\n              required: 'Full name is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), errors.fullName && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.fullName.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            ...register('email', {\n              required: 'Email is required',\n              pattern: {\n                value: /\\S+@\\S+\\.\\S+/,\n                message: 'Email is invalid'\n              }\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.email.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            ...register('username', {\n              required: 'Username is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.username.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            ...register('password', {\n              required: 'Password is required',\n              minLength: {\n                value: 6,\n                message: 'Password must be at least 6 characters'\n              }\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.password.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n            children: \"Sign up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-center text-gray-400\",\n        children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n          children: \"Log in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"hvZci8bfHEyrOM2r7yZN4BE/YxQ=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useForm", "axios", "Link", "useNavigate", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "Signup", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "onSubmit", "data", "response", "post", "localStorage", "setItem", "token", "error", "console", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "fullName", "message", "pattern", "value", "email", "username", "<PERSON><PERSON><PERSON><PERSON>", "password", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Signup.js"], "sourcesContent": ["import React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst Signup = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n\n  const onSubmit = async (data) => {\n    try {\n      const response = await axios.post('/api/auth/signup', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/');\n    } catch (error) {\n      console.error('Signup failed:', error);\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"w-full max-w-md p-8 space-y-6 bg-gray-800 rounded-lg shadow-md\">\n        <div className=\"text-center\">\n          <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"w-32 mx-auto mb-4\" />\n          <h2 className=\"text-3xl font-bold\">Create an Account</h2>\n          <p className=\"mt-2 text-sm text-gray-400\">To start trading your cryptocurrencies with Blaze Trade, create an account to get started.</p>\n        </div>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Full Name</label>\n            <input\n              type=\"text\"\n              {...register('fullName', { required: 'Full name is required' })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.fullName && <p className=\"mt-2 text-sm text-red-500\">{errors.fullName.message}</p>}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Email</label>\n            <input\n              type=\"email\"\n              {...register('email', { required: 'Email is required', pattern: { value: /\\S+@\\S+\\.\\S+/, message: 'Email is invalid' } })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.email && <p className=\"mt-2 text-sm text-red-500\">{errors.email.message}</p>}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Username</label>\n            <input\n              type=\"text\"\n              {...register('username', { required: 'Username is required' })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Password</label>\n            <input\n              type=\"password\"\n              {...register('password', { required: 'Password is required', minLength: { value: 6, message: 'Password must be at least 6 characters' } })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n          </div>\n          <div>\n            <button\n              type=\"submit\"\n              className=\"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n            >\n              Sign up\n            </button>\n          </div>\n        </form>\n        <p className=\"text-sm text-center text-gray-400\">\n          Already have an account?{' '}\n          <Link to=\"/login\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n            Log in\n          </Link>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnE,MAAMa,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,kBAAkB,EAAEF,IAAI,CAAC;MAC3DG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACD,IAAI,CAACK,KAAK,CAAC;MAClDP,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,oBACEf,OAAA;IAAKiB,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFlB,OAAA;MAAKiB,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7ElB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UAAKmB,GAAG,EAAErB,cAAe;UAACsB,GAAG,EAAC,iBAAiB;UAACH,SAAS,EAAC;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChFxB,OAAA;UAAIiB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDxB,OAAA;UAAGiB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA0F;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrI,CAAC,eACNxB,OAAA;QAAMQ,QAAQ,EAAEJ,YAAY,CAACI,QAAQ,CAAE;QAACS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC3DlB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5ExB,OAAA;YACEyB,IAAI,EAAC,MAAM;YAAA,GACPtB,QAAQ,CAAC,UAAU,EAAE;cAAEuB,QAAQ,EAAE;YAAwB,CAAC,CAAC;YAC/DT,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAACqB,QAAQ,iBAAI3B,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAACqB,QAAQ,CAACC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxExB,OAAA;YACEyB,IAAI,EAAC,OAAO;YAAA,GACRtB,QAAQ,CAAC,OAAO,EAAE;cAAEuB,QAAQ,EAAE,mBAAmB;cAAEG,OAAO,EAAE;gBAAEC,KAAK,EAAE,cAAc;gBAAEF,OAAO,EAAE;cAAmB;YAAE,CAAC,CAAC;YACzHX,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAACyB,KAAK,iBAAI/B,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAACyB,KAAK,CAACH;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ExB,OAAA;YACEyB,IAAI,EAAC,MAAM;YAAA,GACPtB,QAAQ,CAAC,UAAU,EAAE;cAAEuB,QAAQ,EAAE;YAAuB,CAAC,CAAC;YAC9DT,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAAC0B,QAAQ,iBAAIhC,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAAC0B,QAAQ,CAACJ;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ExB,OAAA;YACEyB,IAAI,EAAC,UAAU;YAAA,GACXtB,QAAQ,CAAC,UAAU,EAAE;cAAEuB,QAAQ,EAAE,sBAAsB;cAAEO,SAAS,EAAE;gBAAEH,KAAK,EAAE,CAAC;gBAAEF,OAAO,EAAE;cAAyC;YAAE,CAAC,CAAC;YAC1IX,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAAC4B,QAAQ,iBAAIlC,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAAC4B,QAAQ,CAACN;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,eACElB,OAAA;YACEyB,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,gKAAgK;YAAAC,QAAA,EAC3K;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACPxB,OAAA;QAAGiB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,GAAC,0BACvB,EAAC,GAAG,eAC5BlB,OAAA,CAACJ,IAAI;UAACuC,EAAE,EAAC,QAAQ;UAAClB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAEhF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA7EID,MAAM;EAAA,QACgDP,OAAO,EAChDG,WAAW;AAAA;AAAAuC,EAAA,GAFxBnC,MAAM;AA+EZ,eAAeA,MAAM;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}