{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport heroImage from '../assets/images/bitcoin-hero.jpg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleJoin = e => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n  const marketData = [{\n    name: 'DOGE Index',\n    price: '$0.25173',\n    change: '+0.01197',\n    percent: '+4.75%'\n  }, {\n    name: 'SUSHI Index',\n    price: '$0.965',\n    change: '+0.036',\n    percent: '+3.77%'\n  }, {\n    name: 'MASK Index',\n    price: '$1.476',\n    change: '+0.028',\n    percent: '+1.88%'\n  }, {\n    name: 'UNI Index',\n    price: '$10.258',\n    change: '+0.057',\n    percent: '+0.55%'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-blue-950 text-white min-h-screen font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container-custom pt-32 pb-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"grid md:grid-cols-2 gap-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-left\",\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-6xl md:text-7xl font-bold leading-tight\",\n            children: [\"Trade Crypto\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400\",\n              children: \"Like a Pro.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-lg text-gray-300\",\n            children: \"Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleJoin,\n            className: \"mt-8 flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              placeholder: \"Email/Phone Number\",\n              className: \"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\",\n              children: [\"Join Us\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"hidden md:block\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: heroImage,\n            alt: \"BlazeTrade Services\",\n            className: \"rounded-2xl shadow-2xl shadow-blue-500/20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-24\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n          children: marketData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900/50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400\",\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold mt-1\",\n              children: item.price\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-center mt-1 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400 mr-2\",\n                children: item.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: [\"(\", item.percent, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 25\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-24 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold\",\n          children: \"Leading the Expansion of Altcoin Markets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-lg text-blue-200\",\n          children: \"Everything you need for a seamless trading experience.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8 mt-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Secure Wallet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"State-of-the-art security for your digital assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Advanced Charting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Powerful tools and indicators to inform your trades.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Real-time Pricing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Standardised, real-time pricing across all markets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-24\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Support New Listings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Be the first to trade promising new assets. Let's get them listed on BlazeTrade!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              className: \"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Exclusive Referral Bonuses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-transparent py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-custom text-center text-xs text-gray-500\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 BlazeTrade. All Rights Reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "motion", "heroImage", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "navigate", "handleJoin", "e", "preventDefault", "marketData", "name", "price", "change", "percent", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "scale", "delay", "src", "alt", "map", "item", "index", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/LandingPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport heroImage from '../assets/images/bitcoin-hero.jpg';\n\nconst LandingPage = () => {\n  const navigate = useNavigate();\n\n  const handleJoin = (e) => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n\n  const marketData = [\n    { name: 'DOGE Index', price: '$0.25173', change: '+0.01197', percent: '+4.75%' },\n    { name: 'SUSHI Index', price: '$0.965', change: '+0.036', percent: '+3.77%' },\n    { name: 'MASK Index', price: '$1.476', change: '+0.028', percent: '+1.88%' },\n    { name: 'UNI Index', price: '$10.258', change: '+0.057', percent: '+0.55%' },\n  ];\n\n  return (\n    <div className=\"bg-blue-950 text-white min-h-screen font-sans\">\n      <main className=\"container-custom pt-32 pb-16\">\n        {/* Hero Section */}\n        <section className=\"grid md:grid-cols-2 gap-12 items-center\">\n          <motion.div\n            className=\"text-left\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-6xl md:text-7xl font-bold leading-tight\">\n              Trade Crypto\n              <br />\n              <span className=\"text-blue-400\">Like a Pro.</span>\n            </h1>\n            <p className=\"mt-6 text-lg text-gray-300\">\n              Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\n            </p>\n\n            <form onSubmit={handleJoin} className=\"mt-8 flex flex-col sm:flex-row gap-4\">\n              <input\n                type=\"email\"\n                placeholder=\"Email/Phone Number\"\n                className=\"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"\n              />\n              <button type=\"submit\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\">\n                Join Us\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n\n            </form>\n          </motion.div>\n          <motion.div\n            className=\"hidden md:block\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <img src={heroImage} alt=\"BlazeTrade Services\" className=\"rounded-2xl shadow-2xl shadow-blue-500/20\" />\n          </motion.div>\n        </section>\n\n        {/* Market Ticker Section */}\n        <section className=\"mt-24\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n                {marketData.map((item, index) => (\n                    <div key={index} className=\"bg-blue-900/50 p-4 rounded-lg\">\n                        <p className=\"text-sm text-gray-400\">{item.name}</p>\n                        <p className=\"text-2xl font-bold mt-1\">{item.price}</p>\n                        <div className=\"flex justify-center items-center mt-1 text-sm\">\n                            <span className=\"text-green-400 mr-2\">{item.change}</span>\n                            <span className=\"text-gray-400\">({item.percent})</span>\n                        </div>\n                    </div>\n                ))}\n            </div>\n        </section>\n\n        {/* Services Section */}\n        <section className=\"mt-24 text-center\">\n          <h2 className=\"text-4xl font-bold\">Leading the Expansion of Altcoin Markets</h2>\n          <p className=\"mt-4 text-lg text-blue-200\">Everything you need for a seamless trading experience.</p>\n          <div className=\"grid md:grid-cols-3 gap-8 mt-12\">\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Secure Wallet</h3>\n              <p className=\"mt-2 text-blue-200\">State-of-the-art security for your digital assets.</p>\n            </div>\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Advanced Charting</h3>\n              <p className=\"mt-2 text-blue-200\">Powerful tools and indicators to inform your trades.</p>\n            </div>\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Real-time Pricing</h3>\n              <p className=\"mt-2 text-blue-200\">Standardised, real-time pricing across all markets.</p>\n            </div>\n          </div>\n        </section>\n\n        {/* Promo Cards Section */}\n        <section className=\"mt-24\">\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Support New Listings</h3>\n              <p className=\"mt-2 text-blue-200\">Be the first to trade promising new assets. Let's get them listed on BlazeTrade!</p>\n              <Link to=\"/services\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Exclusive Referral Bonuses</h3>\n              <p className=\"mt-2 text-blue-200\">Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.</p>\n              <Link to=\"/signup\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n          </div>\n        </section>\n      </main>\n      <footer className=\"bg-transparent py-4\">\n        <div className=\"container-custom text-center text-xs text-gray-500\">\n          <p>&copy; 2024 BlazeTrade. All Rights Reserved.</p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,SAAS,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMI,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,UAAU;IAAEC,MAAM,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAS,CAAC,EAChF;IAAEH,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,QAAQ;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,EAC7E;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,QAAQ;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,EAC5E;IAAEH,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,SAAS;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,CAC7E;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAC5Db,OAAA;MAAMY,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE5Cb,OAAA;QAASY,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAC1Db,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTF,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9Bb,OAAA;YAAIY,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,cAE3D,eAAAb,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxB,OAAA;cAAMY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACLxB,OAAA;YAAGY,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJxB,OAAA;YAAMyB,QAAQ,EAAErB,UAAW;YAACQ,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAC1Eb,OAAA;cACE0B,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,oBAAoB;cAChCf,SAAS,EAAC;YAAkI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC,eACFxB,OAAA;cAAQ0B,IAAI,EAAC,QAAQ;cAACd,SAAS,EAAC,wIAAwI;cAAAC,QAAA,GAAC,SAEvK,eAAAb,OAAA;gBAAK4B,KAAK,EAAC,4BAA4B;gBAAChB,SAAS,EAAC,SAAS;gBAACiB,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAjB,QAAA,eACjGb,OAAA;kBAAM+B,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,0IAA0I;kBAACC,QAAQ,EAAC;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACbxB,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTF,SAAS,EAAC,iBAAiB;UAC3BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEkB,KAAK,EAAE;UAAI,CAAE;UACpChB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEkB,KAAK,EAAE;UAAE,CAAE;UAClCf,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEe,KAAK,EAAE;UAAI,CAAE;UAAAtB,QAAA,eAE1Cb,OAAA;YAAKoC,GAAG,EAAEtC,SAAU;YAACuC,GAAG,EAAC,qBAAqB;YAACzB,SAAS,EAAC;UAA2C;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGVxB,OAAA;QAASY,SAAS,EAAC,OAAO;QAAAC,QAAA,eACtBb,OAAA;UAAKY,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC7DN,UAAU,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBxC,OAAA;YAAiBY,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBACtDb,OAAA;cAAGY,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE0B,IAAI,CAAC/B;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDxB,OAAA;cAAGY,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE0B,IAAI,CAAC9B;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDxB,OAAA;cAAKY,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC1Db,OAAA;gBAAMY,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAE0B,IAAI,CAAC7B;cAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1DxB,OAAA;gBAAMY,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,GAAC,EAAC0B,IAAI,CAAC5B,OAAO,EAAC,GAAC;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GANAgB,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVxB,OAAA;QAASY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACpCb,OAAA;UAAIY,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAwC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFxB,OAAA;UAAGY,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAsD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpGxB,OAAA;UAAKY,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9Cb,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHb,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDxB,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAkD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACNxB,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHb,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDxB,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAoD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACNxB,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHb,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDxB,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAmD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVxB,OAAA;QAASY,SAAS,EAAC,OAAO;QAAAC,QAAA,eACxBb,OAAA;UAAKY,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCb,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHb,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxB,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAgF;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtHxB,OAAA,CAACL,IAAI;cAAC8C,EAAE,EAAC,WAAW;cAAC7B,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAAC;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eACNxB,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHb,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAA0B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClExB,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAqG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3IxB,OAAA,CAACL,IAAI;cAAC8C,EAAE,EAAC,SAAS;cAAC7B,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAAC;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACPxB,OAAA;MAAQY,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACrCb,OAAA;QAAKY,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjEb,OAAA;UAAAa,QAAA,EAAG;QAA4C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtB,EAAA,CAvHID,WAAW;EAAA,QACEL,WAAW;AAAA;AAAA8C,EAAA,GADxBzC,WAAW;AAyHjB,eAAeA,WAAW;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}