#### Personalization helper specs

- setSubject() - set-subject.spec.js
- setSendAt()  - set-send-at.spec.js
- setTo() - set-to.spec.js
- setFrom() - set-from.spec.js
- addTo() - add-to.spec.js
- setCc() - set-cc.spec.js
- addCc() - add-cc.spec.js
- setBcc() - set-bcc.spec.js
- addBcc() - add-bcc.spec.js
- setHeaders() - set-headers.spec.js
- addHeader() - add-headers.spec.js
- setCustomArgs() - set-custom-args.spec.js
- addCustomArg() - add-custom-args.spec.js
- setSubstitutions() - set-substitutions.spec.js
- addSubstitution() - add-substitutions.spec.js
- reverseMergeSubstitutions() - reverse-merge-substitutions.spec.js
- setSubstitutionWrappers() - set-substitutions-wrappers.spec.js
- deepMergeDynamicTemplateData() - reverse-merge-dynamic_template_data.spec.js
- toJSON() - to-json.spec.js
- fromData() - from-data.spec.js
- #527 - 527-camel-case-headers.spec.js