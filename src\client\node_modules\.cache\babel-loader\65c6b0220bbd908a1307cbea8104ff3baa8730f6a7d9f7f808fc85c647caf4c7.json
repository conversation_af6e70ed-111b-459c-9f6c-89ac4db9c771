{"ast": null, "code": "'use strict';\n\n/**\n * Response error class\n */\nclass ResponseError extends Error {\n  /**\n   * Constructor\n   */\n  constructor(response) {\n    //Super\n    super();\n\n    //Extract data from response\n    const {\n      headers,\n      status,\n      statusText,\n      data\n    } = response;\n\n    //Set data\n    this.code = status;\n    this.message = statusText;\n    this.response = {\n      headers,\n      body: data\n    };\n\n    //Capture stack trace\n    if (!this.stack) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n\n    //Clean up stack trace\n    const regex = new RegExp(process.cwd() + '/', 'gi');\n    this.stack = this.stack.replace(regex, '');\n  }\n\n  /**\n   * Convert to string\n   */\n  toString() {\n    const {\n      body\n    } = this.response;\n    let err = `${this.message} (${this.code})`;\n    if (body && Array.isArray(body.errors)) {\n      body.errors.forEach(error => {\n        const message = error.message;\n        const field = error.field;\n        const help = error.help;\n        err += `\\n  ${message}\\n    ${field}\\n    ${help}`;\n      });\n    }\n    return err;\n  }\n\n  /**\n   * Convert to simple object for JSON responses\n   */\n  toJSON() {\n    const {\n      message,\n      code,\n      response\n    } = this;\n    return {\n      message,\n      code,\n      response\n    };\n  }\n}\n\n//Export\nmodule.exports = ResponseError;", "map": {"version": 3, "names": ["ResponseError", "Error", "constructor", "response", "headers", "status", "statusText", "data", "code", "message", "body", "stack", "captureStackTrace", "regex", "RegExp", "process", "cwd", "replace", "toString", "err", "Array", "isArray", "errors", "for<PERSON>ach", "error", "field", "help", "toJSON", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/response-error.js"], "sourcesContent": ["'use strict';\n\n/**\n * Response error class\n */\nclass ResponseError extends Error {\n\n  /**\n   * Constructor\n   */\n  constructor(response) {\n\n    //Super\n    super();\n\n    //Extract data from response\n    const { headers, status, statusText, data } = response;\n\n    //Set data\n    this.code = status;\n    this.message = statusText;\n    this.response = { headers, body: data };\n\n    //Capture stack trace\n    if (!this.stack) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n\n    //Clean up stack trace\n    const regex = new RegExp(process.cwd() + '/', 'gi');\n    this.stack = this.stack.replace(regex, '');\n  }\n\n  /**\n   * Convert to string\n   */\n  toString() {\n    const { body } = this.response;\n    let err = `${this.message} (${this.code})`;\n    if (body && Array.isArray(body.errors)) {\n      body.errors.forEach(error => {\n        const message = error.message;\n        const field = error.field;\n        const help = error.help;\n        err += `\\n  ${message}\\n    ${field}\\n    ${help}`;\n      });\n    }\n    return err;\n  }\n\n  /**\n   * Convert to simple object for JSON responses\n   */\n  toJSON() {\n    const { message, code, response } = this;\n    return { message, code, response };\n  }\n}\n\n//Export\nmodule.exports = ResponseError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,aAAa,SAASC,KAAK,CAAC;EAEhC;AACF;AACA;EACEC,WAAWA,CAACC,QAAQ,EAAE;IAEpB;IACA,KAAK,CAAC,CAAC;;IAEP;IACA,MAAM;MAAEC,OAAO;MAAEC,MAAM;MAAEC,UAAU;MAAEC;IAAK,CAAC,GAAGJ,QAAQ;;IAEtD;IACA,IAAI,CAACK,IAAI,GAAGH,MAAM;IAClB,IAAI,CAACI,OAAO,GAAGH,UAAU;IACzB,IAAI,CAACH,QAAQ,GAAG;MAAEC,OAAO;MAAEM,IAAI,EAAEH;IAAK,CAAC;;IAEvC;IACA,IAAI,CAAC,IAAI,CAACI,KAAK,EAAE;MACfV,KAAK,CAACW,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACV,WAAW,CAAC;IACjD;;IAEA;IACA,MAAMW,KAAK,GAAG,IAAIC,MAAM,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;IACnD,IAAI,CAACL,KAAK,GAAG,IAAI,CAACA,KAAK,CAACM,OAAO,CAACJ,KAAK,EAAE,EAAE,CAAC;EAC5C;;EAEA;AACF;AACA;EACEK,QAAQA,CAAA,EAAG;IACT,MAAM;MAAER;IAAK,CAAC,GAAG,IAAI,CAACP,QAAQ;IAC9B,IAAIgB,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,KAAK,IAAI,CAACD,IAAI,GAAG;IAC1C,IAAIE,IAAI,IAAIU,KAAK,CAACC,OAAO,CAACX,IAAI,CAACY,MAAM,CAAC,EAAE;MACtCZ,IAAI,CAACY,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;QAC3B,MAAMf,OAAO,GAAGe,KAAK,CAACf,OAAO;QAC7B,MAAMgB,KAAK,GAAGD,KAAK,CAACC,KAAK;QACzB,MAAMC,IAAI,GAAGF,KAAK,CAACE,IAAI;QACvBP,GAAG,IAAI,OAAOV,OAAO,SAASgB,KAAK,SAASC,IAAI,EAAE;MACpD,CAAC,CAAC;IACJ;IACA,OAAOP,GAAG;EACZ;;EAEA;AACF;AACA;EACEQ,MAAMA,CAAA,EAAG;IACP,MAAM;MAAElB,OAAO;MAAED,IAAI;MAAEL;IAAS,CAAC,GAAG,IAAI;IACxC,OAAO;MAAEM,OAAO;MAAED,IAAI;MAAEL;IAAS,CAAC;EACpC;AACF;;AAEA;AACAyB,MAAM,CAACC,OAAO,GAAG7B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}