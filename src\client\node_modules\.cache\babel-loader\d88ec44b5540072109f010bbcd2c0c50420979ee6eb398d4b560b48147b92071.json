{"ast": null, "code": "'use strict';\n\nconst mailer = require('./src/mail');\nconst MailService = require('./src/classes/mail-service');\nmodule.exports = mailer;\nmodule.exports.MailService = MailService;", "map": {"version": 3, "names": ["mailer", "require", "MailService", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/mail/index.js"], "sourcesContent": ["'use strict';\n\nconst mailer = require('./src/mail');\nconst MailService = require('./src/classes/mail-service');\n\nmodule.exports = mailer;\nmodule.exports.MailService = MailService;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,MAAM,GAAGC,OAAO,CAAC,YAAY,CAAC;AACpC,MAAMC,WAAW,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAEzDE,MAAM,CAACC,OAAO,GAAGJ,MAAM;AACvBG,MAAM,CAACC,OAAO,CAACF,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}