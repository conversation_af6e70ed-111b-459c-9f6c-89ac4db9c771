{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\EmailVerification.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmailVerification = () => {\n  _s();\n  const {\n    token\n  } = useParams();\n  const [verificationStatus, setVerificationStatus] = useState('verifying');\n  const [message, setMessage] = useState('Verifying your email address...');\n  useEffect(() => {\n    const verifyEmail = async () => {\n      try {\n        const res = await axios.get(`/api/auth/verify-email/${token}`);\n        setVerificationStatus('success');\n        setMessage(res.data.msg);\n      } catch (err) {\n        var _err$response, _err$response$data;\n        setVerificationStatus('error');\n        setMessage(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.msg) || 'An error occurred during verification.');\n      }\n    };\n    if (token) {\n      verifyEmail();\n    }\n  }, [token]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",\n      children: [verificationStatus === 'verifying' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse text-blue-500 text-4xl mb-4\",\n        children: \"...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this), verificationStatus === 'success' && /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n        className: \"text-6xl text-green-500 mx-auto mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this), verificationStatus === 'error' && /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n        className: \"text-6xl text-red-500 mx-auto mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-4\",\n        children: verificationStatus === 'success' ? 'Verification Successful' : 'Verification Failed'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-300 mb-8\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\",\n        children: \"Proceed to Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailVerification, \"0Ifx5a8a3lj4C3kjRtX4f+Q9V7A=\", false, function () {\n  return [useParams];\n});\n_c = EmailVerification;\nexport default EmailVerification;\nvar _c;\n$RefreshReg$(_c, \"EmailVerification\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Link", "axios", "FaCheckCircle", "FaTimesCircle", "jsxDEV", "_jsxDEV", "EmailVerification", "_s", "token", "verificationStatus", "setVerificationStatus", "message", "setMessage", "verifyEmail", "res", "get", "data", "msg", "err", "_err$response", "_err$response$data", "response", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/EmailVerification.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';\n\nconst EmailVerification = () => {\n  const { token } = useParams();\n  const [verificationStatus, setVerificationStatus] = useState('verifying');\n  const [message, setMessage] = useState('Verifying your email address...');\n\n  useEffect(() => {\n    const verifyEmail = async () => {\n      try {\n        const res = await axios.get(`/api/auth/verify-email/${token}`);\n        setVerificationStatus('success');\n        setMessage(res.data.msg);\n      } catch (err) {\n        setVerificationStatus('error');\n        setMessage(err.response?.data?.msg || 'An error occurred during verification.');\n      }\n    };\n\n    if (token) {\n      verifyEmail();\n    }\n  }, [token]);\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n        {verificationStatus === 'verifying' && (\n          <div className=\"animate-pulse text-blue-500 text-4xl mb-4\">...</div>\n        )}\n        {verificationStatus === 'success' && (\n          <FaCheckCircle className=\"text-6xl text-green-500 mx-auto mb-6\" />\n        )}\n        {verificationStatus === 'error' && (\n          <FaTimesCircle className=\"text-6xl text-red-500 mx-auto mb-6\" />\n        )}\n        <h1 className=\"text-2xl font-bold mb-4\">\n          {verificationStatus === 'success' ? 'Verification Successful' : 'Verification Failed'}\n        </h1>\n        <p className=\"text-gray-300 mb-8\">{message}</p>\n        <Link to=\"/login\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\">\n          Proceed to Login\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default EmailVerification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAM,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC7B,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGZ,QAAQ,CAAC,WAAW,CAAC;EACzE,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,iCAAiC,CAAC;EAEzED,SAAS,CAAC,MAAM;IACd,MAAMgB,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,GAAG,GAAG,MAAMb,KAAK,CAACc,GAAG,CAAC,0BAA0BP,KAAK,EAAE,CAAC;QAC9DE,qBAAqB,CAAC,SAAS,CAAC;QAChCE,UAAU,CAACE,GAAG,CAACE,IAAI,CAACC,GAAG,CAAC;MAC1B,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QACZV,qBAAqB,CAAC,OAAO,CAAC;QAC9BE,UAAU,CAAC,EAAAO,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBH,GAAG,KAAI,wCAAwC,CAAC;MACjF;IACF,CAAC;IAED,IAAIT,KAAK,EAAE;MACTK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;EAEX,oBACEH,OAAA;IAAKiB,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFlB,OAAA;MAAKiB,SAAS,EAAC,kEAAkE;MAAAC,QAAA,GAC9Ed,kBAAkB,KAAK,WAAW,iBACjCJ,OAAA;QAAKiB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACpE,EACAlB,kBAAkB,KAAK,SAAS,iBAC/BJ,OAAA,CAACH,aAAa;QAACoB,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAClE,EACAlB,kBAAkB,KAAK,OAAO,iBAC7BJ,OAAA,CAACF,aAAa;QAACmB,SAAS,EAAC;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAChE,eACDtB,OAAA;QAAIiB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACpCd,kBAAkB,KAAK,SAAS,GAAG,yBAAyB,GAAG;MAAqB;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,eACLtB,OAAA;QAAGiB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEZ;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CtB,OAAA,CAACL,IAAI;QAAC4B,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,iGAAiG;QAAAC,QAAA,EAAC;MAE9H;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CA5CID,iBAAiB;EAAA,QACHP,SAAS;AAAA;AAAA8B,EAAA,GADvBvB,iBAAiB;AA8CvB,eAAeA,iBAAiB;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}