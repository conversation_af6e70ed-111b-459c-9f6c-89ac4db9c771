<IfModule mod_rewrite.c>
  RewriteEngine On

  # Rule to proxy API requests to the Node.js backend
  RewriteRule ^api/(.*)$ http://127.0.0.1:%{ENV:PORT}/api/$1 [P,L]

  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  # Exclude API requests from the frontend routing rules
  RewriteCond %{REQUEST_URI} !^/api/

  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-l
  RewriteRule . /index.html [L]
</IfModule>
