{"ast": null, "code": "'use strict';\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToCamelCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str.trim().replace(/_+|\\-+/g, ' ').replace(/(?:^\\w|[A-Z]|\\b\\w|\\s+)/g, function (match, index) {\n    if (Number(match) === 0) {\n      return '';\n    }\n    return index === 0 ? match.toLowerCase() : match.toUpperCase();\n  });\n};", "map": {"version": 3, "names": ["module", "exports", "strToCamelCase", "str", "Error", "trim", "replace", "match", "index", "Number", "toLowerCase", "toUpperCase"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/str-to-camel-case.js"], "sourcesContent": ["'use strict';\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToCamelCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str\n    .trim()\n    .replace(/_+|\\-+/g, ' ')\n    .replace(/(?:^\\w|[A-Z]|\\b\\w|\\s+)/g, function(match, index) {\n      if (Number(match) === 0) {\n        return '';\n      }\n      return (index === 0) ? match.toLowerCase() : match.toUpperCase();\n    });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,GAAG,CACPE,IAAI,CAAC,CAAC,CACNC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBA,OAAO,CAAC,yBAAyB,EAAE,UAASC,KAAK,EAAEC,KAAK,EAAE;IACzD,IAAIC,MAAM,CAACF,KAAK,CAAC,KAAK,CAAC,EAAE;MACvB,OAAO,EAAE;IACX;IACA,OAAQC,KAAK,KAAK,CAAC,GAAID,KAAK,CAACG,WAAW,CAAC,CAAC,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC;EAClE,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}