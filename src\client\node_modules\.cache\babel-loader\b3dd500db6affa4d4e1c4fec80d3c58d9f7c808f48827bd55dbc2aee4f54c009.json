{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\LandingPage.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport heroImage from '../assets/crypto-images.png';\nimport LandingPageFooter from '../components/LandingPageFooter';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimatedSection = ({\n  children\n}) => {\n  _s();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n  React.useEffect(() => {\n    if (inView) {\n      controls.start('visible');\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.section, {\n    ref: ref,\n    className: \"mt-24\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.8,\n          ease: 'easeOut'\n        }\n      },\n      hidden: {\n        opacity: 0,\n        y: 50\n      }\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(AnimatedSection, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c = AnimatedSection;\nconst LandingPage = () => {\n  _s2();\n  const navigate = useNavigate();\n  const handleJoin = e => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n  const marketData = [{\n    name: 'DOGE Index',\n    price: '$0.25173',\n    change: '+0.01197',\n    percent: '+4.75%'\n  }, {\n    name: 'SUSHI Index',\n    price: '$0.965',\n    change: '+0.036',\n    percent: '+3.77%'\n  }, {\n    name: 'MASK Index',\n    price: '$1.476',\n    change: '+0.028',\n    percent: '+1.88%'\n  }, {\n    name: 'UNI Index',\n    price: '$10.258',\n    change: '+0.057',\n    percent: '+0.55%'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-blue-950 text-white min-h-screen font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container-custom pt-32 pb-16\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatedSection, {\n        children: /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"grid md:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"text-left\",\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-6xl md:text-7xl font-bold leading-tight\",\n              children: [\"Trade Crypto\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-400\",\n                children: \"Like a Pro.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-6 text-lg text-gray-300\",\n              children: \"Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleJoin,\n              className: \"mt-8 flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                placeholder: \"Email/Phone Number\",\n                className: \"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\",\n                children: [\"Join Us\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"mt-12 md:mt-0\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: heroImage,\n              alt: \"BlazeTrade Services\",\n              className: \"rounded-2xl shadow-2xl shadow-blue-500/20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatedSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n          children: marketData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900/50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400\",\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold mt-1\",\n              children: item.price\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-center mt-1 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400 mr-2\",\n                children: item.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: [\"(\", item.percent, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatedSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold\",\n            children: \"Leading the Expansion of Altcoin Markets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-lg text-blue-200\",\n            children: \"Everything you need for a seamless trading experience.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid md:grid-cols-3 gap-8 mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"Secure Wallet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-blue-200\",\n                children: \"State-of-the-art security for your digital assets.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"Advanced Charting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-blue-200\",\n                children: \"Powerful tools and indicators to inform your trades.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"Real-time Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-blue-200\",\n                children: \"Standardised, real-time pricing across all markets.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatedSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Support New Listings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Be the first to trade promising new assets. Let's get them listed on BlazeTrade!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              className: \"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Exclusive Referral Bonuses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-blue-200\",\n              children: \"Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LandingPageFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s2(LandingPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c2 = LandingPage;\nexport default LandingPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnimatedSection\");\n$RefreshReg$(_c2, \"LandingPage\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "motion", "useAnimation", "useInView", "heroImage", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "jsxDEV", "_jsxDEV", "AnimatedSection", "children", "_s", "controls", "ref", "inView", "triggerOnce", "threshold", "useEffect", "start", "section", "className", "initial", "animate", "variants", "visible", "opacity", "y", "transition", "duration", "ease", "hidden", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "LandingPage", "_s2", "navigate", "handleJoin", "e", "preventDefault", "marketData", "name", "price", "change", "percent", "div", "x", "onSubmit", "type", "placeholder", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "scale", "delay", "src", "alt", "map", "item", "index", "to", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/LandingPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport heroImage from '../assets/crypto-images.png';\nimport LandingPageFooter from '../components/LandingPageFooter';\n\nconst AnimatedSection = ({ children }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  React.useEffect(() => {\n    if (inView) {\n      controls.start('visible');\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.section\n      ref={ref}\n      className=\"mt-24\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },\n        hidden: { opacity: 0, y: 50 },\n      }}\n    >\n      {children}\n    </motion.section>\n  );\n};\n\nconst LandingPage = () => {\n  const navigate = useNavigate();\n\n  const handleJoin = (e) => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n\n  const marketData = [\n    { name: 'DOGE Index', price: '$0.25173', change: '+0.01197', percent: '+4.75%' },\n    { name: 'SUSHI Index', price: '$0.965', change: '+0.036', percent: '+3.77%' },\n    { name: 'MASK Index', price: '$1.476', change: '+0.028', percent: '+1.88%' },\n    { name: 'UNI Index', price: '$10.258', change: '+0.057', percent: '+0.55%' },\n  ];\n\n  return (\n    <div className=\"bg-blue-950 text-white min-h-screen font-sans\">\n      <main className=\"container-custom pt-32 pb-16\">\n        {/* Hero Section */}\n        <AnimatedSection>\n          <section className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              className=\"text-left\"\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h1 className=\"text-6xl md:text-7xl font-bold leading-tight\">\n                Trade Crypto\n                <br />\n                <span className=\"text-blue-400\">Like a Pro.</span>\n              </h1>\n              <p className=\"mt-6 text-lg text-gray-300\">\n                Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\n              </p>\n\n              <form onSubmit={handleJoin} className=\"mt-8 flex flex-col sm:flex-row gap-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Email/Phone Number\"\n                  className=\"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"\n                />\n                <button type=\"submit\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\">\n                  Join Us\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n\n              </form>\n            </motion.div>\n            <motion.div\n              className=\"mt-12 md:mt-0\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <img src={heroImage} alt=\"BlazeTrade Services\" className=\"rounded-2xl shadow-2xl shadow-blue-500/20\" />\n            </motion.div>\n          </section>\n        </AnimatedSection>\n\n        {/* Market Ticker Section */}\n        <AnimatedSection>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n            {marketData.map((item, index) => (\n              <div key={index} className=\"bg-blue-900/50 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-400\">{item.name}</p>\n                <p className=\"text-2xl font-bold mt-1\">{item.price}</p>\n                <div className=\"flex justify-center items-center mt-1 text-sm\">\n                  <span className=\"text-green-400 mr-2\">{item.change}</span>\n                  <span className=\"text-gray-400\">({item.percent})</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </AnimatedSection>\n\n        {/* Services Section */}\n        <AnimatedSection>\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold\">Leading the Expansion of Altcoin Markets</h2>\n            <p className=\"mt-4 text-lg text-blue-200\">Everything you need for a seamless trading experience.</p>\n            <div className=\"grid md:grid-cols-3 gap-8 mt-12\">\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Secure Wallet</h3>\n                <p className=\"mt-2 text-blue-200\">State-of-the-art security for your digital assets.</p>\n              </div>\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Advanced Charting</h3>\n                <p className=\"mt-2 text-blue-200\">Powerful tools and indicators to inform your trades.</p>\n              </div>\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Real-time Pricing</h3>\n                <p className=\"mt-2 text-blue-200\">Standardised, real-time pricing across all markets.</p>\n              </div>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Promo Cards Section */}\n        <AnimatedSection>\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Support New Listings</h3>\n              <p className=\"mt-2 text-blue-200\">Be the first to trade promising new assets. Let's get them listed on BlazeTrade!</p>\n              <Link to=\"/services\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Exclusive Referral Bonuses</h3>\n              <p className=\"mt-2 text-blue-200\">Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.</p>\n              <Link to=\"/signup\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n          </div>\n        </AnimatedSection>\n      </main>\n      <LandingPageFooter />\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACpD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAM,CAACU,GAAG,EAAEC,MAAM,CAAC,GAAGV,SAAS,CAAC;IAC9BW,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFjB,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,IAAIH,MAAM,EAAE;MACVF,QAAQ,CAACM,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACN,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEN,OAAA,CAACN,MAAM,CAACiB,OAAO;IACbN,GAAG,EAAEA,GAAI;IACTO,SAAS,EAAC,OAAO;IACjBC,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAEV,QAAS;IAClBW,QAAQ,EAAE;MACRC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU;MAAE,CAAC;MAC7EC,MAAM,EAAE;QAAEL,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG;IAC9B,CAAE;IAAAhB,QAAA,EAEDA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAACvB,EAAA,CA3BIF,eAAe;EAAA,QACFN,YAAY,EACPC,SAAS;AAAA;AAAA+B,EAAA,GAF3B1B,eAAe;AA6BrB,MAAM2B,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAE9B,MAAMsC,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMI,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,UAAU;IAAEC,MAAM,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAS,CAAC,EAChF;IAAEH,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,QAAQ;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,EAC7E;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,QAAQ;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,EAC5E;IAAEH,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,SAAS;IAAEC,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC,CAC7E;EAED,oBACEtC,OAAA;IAAKY,SAAS,EAAC,+CAA+C;IAAAV,QAAA,gBAC5DF,OAAA;MAAMY,SAAS,EAAC,8BAA8B;MAAAV,QAAA,gBAE5CF,OAAA,CAACC,eAAe;QAAAC,QAAA,eACdF,OAAA;UAASY,SAAS,EAAC,yCAAyC;UAAAV,QAAA,gBAC1DF,OAAA,CAACN,MAAM,CAAC6C,GAAG;YACT3B,SAAS,EAAC,WAAW;YACrBC,OAAO,EAAE;cAAEI,OAAO,EAAE,CAAC;cAAEuB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC1B,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEuB,CAAC,EAAE;YAAE,CAAE;YAC9BrB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAlB,QAAA,gBAE9BF,OAAA;cAAIY,SAAS,EAAC,8CAA8C;cAAAV,QAAA,GAAC,cAE3D,eAAAF,OAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1B,OAAA;gBAAMY,SAAS,EAAC,eAAe;gBAAAV,QAAA,EAAC;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACL1B,OAAA;cAAGY,SAAS,EAAC,4BAA4B;cAAAV,QAAA,EAAC;YAE1C;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ1B,OAAA;cAAMyC,QAAQ,EAAEV,UAAW;cAACnB,SAAS,EAAC,sCAAsC;cAAAV,QAAA,gBAC1EF,OAAA;gBACE0C,IAAI,EAAC,OAAO;gBACZC,WAAW,EAAC,oBAAoB;gBAChC/B,SAAS,EAAC;cAAkI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I,CAAC,eACF1B,OAAA;gBAAQ0C,IAAI,EAAC,QAAQ;gBAAC9B,SAAS,EAAC,wIAAwI;gBAAAV,QAAA,GAAC,SAEvK,eAAAF,OAAA;kBAAK4C,KAAK,EAAC,4BAA4B;kBAAChC,SAAS,EAAC,SAAS;kBAACiC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAA5C,QAAA,eACjGF,OAAA;oBAAM+C,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,0IAA0I;oBAACC,QAAQ,EAAC;kBAAS;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACb1B,OAAA,CAACN,MAAM,CAAC6C,GAAG;YACT3B,SAAS,EAAC,eAAe;YACzBC,OAAO,EAAE;cAAEI,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAI,CAAE;YACpCpC,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAE,CAAE;YAClC/B,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAE+B,KAAK,EAAE;YAAI,CAAE;YAAAjD,QAAA,eAE1CF,OAAA;cAAKoD,GAAG,EAAEvD,SAAU;cAACwD,GAAG,EAAC,qBAAqB;cAACzC,SAAS,EAAC;YAA2C;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAGlB1B,OAAA,CAACC,eAAe;QAAAC,QAAA,eACdF,OAAA;UAAKY,SAAS,EAAC,mDAAmD;UAAAV,QAAA,EAC/DgC,UAAU,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC1BxD,OAAA;YAAiBY,SAAS,EAAC,+BAA+B;YAAAV,QAAA,gBACxDF,OAAA;cAAGY,SAAS,EAAC,uBAAuB;cAAAV,QAAA,EAAEqD,IAAI,CAACpB;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD1B,OAAA;cAAGY,SAAS,EAAC,yBAAyB;cAAAV,QAAA,EAAEqD,IAAI,CAACnB;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD1B,OAAA;cAAKY,SAAS,EAAC,+CAA+C;cAAAV,QAAA,gBAC5DF,OAAA;gBAAMY,SAAS,EAAC,qBAAqB;gBAAAV,QAAA,EAAEqD,IAAI,CAAClB;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1D1B,OAAA;gBAAMY,SAAS,EAAC,eAAe;gBAAAV,QAAA,GAAC,GAAC,EAACqD,IAAI,CAACjB,OAAO,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA,GANE8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGlB1B,OAAA,CAACC,eAAe;QAAAC,QAAA,eACdF,OAAA;UAAKY,SAAS,EAAC,aAAa;UAAAV,QAAA,gBAC1BF,OAAA;YAAIY,SAAS,EAAC,oBAAoB;YAAAV,QAAA,EAAC;UAAwC;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChF1B,OAAA;YAAGY,SAAS,EAAC,4BAA4B;YAAAV,QAAA,EAAC;UAAsD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpG1B,OAAA;YAAKY,SAAS,EAAC,iCAAiC;YAAAV,QAAA,gBAC9CF,OAAA;cAAKY,SAAS,EAAC,oGAAoG;cAAAV,QAAA,gBACjHF,OAAA;gBAAIY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAa;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD1B,OAAA;gBAAGY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAkD;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACN1B,OAAA;cAAKY,SAAS,EAAC,oGAAoG;cAAAV,QAAA,gBACjHF,OAAA;gBAAIY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAiB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD1B,OAAA;gBAAGY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAoD;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACN1B,OAAA;cAAKY,SAAS,EAAC,oGAAoG;cAAAV,QAAA,gBACjHF,OAAA;gBAAIY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAiB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD1B,OAAA;gBAAGY,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAAmD;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGlB1B,OAAA,CAACC,eAAe;QAAAC,QAAA,eACdF,OAAA;UAAKY,SAAS,EAAC,2BAA2B;UAAAV,QAAA,gBACxCF,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAV,QAAA,gBACjHF,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAV,QAAA,EAAC;YAAoB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D1B,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAV,QAAA,EAAC;YAAgF;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtH1B,OAAA,CAACR,IAAI;cAACiE,EAAE,EAAC,WAAW;cAAC7C,SAAS,EAAC,mEAAmE;cAAAV,QAAA,EAAC;YAAe;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eACN1B,OAAA;YAAKY,SAAS,EAAC,oGAAoG;YAAAV,QAAA,gBACjHF,OAAA;cAAIY,SAAS,EAAC,oBAAoB;cAAAV,QAAA,EAAC;YAA0B;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE1B,OAAA;cAAGY,SAAS,EAAC,oBAAoB;cAAAV,QAAA,EAAC;YAAqG;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3I1B,OAAA,CAACR,IAAI;cAACiE,EAAE,EAAC,SAAS;cAAC7C,SAAS,EAAC,mEAAmE;cAAAV,QAAA,EAAC;YAAe;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACP1B,OAAA,CAACF,iBAAiB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEV,CAAC;AAACG,GAAA,CAvHID,WAAW;EAAA,QACEnC,WAAW;AAAA;AAAAiE,GAAA,GADxB9B,WAAW;AAyHjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}