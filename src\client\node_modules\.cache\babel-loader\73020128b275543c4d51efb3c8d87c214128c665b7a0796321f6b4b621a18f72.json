{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\AuthHeader.js\";\nimport React from 'react';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthHeader = ({\n  title,\n  subtitle\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"w-full py-4 bg-gray-900\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom flex flex-col items-center justify-center text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: blazeTradeLogo,\n        alt: \"BlazeTrade Logo\",\n        className: \"h-12 object-contain mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-white\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-sm text-gray-400\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AuthHeader;\nexport default AuthHeader;\nvar _c;\n$RefreshReg$(_c, \"AuthHeader\");", "map": {"version": 3, "names": ["React", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "subtitle", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/AuthHeader.js"], "sourcesContent": ["import React from 'react';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst AuthHeader = ({ title, subtitle }) => {\n  return (\n    <header className=\"w-full py-4 bg-gray-900\">\n      <div className=\"container-custom flex flex-col items-center justify-center text-center\">\n        <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-12 object-contain mb-4\" />\n        <h1 className=\"text-3xl font-bold text-white\">{title}</h1>\n        <p className=\"mt-2 text-sm text-gray-400\">{subtitle}</p>\n      </div>\n    </header>\n  );\n};\n\nexport default AuthHeader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAC1C,oBACEH,OAAA;IAAQI,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACzCL,OAAA;MAAKI,SAAS,EAAC,wEAAwE;MAAAC,QAAA,gBACrFL,OAAA;QAAKM,GAAG,EAAER,cAAe;QAACS,GAAG,EAAC,iBAAiB;QAACH,SAAS,EAAC;MAA0B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvFX,OAAA;QAAII,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAEH;MAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1DX,OAAA;QAAGI,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAEF;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GAVIX,UAAU;AAYhB,eAAeA,UAAU;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}