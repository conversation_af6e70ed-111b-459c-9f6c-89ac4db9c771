{"ast": null, "code": "'use strict';\n\nconst axios = require('axios');\nconst pkg = require('../../package.json');\nconst {\n  helpers: {\n    mergeData\n  },\n  classes: {\n    Response,\n    ResponseError\n  }\n} = require('@sendgrid/helpers');\nconst API_KEY_PREFIX = 'SG.';\nconst SENDGRID_BASE_URL = 'https://api.sendgrid.com/';\nconst TWILIO_BASE_URL = 'https://email.twilio.com/';\nconst SENDGRID_REGION = 'global';\n// Initialize the allowed regions and their corresponding hosts\nconst REGION_HOST_MAP = {\n  eu: 'https://api.eu.sendgrid.com/',\n  global: 'https://api.sendgrid.com/'\n};\nclass Client {\n  constructor() {\n    this.auth = '';\n    this.impersonateSubuser = '';\n    this.sendgrid_region = SENDGRID_REGION;\n    this.defaultHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      'User-Agent': 'sendgrid/' + pkg.version + ';nodejs'\n    };\n    this.defaultRequest = {\n      baseUrl: SENDGRID_BASE_URL,\n      url: '',\n      method: 'GET',\n      headers: {},\n      maxContentLength: Infinity,\n      // Don't limit the content length.\n      maxBodyLength: Infinity\n    };\n  }\n  setApiKey(apiKey) {\n    this.auth = 'Bearer ' + apiKey;\n    this.setDefaultRequest('baseUrl', REGION_HOST_MAP[this.sendgrid_region]);\n    if (!this.isValidApiKey(apiKey)) {\n      console.warn(`API key does not start with \"${API_KEY_PREFIX}\".`);\n    }\n  }\n  setTwilioEmailAuth(username, password) {\n    const b64Auth = Buffer.from(username + ':' + password).toString('base64');\n    this.auth = 'Basic ' + b64Auth;\n    this.setDefaultRequest('baseUrl', TWILIO_BASE_URL);\n    if (!this.isValidTwilioAuth(username, password)) {\n      console.warn('Twilio Email credentials must be non-empty strings.');\n    }\n  }\n  isValidApiKey(apiKey) {\n    return this.isString(apiKey) && apiKey.trim().startsWith(API_KEY_PREFIX);\n  }\n  isValidTwilioAuth(username, password) {\n    return this.isString(username) && username && this.isString(password) && password;\n  }\n  isString(value) {\n    return typeof value === 'string' || value instanceof String;\n  }\n  setImpersonateSubuser(subuser) {\n    this.impersonateSubuser = subuser;\n  }\n  setDefaultHeader(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultHeaders, key);\n      return this;\n    }\n    this.defaultHeaders[key] = value;\n    return this;\n  }\n  setDefaultRequest(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultRequest, key);\n      return this;\n    }\n    this.defaultRequest[key] = value;\n    return this;\n  }\n\n  /**\n   * Global is the default residency (or region)\n   * Global region means the message will be sent through https://api.sendgrid.com\n   * EU region means the message will be sent through https://api.eu.sendgrid.com\n   **/\n  setDataResidency(region) {\n    if (!REGION_HOST_MAP.hasOwnProperty(region)) {\n      console.warn('Region can only be \"global\" or \"eu\".');\n    } else {\n      this.sendgrid_region = region;\n      this.setDefaultRequest('baseUrl', REGION_HOST_MAP[region]);\n    }\n    return this;\n  }\n  createHeaders(data) {\n    // Merge data with default headers.\n    const headers = mergeData(this.defaultHeaders, data);\n\n    // Add auth, but don't overwrite if header already set.\n    if (typeof headers.Authorization === 'undefined' && this.auth) {\n      headers.Authorization = this.auth;\n    }\n    if (this.impersonateSubuser) {\n      headers['On-Behalf-Of'] = this.impersonateSubuser;\n    }\n    return headers;\n  }\n  createRequest(data) {\n    let options = {\n      url: data.uri || data.url,\n      baseUrl: data.baseUrl,\n      method: data.method,\n      data: data.body,\n      params: data.qs,\n      headers: data.headers\n    };\n\n    // Merge data with default request.\n    options = mergeData(this.defaultRequest, options);\n    options.headers = this.createHeaders(options.headers);\n    options.baseURL = options.baseUrl;\n    delete options.baseUrl;\n    return options;\n  }\n  request(data, cb) {\n    data = this.createRequest(data);\n    const promise = new Promise((resolve, reject) => {\n      axios(data).then(response => {\n        return resolve([new Response(response.status, response.data, response.headers), response.data]);\n      }).catch(error => {\n        if (error.response) {\n          if (error.response.status >= 400) {\n            return reject(new ResponseError(error.response));\n          }\n        }\n        return reject(error);\n      });\n    });\n\n    // Throw an error in case a callback function was not passed.\n    if (cb && typeof cb !== 'function') {\n      throw new Error('Callback passed is not a function.');\n    }\n    if (cb) {\n      return promise.then(result => cb(null, result)).catch(error => cb(error, null));\n    }\n    return promise;\n  }\n}\nmodule.exports = Client;", "map": {"version": 3, "names": ["axios", "require", "pkg", "helpers", "mergeData", "classes", "Response", "ResponseError", "API_KEY_PREFIX", "SENDGRID_BASE_URL", "TWILIO_BASE_URL", "SENDGRID_REGION", "REGION_HOST_MAP", "eu", "global", "Client", "constructor", "auth", "impersonate<PERSON><PERSON><PERSON>", "sendgrid_region", "defaultHeaders", "Accept", "version", "defaultRequest", "baseUrl", "url", "method", "headers", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setDefaultRequest", "isValidApiKey", "console", "warn", "setTwilioEmailAuth", "username", "password", "b64Auth", "<PERSON><PERSON><PERSON>", "from", "toString", "isValidTwilioAuth", "isString", "trim", "startsWith", "value", "String", "setImpersonateSubuser", "subuser", "set<PERSON>ef<PERSON>H<PERSON>er", "key", "Object", "assign", "setDataResidency", "region", "hasOwnProperty", "createHeaders", "data", "Authorization", "createRequest", "options", "uri", "body", "params", "qs", "baseURL", "request", "cb", "promise", "Promise", "resolve", "reject", "then", "response", "status", "catch", "error", "Error", "result", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/client/src/classes/client.js"], "sourcesContent": ["'use strict';\nconst axios = require('axios');\nconst pkg = require('../../package.json');\nconst {\n  helpers: {\n    mergeData,\n  },\n  classes: {\n    Response,\n    ResponseError,\n  },\n} = require('@sendgrid/helpers');\n\nconst API_KEY_PREFIX = 'SG.';\nconst SENDGRID_BASE_URL = 'https://api.sendgrid.com/';\nconst TWILIO_BASE_URL = 'https://email.twilio.com/';\nconst SENDGRID_REGION = 'global';\n// Initialize the allowed regions and their corresponding hosts\nconst REGION_HOST_MAP = {\n  eu: 'https://api.eu.sendgrid.com/',\n  global: 'https://api.sendgrid.com/',\n};\nclass Client {\n  constructor() {\n    this.auth = '';\n    this.impersonateSubuser = '';\n    this.sendgrid_region = SENDGRID_REGION;\n\n    this.defaultHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      'User-Agent': 'sendgrid/' + pkg.version + ';nodejs',\n    };\n\n    this.defaultRequest = {\n      baseUrl: SENDGRID_BASE_URL,\n      url: '',\n      method: 'GET',\n      headers: {},\n      maxContentLength: Infinity, // Don't limit the content length.\n      maxBodyLength: Infinity,\n    };\n  }\n\n  setApiKey(apiKey) {\n    this.auth = 'Bearer ' + apiKey;\n    this.setDefaultRequest('baseUrl', REGION_HOST_MAP[this.sendgrid_region]);\n\n    if (!this.isValidApiKey(apiKey)) {\n      console.warn(`API key does not start with \"${API_KEY_PREFIX}\".`);\n    }\n  }\n\n  setTwilioEmailAuth(username, password) {\n    const b64Auth = Buffer.from(username + ':' + password).toString('base64');\n    this.auth = 'Basic ' + b64Auth;\n    this.setDefaultRequest('baseUrl', TWILIO_BASE_URL);\n\n    if (!this.isValidTwilioAuth(username, password)) {\n      console.warn('Twilio Email credentials must be non-empty strings.');\n    }\n  }\n\n  isValidApiKey(apiKey) {\n    return this.isString(apiKey) && apiKey.trim().startsWith(API_KEY_PREFIX);\n  }\n\n  isValidTwilioAuth(username, password) {\n    return this.isString(username) && username\n      && this.isString(password) && password;\n  }\n\n  isString(value) {\n    return typeof value === 'string' || value instanceof String;\n  }\n\n  setImpersonateSubuser(subuser) {\n    this.impersonateSubuser = subuser;\n  }\n\n  setDefaultHeader(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultHeaders, key);\n      return this;\n    }\n\n    this.defaultHeaders[key] = value;\n    return this;\n  }\n\n  setDefaultRequest(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultRequest, key);\n      return this;\n    }\n\n    this.defaultRequest[key] = value;\n    return this;\n  }\n\n  /**\n   * Global is the default residency (or region)\n   * Global region means the message will be sent through https://api.sendgrid.com\n   * EU region means the message will be sent through https://api.eu.sendgrid.com\n   **/\n  setDataResidency(region) {\n    if (!REGION_HOST_MAP.hasOwnProperty(region)) {\n      console.warn('Region can only be \"global\" or \"eu\".');\n    } else {\n      this.sendgrid_region = region;\n      this.setDefaultRequest('baseUrl', REGION_HOST_MAP[region]);\n    }\n    return this;\n  }\n\n  createHeaders(data) {\n    // Merge data with default headers.\n    const headers = mergeData(this.defaultHeaders, data);\n\n    // Add auth, but don't overwrite if header already set.\n    if (typeof headers.Authorization === 'undefined' && this.auth) {\n      headers.Authorization = this.auth;\n    }\n\n    if (this.impersonateSubuser) {\n      headers['On-Behalf-Of'] = this.impersonateSubuser;\n    }\n\n    return headers;\n  }\n\n  createRequest(data) {\n    let options = {\n      url: data.uri || data.url,\n      baseUrl: data.baseUrl,\n      method: data.method,\n      data: data.body,\n      params: data.qs,\n      headers: data.headers,\n    };\n\n    // Merge data with default request.\n    options = mergeData(this.defaultRequest, options);\n    options.headers = this.createHeaders(options.headers);\n    options.baseURL = options.baseUrl;\n    delete options.baseUrl;\n\n    return options;\n  }\n\n  request(data, cb) {\n    data = this.createRequest(data);\n\n    const promise = new Promise((resolve, reject) => {\n      axios(data)\n        .then(response => {\n          return resolve([\n            new Response(response.status, response.data, response.headers),\n            response.data,\n          ]);\n        })\n        .catch(error => {\n          if (error.response) {\n            if (error.response.status >= 400) {\n              return reject(new ResponseError(error.response));\n            }\n          }\n          return reject(error);\n        });\n    });\n\n    // Throw an error in case a callback function was not passed.\n    if (cb && typeof cb !== 'function') {\n      throw new Error('Callback passed is not a function.');\n    }\n\n    if (cb) {\n      return promise\n        .then(result => cb(null, result))\n        .catch(error => cb(error, null));\n    }\n\n    return promise;\n  }\n}\n\nmodule.exports = Client;\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAMC,GAAG,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACzC,MAAM;EACJE,OAAO,EAAE;IACPC;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,QAAQ;IACRC;EACF;AACF,CAAC,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAEhC,MAAMO,cAAc,GAAG,KAAK;AAC5B,MAAMC,iBAAiB,GAAG,2BAA2B;AACrD,MAAMC,eAAe,GAAG,2BAA2B;AACnD,MAAMC,eAAe,GAAG,QAAQ;AAChC;AACA,MAAMC,eAAe,GAAG;EACtBC,EAAE,EAAE,8BAA8B;EAClCC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,eAAe,GAAGR,eAAe;IAEtC,IAAI,CAACS,cAAc,GAAG;MACpBC,MAAM,EAAE,kBAAkB;MAC1B,cAAc,EAAE,kBAAkB;MAClC,YAAY,EAAE,WAAW,GAAGnB,GAAG,CAACoB,OAAO,GAAG;IAC5C,CAAC;IAED,IAAI,CAACC,cAAc,GAAG;MACpBC,OAAO,EAAEf,iBAAiB;MAC1BgB,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE,CAAC,CAAC;MACXC,gBAAgB,EAAEC,QAAQ;MAAE;MAC5BC,aAAa,EAAED;IACjB,CAAC;EACH;EAEAE,SAASA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACf,IAAI,GAAG,SAAS,GAAGe,MAAM;IAC9B,IAAI,CAACC,iBAAiB,CAAC,SAAS,EAAErB,eAAe,CAAC,IAAI,CAACO,eAAe,CAAC,CAAC;IAExE,IAAI,CAAC,IAAI,CAACe,aAAa,CAACF,MAAM,CAAC,EAAE;MAC/BG,OAAO,CAACC,IAAI,CAAC,gCAAgC5B,cAAc,IAAI,CAAC;IAClE;EACF;EAEA6B,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACrC,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,QAAQ,GAAG,GAAG,GAAGC,QAAQ,CAAC,CAACI,QAAQ,CAAC,QAAQ,CAAC;IACzE,IAAI,CAAC1B,IAAI,GAAG,QAAQ,GAAGuB,OAAO;IAC9B,IAAI,CAACP,iBAAiB,CAAC,SAAS,EAAEvB,eAAe,CAAC;IAElD,IAAI,CAAC,IAAI,CAACkC,iBAAiB,CAACN,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MAC/CJ,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;IACrE;EACF;EAEAF,aAAaA,CAACF,MAAM,EAAE;IACpB,OAAO,IAAI,CAACa,QAAQ,CAACb,MAAM,CAAC,IAAIA,MAAM,CAACc,IAAI,CAAC,CAAC,CAACC,UAAU,CAACvC,cAAc,CAAC;EAC1E;EAEAoC,iBAAiBA,CAACN,QAAQ,EAAEC,QAAQ,EAAE;IACpC,OAAO,IAAI,CAACM,QAAQ,CAACP,QAAQ,CAAC,IAAIA,QAAQ,IACrC,IAAI,CAACO,QAAQ,CAACN,QAAQ,CAAC,IAAIA,QAAQ;EAC1C;EAEAM,QAAQA,CAACG,KAAK,EAAE;IACd,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYC,MAAM;EAC7D;EAEAC,qBAAqBA,CAACC,OAAO,EAAE;IAC7B,IAAI,CAACjC,kBAAkB,GAAGiC,OAAO;EACnC;EAEAC,gBAAgBA,CAACC,GAAG,EAAEL,KAAK,EAAE;IAC3B,IAAIK,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3C;MACAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACnC,cAAc,EAAEiC,GAAG,CAAC;MACvC,OAAO,IAAI;IACb;IAEA,IAAI,CAACjC,cAAc,CAACiC,GAAG,CAAC,GAAGL,KAAK;IAChC,OAAO,IAAI;EACb;EAEAf,iBAAiBA,CAACoB,GAAG,EAAEL,KAAK,EAAE;IAC5B,IAAIK,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3C;MACAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChC,cAAc,EAAE8B,GAAG,CAAC;MACvC,OAAO,IAAI;IACb;IAEA,IAAI,CAAC9B,cAAc,CAAC8B,GAAG,CAAC,GAAGL,KAAK;IAChC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEQ,gBAAgBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC7C,eAAe,CAAC8C,cAAc,CAACD,MAAM,CAAC,EAAE;MAC3CtB,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAACjB,eAAe,GAAGsC,MAAM;MAC7B,IAAI,CAACxB,iBAAiB,CAAC,SAAS,EAAErB,eAAe,CAAC6C,MAAM,CAAC,CAAC;IAC5D;IACA,OAAO,IAAI;EACb;EAEAE,aAAaA,CAACC,IAAI,EAAE;IAClB;IACA,MAAMjC,OAAO,GAAGvB,SAAS,CAAC,IAAI,CAACgB,cAAc,EAAEwC,IAAI,CAAC;;IAEpD;IACA,IAAI,OAAOjC,OAAO,CAACkC,aAAa,KAAK,WAAW,IAAI,IAAI,CAAC5C,IAAI,EAAE;MAC7DU,OAAO,CAACkC,aAAa,GAAG,IAAI,CAAC5C,IAAI;IACnC;IAEA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3BS,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAACT,kBAAkB;IACnD;IAEA,OAAOS,OAAO;EAChB;EAEAmC,aAAaA,CAACF,IAAI,EAAE;IAClB,IAAIG,OAAO,GAAG;MACZtC,GAAG,EAAEmC,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAACnC,GAAG;MACzBD,OAAO,EAAEoC,IAAI,CAACpC,OAAO;MACrBE,MAAM,EAAEkC,IAAI,CAAClC,MAAM;MACnBkC,IAAI,EAAEA,IAAI,CAACK,IAAI;MACfC,MAAM,EAAEN,IAAI,CAACO,EAAE;MACfxC,OAAO,EAAEiC,IAAI,CAACjC;IAChB,CAAC;;IAED;IACAoC,OAAO,GAAG3D,SAAS,CAAC,IAAI,CAACmB,cAAc,EAAEwC,OAAO,CAAC;IACjDA,OAAO,CAACpC,OAAO,GAAG,IAAI,CAACgC,aAAa,CAACI,OAAO,CAACpC,OAAO,CAAC;IACrDoC,OAAO,CAACK,OAAO,GAAGL,OAAO,CAACvC,OAAO;IACjC,OAAOuC,OAAO,CAACvC,OAAO;IAEtB,OAAOuC,OAAO;EAChB;EAEAM,OAAOA,CAACT,IAAI,EAAEU,EAAE,EAAE;IAChBV,IAAI,GAAG,IAAI,CAACE,aAAa,CAACF,IAAI,CAAC;IAE/B,MAAMW,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC/C1E,KAAK,CAAC4D,IAAI,CAAC,CACRe,IAAI,CAACC,QAAQ,IAAI;QAChB,OAAOH,OAAO,CAAC,CACb,IAAInE,QAAQ,CAACsE,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAAChB,IAAI,EAAEgB,QAAQ,CAACjD,OAAO,CAAC,EAC9DiD,QAAQ,CAAChB,IAAI,CACd,CAAC;MACJ,CAAC,CAAC,CACDkB,KAAK,CAACC,KAAK,IAAI;QACd,IAAIA,KAAK,CAACH,QAAQ,EAAE;UAClB,IAAIG,KAAK,CAACH,QAAQ,CAACC,MAAM,IAAI,GAAG,EAAE;YAChC,OAAOH,MAAM,CAAC,IAAInE,aAAa,CAACwE,KAAK,CAACH,QAAQ,CAAC,CAAC;UAClD;QACF;QACA,OAAOF,MAAM,CAACK,KAAK,CAAC;MACtB,CAAC,CAAC;IACN,CAAC,CAAC;;IAEF;IACA,IAAIT,EAAE,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIU,KAAK,CAAC,oCAAoC,CAAC;IACvD;IAEA,IAAIV,EAAE,EAAE;MACN,OAAOC,OAAO,CACXI,IAAI,CAACM,MAAM,IAAIX,EAAE,CAAC,IAAI,EAAEW,MAAM,CAAC,CAAC,CAChCH,KAAK,CAACC,KAAK,IAAIT,EAAE,CAACS,KAAK,EAAE,IAAI,CAAC,CAAC;IACpC;IAEA,OAAOR,OAAO;EAChB;AACF;AAEAW,MAAM,CAACC,OAAO,GAAGpE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}