{"ast": null, "code": "import React from'react';import{Link,useNavigate}from'react-router-dom';import{motion,useAnimation}from'framer-motion';import{useInView}from'react-intersection-observer';import heroImage from'../assets/crypto-images.png';import LandingPageFooter from'../components/LandingPageFooter';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimatedSection=_ref=>{let{children}=_ref;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});React.useEffect(()=>{if(inView){controls.start('visible');}},[controls,inView]);return/*#__PURE__*/_jsx(motion.section,{ref:ref,className:\"mt-24\",initial:\"hidden\",animate:controls,variants:{visible:{opacity:1,y:0,transition:{duration:0.8,ease:'easeOut'}},hidden:{opacity:0,y:50}},children:children});};const LandingPage=()=>{const navigate=useNavigate();const handleJoin=e=>{e.preventDefault();navigate('/signup');};const marketData=[{name:'DOGE Index',price:'$0.25173',change:'+0.01197',percent:'+4.75%'},{name:'SUSHI Index',price:'$0.965',change:'+0.036',percent:'+3.77%'},{name:'MASK Index',price:'$1.476',change:'+0.028',percent:'+1.88%'},{name:'UNI Index',price:'$10.258',change:'+0.057',percent:'+0.55%'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-950 text-white min-h-screen font-sans\",children:[/*#__PURE__*/_jsxs(\"main\",{className:\"container-custom pt-32 pb-16\",children:[/*#__PURE__*/_jsx(AnimatedSection,{children:/*#__PURE__*/_jsxs(\"section\",{className:\"grid md:grid-cols-2 gap-12 items-center\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"text-left\",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:0.8},children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-6xl md:text-7xl font-bold leading-tight\",children:[\"Trade Crypto\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-400\",children:\"Like a Pro.\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-6 text-lg text-gray-300\",children:\"Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleJoin,className:\"mt-8 flex flex-col sm:flex-row gap-4\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"email\",placeholder:\"Email/Phone Number\",className:\"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\",children:[\"Join Us\",/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-5 w-5\",viewBox:\"0 0 20 20\",fill:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",clipRule:\"evenodd\"})})]})]})]}),/*#__PURE__*/_jsx(motion.div,{className:\"mt-12 md:mt-0\",initial:{opacity:0,scale:0.8},animate:{opacity:1,scale:1},transition:{duration:0.8,delay:0.2},children:/*#__PURE__*/_jsx(\"img\",{src:heroImage,alt:\"BlazeTrade Services\",className:\"rounded-2xl shadow-2xl shadow-blue-500/20\"})})]})}),/*#__PURE__*/_jsx(AnimatedSection,{children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",children:marketData.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900/50 p-4 rounded-lg\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:item.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold mt-1\",children:item.price}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center items-center mt-1 text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-green-400 mr-2\",children:item.change}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-400\",children:[\"(\",item.percent,\")\"]})]})]},index))})}),/*#__PURE__*/_jsx(AnimatedSection,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-4xl font-bold\",children:\"Leading the Expansion of Altcoin Markets\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-lg text-blue-200\",children:\"Everything you need for a seamless trading experience.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-3 gap-8 mt-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold\",children:\"Secure Wallet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-blue-200\",children:\"State-of-the-art security for your digital assets.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold\",children:\"Advanced Charting\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-blue-200\",children:\"Powerful tools and indicators to inform your trades.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold\",children:\"Real-time Pricing\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-blue-200\",children:\"Standardised, real-time pricing across all markets.\"})]})]})]})}),/*#__PURE__*/_jsx(AnimatedSection,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold\",children:\"Support New Listings\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-blue-200\",children:\"Be the first to trade promising new assets. Let's get them listed on BlazeTrade!\"}),/*#__PURE__*/_jsx(Link,{to:\"/services\",className:\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",children:\"Join Now \\u2192\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold\",children:\"Exclusive Referral Bonuses\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-blue-200\",children:\"Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.\"}),/*#__PURE__*/_jsx(Link,{to:\"/signup\",className:\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\",children:\"Join Now \\u2192\"})]})]})})]}),/*#__PURE__*/_jsx(LandingPageFooter,{})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "motion", "useAnimation", "useInView", "heroImage", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "jsx", "_jsx", "jsxs", "_jsxs", "AnimatedSection", "_ref", "children", "controls", "ref", "inView", "triggerOnce", "threshold", "useEffect", "start", "section", "className", "initial", "animate", "variants", "visible", "opacity", "y", "transition", "duration", "ease", "hidden", "LandingPage", "navigate", "handleJoin", "e", "preventDefault", "marketData", "name", "price", "change", "percent", "div", "x", "onSubmit", "type", "placeholder", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "scale", "delay", "src", "alt", "map", "item", "index", "to"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/LandingPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport heroImage from '../assets/crypto-images.png';\nimport LandingPageFooter from '../components/LandingPageFooter';\n\nconst AnimatedSection = ({ children }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  React.useEffect(() => {\n    if (inView) {\n      controls.start('visible');\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.section\n      ref={ref}\n      className=\"mt-24\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },\n        hidden: { opacity: 0, y: 50 },\n      }}\n    >\n      {children}\n    </motion.section>\n  );\n};\n\nconst LandingPage = () => {\n  const navigate = useNavigate();\n\n  const handleJoin = (e) => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n\n  const marketData = [\n    { name: 'DOGE Index', price: '$0.25173', change: '+0.01197', percent: '+4.75%' },\n    { name: 'SUSHI Index', price: '$0.965', change: '+0.036', percent: '+3.77%' },\n    { name: 'MASK Index', price: '$1.476', change: '+0.028', percent: '+1.88%' },\n    { name: 'UNI Index', price: '$10.258', change: '+0.057', percent: '+0.55%' },\n  ];\n\n  return (\n    <div className=\"bg-blue-950 text-white min-h-screen font-sans\">\n      <main className=\"container-custom pt-32 pb-16\">\n        {/* Hero Section */}\n        <AnimatedSection>\n          <section className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              className=\"text-left\"\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h1 className=\"text-6xl md:text-7xl font-bold leading-tight\">\n                Trade Crypto\n                <br />\n                <span className=\"text-blue-400\">Like a Pro.</span>\n              </h1>\n              <p className=\"mt-6 text-lg text-gray-300\">\n                Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\n              </p>\n\n              <form onSubmit={handleJoin} className=\"mt-8 flex flex-col sm:flex-row gap-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Email/Phone Number\"\n                  className=\"flex-grow bg-blue-900 border border-blue-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-white\"\n                />\n                <button type=\"submit\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\">\n                  Join Us\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n\n              </form>\n            </motion.div>\n            <motion.div\n              className=\"mt-12 md:mt-0\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <img src={heroImage} alt=\"BlazeTrade Services\" className=\"rounded-2xl shadow-2xl shadow-blue-500/20\" />\n            </motion.div>\n          </section>\n        </AnimatedSection>\n\n        {/* Market Ticker Section */}\n        <AnimatedSection>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n            {marketData.map((item, index) => (\n              <div key={index} className=\"bg-blue-900/50 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-400\">{item.name}</p>\n                <p className=\"text-2xl font-bold mt-1\">{item.price}</p>\n                <div className=\"flex justify-center items-center mt-1 text-sm\">\n                  <span className=\"text-green-400 mr-2\">{item.change}</span>\n                  <span className=\"text-gray-400\">({item.percent})</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </AnimatedSection>\n\n        {/* Services Section */}\n        <AnimatedSection>\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold\">Leading the Expansion of Altcoin Markets</h2>\n            <p className=\"mt-4 text-lg text-blue-200\">Everything you need for a seamless trading experience.</p>\n            <div className=\"grid md:grid-cols-3 gap-8 mt-12\">\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Secure Wallet</h3>\n                <p className=\"mt-2 text-blue-200\">State-of-the-art security for your digital assets.</p>\n              </div>\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Advanced Charting</h3>\n                <p className=\"mt-2 text-blue-200\">Powerful tools and indicators to inform your trades.</p>\n              </div>\n              <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n                <h3 className=\"text-2xl font-bold\">Real-time Pricing</h3>\n                <p className=\"mt-2 text-blue-200\">Standardised, real-time pricing across all markets.</p>\n              </div>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Promo Cards Section */}\n        <AnimatedSection>\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Support New Listings</h3>\n              <p className=\"mt-2 text-blue-200\">Be the first to trade promising new assets. Let's get them listed on BlazeTrade!</p>\n              <Link to=\"/services\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n            <div className=\"bg-blue-900 border border-blue-800 rounded-xl p-8 hover:border-blue-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Exclusive Referral Bonuses</h3>\n              <p className=\"mt-2 text-blue-200\">Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.</p>\n              <Link to=\"/signup\" className=\"mt-4 inline-block text-blue-400 font-semibold hover:text-blue-300\">Join Now &rarr;</Link>\n            </div>\n          </div>\n        </AnimatedSection>\n      </main>\n      <LandingPageFooter />\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,CAAEC,YAAY,KAAQ,eAAe,CACpD,OAASC,SAAS,KAAQ,6BAA6B,CACvD,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhE,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACnC,KAAM,CAAAE,QAAQ,CAAGX,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACY,GAAG,CAAEC,MAAM,CAAC,CAAGZ,SAAS,CAAC,CAC9Ba,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFnB,KAAK,CAACoB,SAAS,CAAC,IAAM,CACpB,GAAIH,MAAM,CAAE,CACVF,QAAQ,CAACM,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACN,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACER,IAAA,CAACN,MAAM,CAACmB,OAAO,EACbN,GAAG,CAAEA,GAAI,CACTO,SAAS,CAAC,OAAO,CACjBC,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAEV,QAAS,CAClBW,QAAQ,CAAE,CACRC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,SAAU,CAAE,CAAC,CAC7EC,MAAM,CAAE,CAAEL,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAC9B,CAAE,CAAAf,QAAA,CAEDA,QAAQ,CACK,CAAC,CAErB,CAAC,CAED,KAAM,CAAAoB,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAkC,UAAU,CAAIC,CAAC,EAAK,CACxBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBH,QAAQ,CAAC,SAAS,CAAC,CACrB,CAAC,CAED,KAAM,CAAAI,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,YAAY,CAAEC,KAAK,CAAE,UAAU,CAAEC,MAAM,CAAE,UAAU,CAAEC,OAAO,CAAE,QAAS,CAAC,CAChF,CAAEH,IAAI,CAAE,aAAa,CAAEC,KAAK,CAAE,QAAQ,CAAEC,MAAM,CAAE,QAAQ,CAAEC,OAAO,CAAE,QAAS,CAAC,CAC7E,CAAEH,IAAI,CAAE,YAAY,CAAEC,KAAK,CAAE,QAAQ,CAAEC,MAAM,CAAE,QAAQ,CAAEC,OAAO,CAAE,QAAS,CAAC,CAC5E,CAAEH,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,SAAS,CAAEC,MAAM,CAAE,QAAQ,CAAEC,OAAO,CAAE,QAAS,CAAC,CAC7E,CAED,mBACEhC,KAAA,QAAKY,SAAS,CAAC,+CAA+C,CAAAT,QAAA,eAC5DH,KAAA,SAAMY,SAAS,CAAC,8BAA8B,CAAAT,QAAA,eAE5CL,IAAA,CAACG,eAAe,EAAAE,QAAA,cACdH,KAAA,YAASY,SAAS,CAAC,yCAAyC,CAAAT,QAAA,eAC1DH,KAAA,CAACR,MAAM,CAACyC,GAAG,EACTrB,SAAS,CAAC,WAAW,CACrBC,OAAO,CAAE,CAAEI,OAAO,CAAE,CAAC,CAAEiB,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCpB,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAEiB,CAAC,CAAE,CAAE,CAAE,CAC9Bf,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAjB,QAAA,eAE9BH,KAAA,OAAIY,SAAS,CAAC,8CAA8C,CAAAT,QAAA,EAAC,cAE3D,cAAAL,IAAA,QAAK,CAAC,cACNA,IAAA,SAAMc,SAAS,CAAC,eAAe,CAAAT,QAAA,CAAC,aAAW,CAAM,CAAC,EAChD,CAAC,cACLL,IAAA,MAAGc,SAAS,CAAC,4BAA4B,CAAAT,QAAA,CAAC,kHAE1C,CAAG,CAAC,cAEJH,KAAA,SAAMmC,QAAQ,CAAEV,UAAW,CAACb,SAAS,CAAC,sCAAsC,CAAAT,QAAA,eAC1EL,IAAA,UACEsC,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,oBAAoB,CAChCzB,SAAS,CAAC,kIAAkI,CAC7I,CAAC,cACFZ,KAAA,WAAQoC,IAAI,CAAC,QAAQ,CAACxB,SAAS,CAAC,wIAAwI,CAAAT,QAAA,EAAC,SAEvK,cAAAL,IAAA,QAAKwC,KAAK,CAAC,4BAA4B,CAAC1B,SAAS,CAAC,SAAS,CAAC2B,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,cAAc,CAAArC,QAAA,cACjGL,IAAA,SAAM2C,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,0IAA0I,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACxL,CAAC,EACA,CAAC,EAEL,CAAC,EACG,CAAC,cACb7C,IAAA,CAACN,MAAM,CAACyC,GAAG,EACTrB,SAAS,CAAC,eAAe,CACzBC,OAAO,CAAE,CAAEI,OAAO,CAAE,CAAC,CAAE2B,KAAK,CAAE,GAAI,CAAE,CACpC9B,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAE2B,KAAK,CAAE,CAAE,CAAE,CAClCzB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEyB,KAAK,CAAE,GAAI,CAAE,CAAA1C,QAAA,cAE1CL,IAAA,QAAKgD,GAAG,CAAEnD,SAAU,CAACoD,GAAG,CAAC,qBAAqB,CAACnC,SAAS,CAAC,2CAA2C,CAAE,CAAC,CAC7F,CAAC,EACN,CAAC,CACK,CAAC,cAGlBd,IAAA,CAACG,eAAe,EAAAE,QAAA,cACdL,IAAA,QAAKc,SAAS,CAAC,mDAAmD,CAAAT,QAAA,CAC/DyB,UAAU,CAACoB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC1BlD,KAAA,QAAiBY,SAAS,CAAC,+BAA+B,CAAAT,QAAA,eACxDL,IAAA,MAAGc,SAAS,CAAC,uBAAuB,CAAAT,QAAA,CAAE8C,IAAI,CAACpB,IAAI,CAAI,CAAC,cACpD/B,IAAA,MAAGc,SAAS,CAAC,yBAAyB,CAAAT,QAAA,CAAE8C,IAAI,CAACnB,KAAK,CAAI,CAAC,cACvD9B,KAAA,QAAKY,SAAS,CAAC,+CAA+C,CAAAT,QAAA,eAC5DL,IAAA,SAAMc,SAAS,CAAC,qBAAqB,CAAAT,QAAA,CAAE8C,IAAI,CAAClB,MAAM,CAAO,CAAC,cAC1D/B,KAAA,SAAMY,SAAS,CAAC,eAAe,CAAAT,QAAA,EAAC,GAAC,CAAC8C,IAAI,CAACjB,OAAO,CAAC,GAAC,EAAM,CAAC,EACpD,CAAC,GANEkB,KAOL,CACN,CAAC,CACC,CAAC,CACS,CAAC,cAGlBpD,IAAA,CAACG,eAAe,EAAAE,QAAA,cACdH,KAAA,QAAKY,SAAS,CAAC,aAAa,CAAAT,QAAA,eAC1BL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,0CAAwC,CAAI,CAAC,cAChFL,IAAA,MAAGc,SAAS,CAAC,4BAA4B,CAAAT,QAAA,CAAC,wDAAsD,CAAG,CAAC,cACpGH,KAAA,QAAKY,SAAS,CAAC,iCAAiC,CAAAT,QAAA,eAC9CH,KAAA,QAAKY,SAAS,CAAC,oGAAoG,CAAAT,QAAA,eACjHL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,eAAa,CAAI,CAAC,cACrDL,IAAA,MAAGc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,oDAAkD,CAAG,CAAC,EACrF,CAAC,cACNH,KAAA,QAAKY,SAAS,CAAC,oGAAoG,CAAAT,QAAA,eACjHL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACzDL,IAAA,MAAGc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,sDAAoD,CAAG,CAAC,EACvF,CAAC,cACNH,KAAA,QAAKY,SAAS,CAAC,oGAAoG,CAAAT,QAAA,eACjHL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACzDL,IAAA,MAAGc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,qDAAmD,CAAG,CAAC,EACtF,CAAC,EACH,CAAC,EACH,CAAC,CACS,CAAC,cAGlBL,IAAA,CAACG,eAAe,EAAAE,QAAA,cACdH,KAAA,QAAKY,SAAS,CAAC,2BAA2B,CAAAT,QAAA,eACxCH,KAAA,QAAKY,SAAS,CAAC,oGAAoG,CAAAT,QAAA,eACjHL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC5DL,IAAA,MAAGc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,kFAAgF,CAAG,CAAC,cACtHL,IAAA,CAACR,IAAI,EAAC6D,EAAE,CAAC,WAAW,CAACvC,SAAS,CAAC,mEAAmE,CAAAT,QAAA,CAAC,iBAAe,CAAM,CAAC,EACtH,CAAC,cACNH,KAAA,QAAKY,SAAS,CAAC,oGAAoG,CAAAT,QAAA,eACjHL,IAAA,OAAIc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,4BAA0B,CAAI,CAAC,cAClEL,IAAA,MAAGc,SAAS,CAAC,oBAAoB,CAAAT,QAAA,CAAC,uGAAqG,CAAG,CAAC,cAC3IL,IAAA,CAACR,IAAI,EAAC6D,EAAE,CAAC,SAAS,CAACvC,SAAS,CAAC,mEAAmE,CAAAT,QAAA,CAAC,iBAAe,CAAM,CAAC,EACpH,CAAC,EACH,CAAC,CACS,CAAC,EACd,CAAC,cACPL,IAAA,CAACF,iBAAiB,GAAE,CAAC,EAClB,CAAC,CAEV,CAAC,CAED,cAAe,CAAA2B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}