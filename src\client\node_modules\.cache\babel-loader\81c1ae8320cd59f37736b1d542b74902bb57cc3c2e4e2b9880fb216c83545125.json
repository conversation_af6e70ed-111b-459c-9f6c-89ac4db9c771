{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst convertKeys = require('./convert-keys');\nconst strToCamelCase = require('./str-to-camel-case');\n\n/**\n * Convert object keys to camel case\n */\nmodule.exports = function toCamelCase(obj, ignored) {\n  return convertKeys(obj, strToCamelCase, ignored);\n};", "map": {"version": 3, "names": ["convertKeys", "require", "strToCamelCase", "module", "exports", "toCamelCase", "obj", "ignored"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/to-camel-case.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst convertKeys = require('./convert-keys');\nconst strToCamelCase = require('./str-to-camel-case');\n\n/**\n * Convert object keys to camel case\n */\nmodule.exports = function toCamelCase(obj, ignored) {\n  return convertKeys(obj, strToCamelCase, ignored);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,qBAAqB,CAAC;;AAErD;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAClD,OAAOP,WAAW,CAACM,GAAG,EAAEJ,cAAc,EAAEK,OAAO,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}