{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const onSubmit = async data => {\n    try {\n      await axios.post('/api/auth/login', data);\n      navigate('/');\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-center text-gray-900\",\n        children: \"Log in to your account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            ...register('username', {\n              required: 'Username is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-600\",\n            children: errors.username.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            ...register('password', {\n              required: 'Password is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-600\",\n            children: errors.password.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"hvZci8bfHEyrOM2r7yZN4BE/YxQ=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useForm", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "onSubmit", "data", "post", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "username", "message", "password", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n\n  const onSubmit = async (data) => {\n    try {\n      await axios.post('/api/auth/login', data);\n      navigate('/');\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100\">\n      <div className=\"w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md\">\n        <h2 className=\"text-2xl font-bold text-center text-gray-900\">Log in to your account</h2>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700\">Username</label>\n            <input\n              type=\"text\"\n              {...register('username', { required: 'Username is required' })}\n              className=\"w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.username && <p className=\"mt-2 text-sm text-red-600\">{errors.username.message}</p>}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n            <input\n              type=\"password\"\n              {...register('password', { required: 'Password is required' })}\n              className=\"w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.password && <p className=\"mt-2 text-sm text-red-600\">{errors.password.message}</p>}\n          </div>\n          <div>\n            <button\n              type=\"submit\"\n              className=\"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n            >\n              Log in\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnE,MAAMW,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMZ,KAAK,CAACa,IAAI,CAAC,iBAAiB,EAAED,IAAI,CAAC;MACzCF,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,oBACEX,OAAA;IAAKa,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEd,OAAA;MAAKa,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAC1Ed,OAAA;QAAIa,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxFlB,OAAA;QAAMQ,QAAQ,EAAEJ,YAAY,CAACI,QAAQ,CAAE;QAACK,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC3Dd,OAAA;UAAAc,QAAA,gBACEd,OAAA;YAAOa,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ElB,OAAA;YACEmB,IAAI,EAAC,MAAM;YAAA,GACPhB,QAAQ,CAAC,UAAU,EAAE;cAAEiB,QAAQ,EAAE;YAAuB,CAAC,CAAC;YAC9DP,SAAS,EAAC;UAAiH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,EACDZ,MAAM,CAACe,QAAQ,iBAAIrB,OAAA;YAAGa,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAER,MAAM,CAACe,QAAQ,CAACC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNlB,OAAA;UAAAc,QAAA,gBACEd,OAAA;YAAOa,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ElB,OAAA;YACEmB,IAAI,EAAC,UAAU;YAAA,GACXhB,QAAQ,CAAC,UAAU,EAAE;cAAEiB,QAAQ,EAAE;YAAuB,CAAC,CAAC;YAC9DP,SAAS,EAAC;UAAiH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,EACDZ,MAAM,CAACiB,QAAQ,iBAAIvB,OAAA;YAAGa,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAER,MAAM,CAACiB,QAAQ,CAACD;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNlB,OAAA;UAAAc,QAAA,eACEd,OAAA;YACEmB,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gKAAgK;YAAAC,QAAA,EAC3K;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAhDID,KAAK;EAAA,QACiDL,OAAO,EAChDE,WAAW;AAAA;AAAA0B,EAAA,GAFxBvB,KAAK;AAkDX,eAAeA,KAAK;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}