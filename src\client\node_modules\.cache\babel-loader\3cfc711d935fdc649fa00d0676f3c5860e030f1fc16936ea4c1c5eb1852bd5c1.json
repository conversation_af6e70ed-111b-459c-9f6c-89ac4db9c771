{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\CheckEmail.js\";\nimport React, { useEffect, useState } from 'react';\nimport { Link, useSearchParams } from 'react-router-dom';\nimport { FaEnvelopeOpenText, FaCheckCircle } from 'react-icons/fa';\nimport { auth } from '../firebase';\nimport { applyActionCode } from 'firebase/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckEmail = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",\n      children: [/*#__PURE__*/_jsxDEV(FaEnvelopeOpenText, {\n        className: \"text-6xl text-blue-500 mx-auto mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"Check Your Inbox\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-300 mb-6\",\n        children: \"We have sent a verification link to your email address. Please click the link to complete your registration.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400 text-sm\",\n        children: \"Didn't receive the email? Check your spam folder or try signing up again.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"mt-8 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\",\n        children: \"Back to Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = CheckEmail;\nexport default CheckEmail;\nvar _c;\n$RefreshReg$(_c, \"CheckEmail\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "useSearchParams", "FaEnvelopeOpenText", "FaCheckCircle", "auth", "applyActionCode", "jsxDEV", "_jsxDEV", "CheckEmail", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/CheckEmail.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link, useSearchParams } from 'react-router-dom';\nimport { FaEnvelopeOpenText, FaCheckCircle } from 'react-icons/fa';\nimport { auth } from '../firebase';\nimport { applyActionCode } from 'firebase/auth';\n\nconst CheckEmail = () => {\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n        <FaEnvelopeOpenText className=\"text-6xl text-blue-500 mx-auto mb-6\" />\n        <h1 className=\"text-3xl font-bold mb-4\">Check Your Inbox</h1>\n        <p className=\"text-gray-300 mb-6\">\n          We have sent a verification link to your email address. Please click the link to complete your registration.\n        </p>\n        <p className=\"text-gray-400 text-sm\">\n          Didn't receive the email? Check your spam folder or try signing up again.\n        </p>\n        <Link to=\"/login\" className=\"mt-8 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\">\n          Back to Login\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default CheckEmail;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,eAAe,QAAQ,kBAAkB;AACxD,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,gBAAgB;AAClE,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAAKE,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFH,OAAA;MAAKE,SAAS,EAAC,kEAAkE;MAAAC,QAAA,gBAC/EH,OAAA,CAACL,kBAAkB;QAACO,SAAS,EAAC;MAAqC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtEP,OAAA;QAAIE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DP,OAAA;QAAGE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAGE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA,CAACP,IAAI;QAACe,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,mHAAmH;QAAAC,QAAA,EAAC;MAEhJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAlBIR,UAAU;AAoBhB,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}