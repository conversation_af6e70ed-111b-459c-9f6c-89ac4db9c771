{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { auth } from '../firebase';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaBitcoin, FaChartLine, FaShieldAlt, FaUserTie, FaArrowRight } from 'react-icons/fa';\nimport bitcoinHero from '../assets/images/bitcoin-icon.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, currentUser => {\n      if (currentUser) {\n        setUser(currentUser);\n      } else {\n        setUser(null);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pt-16\",\n    children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-custom\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-white\",\n            children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold\",\n              children: user.displayName ? user.displayName.split(' ')[0] : 'Trader'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 31\n            }, this), \"! \\uD83D\\uDC4B\", user.emailVerified ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\",\n              children: \"\\u2713 Email Verified\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\",\n              children: \"Please verify your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/dashboard\",\n            className: \"text-sm bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors\",\n            children: \"Go to Dashboard \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InstagramCTA, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CTASection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatsSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ServicesSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n\n// Hero Section Component\n_s(Home, \"5s2qRsV95gTJBmaaTh11GoxYeGE=\");\n_c = Home;\nconst HeroSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative bg-primary-dark text-white overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:w-1/2 mb-10 md:mb-0\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: \"Your Trusted Bitcoin Exchange & Trading Partner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          className: \"text-lg md:text-xl text-gray-300 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          children: \"Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. We make Bitcoin trading accessible for everyone.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"flex flex-wrap gap-4\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"btn-primary inline-flex items-center text-lg px-8 py-3\",\n            children: [\"Get Started \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 27\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://wa.me/2348163309355\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"md:w-1/2 flex justify-center\",\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.7\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotateY: [0, 360]\n            },\n            transition: {\n              duration: 20,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"relative z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: bitcoinHero,\n              alt: \"Bitcoin\",\n              className: \"w-64 h-64 md:w-80 md:h-80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n\n// Features Section Component with Scroll Animation\n_c2 = HeroSection;\nconst FeaturesSection = () => {\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(FaBitcoin, {\n      className: \"text-4xl text-yellow-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this),\n    title: \"Secure Bitcoin Exchange\",\n    description: \"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {\n      className: \"text-4xl text-accent\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this),\n    title: \"Advanced Trading Tools\",\n    description: \"Access professional trading tools and real-time market data for informed decisions.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n      className: \"text-4xl text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this),\n    title: \"Maximum Security\",\n    description: \"Your assets are protected with industry-leading security measures and encryption.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaUserTie, {\n      className: \"text-4xl text-purple-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this),\n    title: \"Expert Support\",\n    description: \"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        title: \"Why Choose BlazeTrade\",\n        subtitle: \"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCard, {\n          icon: feature.icon,\n          title: feature.title,\n          description: feature.description,\n          index: index\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n\n// Services Section Component\n_c3 = FeaturesSection;\nconst ServicesSection = () => {\n  const services = [{\n    title: \"Bitcoin Exchange\",\n    description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",\n    link: \"/services#bitcoin-exchange\"\n  }, {\n    title: \"Crypto Trading\",\n    description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",\n    link: \"/services#crypto-trading\"\n  }, {\n    title: \"Market Analysis\",\n    description: \"Get detailed market insights and analysis to make informed trading decisions.\",\n    link: \"/services#market-analysis\"\n  }, {\n    title: \"Portfolio Management\",\n    description: \"Professional management of your cryptocurrency portfolio for optimal returns.\",\n    link: \"/services#portfolio-management\"\n  }, {\n    title: \"Security Solutions\",\n    description: \"Advanced security solutions to protect your digital assets and investments.\",\n    link: \"/services#security-solutions\"\n  }, {\n    title: \"Consulting Services\",\n    description: \"Expert consulting services for individuals and businesses entering the crypto space.\",\n    link: \"/services#consulting\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        title: \"Our Services\",\n        subtitle: \"Comprehensive cryptocurrency services tailored to your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(ServiceCard, {\n          title: service.title,\n          description: service.description,\n          link: service.link,\n          index: index\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n};\n\n// Stats Section Component\n_c4 = ServicesSection;\nconst StatsSection = () => {\n  _s2();\n  const stats = [{\n    value: \"10K+\",\n    label: \"Active Users\"\n  }, {\n    value: \"$250M+\",\n    label: \"Monthly Volume\"\n  }, {\n    value: \"99.9%\",\n    label: \"Uptime\"\n  }, {\n    value: \"24/7\",\n    label: \"Support\"\n  }];\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-16 bg-primary-dark text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0\n          },\n          visible: {\n            opacity: 1,\n            transition: {\n              staggerChildren: 0.2\n            }\n          }\n        },\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: {\n            hidden: {\n              opacity: 0,\n              y: 20\n            },\n            visible: {\n              opacity: 1,\n              y: 0,\n              transition: {\n                duration: 0.6\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl md:text-5xl font-bold mb-2\",\n            children: stat.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300\",\n            children: stat.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n\n// CTA Section Component\n_s2(StatsSection, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c5 = StatsSection;\nconst CTASection = () => {\n  _s3();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0,\n            y: 20\n          },\n          visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n              duration: 0.6\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4\",\n          children: \"Ready to trade?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\",\n          children: \"Hit the link below to start trading your cryptocurrencies with Blaze Trade.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://wa.me/2348163309355\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center btn-primary text-lg px-8 py-3\",\n          children: [\"Trade Now \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 5\n  }, this);\n};\n\n// Reusable Section Header Component\n_s3(CTASection, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c6 = CTASection;\nconst SectionHeader = ({\n  title,\n  subtitle\n}) => {\n  _s4();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"text-center max-w-3xl mx-auto\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.6\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-lg text-gray-600\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 385,\n    columnNumber: 5\n  }, this);\n};\n\n// Feature Card Component with Animation\n_s4(SectionHeader, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c7 = SectionHeader;\nconst FeatureCard = ({\n  icon,\n  title,\n  description,\n  index\n}) => {\n  _s5();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-semibold mb-2 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 416,\n    columnNumber: 5\n  }, this);\n};\n\n// Service Card Component with Animation\n_s5(FeatureCard, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c8 = FeatureCard;\nconst ServiceCard = ({\n  title,\n  description,\n  link,\n  index\n}) => {\n  _s6();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-semibold mb-3 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 mb-4\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: link,\n      className: \"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\",\n      children: [\"Learn More \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n        className: \"ml-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 20\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 455,\n    columnNumber: 5\n  }, this);\n};\n\n// Instagram CTA Section\n_s6(ServiceCard, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c9 = ServiceCard;\nconst InstagramCTA = () => {\n  _s7();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"text-center max-w-3xl mx-auto\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0,\n            y: 20\n          },\n          visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n              duration: 0.6\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",\n          children: \"Follow Us on Instagram\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-8\",\n          children: \"Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center btn-secondary text-lg px-8 py-3\",\n          children: \"@blaze__trade\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 499,\n    columnNumber: 5\n  }, this);\n};\n_s7(InstagramCTA, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c0 = InstagramCTA;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c2, \"HeroSection\");\n$RefreshReg$(_c3, \"FeaturesSection\");\n$RefreshReg$(_c4, \"ServicesSection\");\n$RefreshReg$(_c5, \"StatsSection\");\n$RefreshReg$(_c6, \"CTASection\");\n$RefreshReg$(_c7, \"SectionHeader\");\n$RefreshReg$(_c8, \"FeatureCard\");\n$RefreshReg$(_c9, \"ServiceCard\");\n$RefreshReg$(_c0, \"InstagramCTA\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "auth", "onAuthStateChanged", "motion", "useAnimation", "useInView", "FaBitcoin", "FaChartLine", "FaShieldAlt", "FaUserTie", "FaArrowRight", "bitcoinHero", "jsxDEV", "_jsxDEV", "Home", "_s", "user", "setUser", "unsubscribe", "currentUser", "className", "children", "displayName", "split", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "emailVerified", "to", "HeroSection", "InstagramCTA", "CTASection", "FeaturesSection", "StatsSection", "ServicesSection", "_c", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "href", "target", "rel", "scale", "rotateY", "repeat", "Infinity", "ease", "src", "alt", "_c2", "features", "icon", "title", "description", "SectionHeader", "subtitle", "map", "feature", "index", "FeatureCard", "_c3", "services", "link", "service", "ServiceCard", "_c4", "_s2", "stats", "value", "label", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "stat", "_c5", "_s3", "_c6", "_s4", "_c7", "_s5", "_c8", "_s6", "_c9", "_s7", "_c0", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Home.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { auth } from '../firebase';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaBitcoin, FaChartLine, FaShieldAlt, FaUserTie, FaArrowRight } from 'react-icons/fa';\nimport bitcoinHero from '../assets/images/bitcoin-icon.png';\n\nconst Home = () => {\n  const [user, setUser] = useState(null);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\n      if (currentUser) {\n        setUser(currentUser);\n      } else {\n        setUser(null);\n      }\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  return (\n    <div className=\"pt-16\">\n      {/* Welcome Message */}\n      {user && (\n        <div className=\"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\">\n          <div className=\"container-custom\">\n            <div className=\"flex items-center justify-between\">\n              <p className=\"text-lg text-white\">\n                Welcome back, <span className=\"font-bold\">\n                  {user.displayName ? user.displayName.split(' ')[0] : 'Trader'}\n                </span>! 👋\n                {user.emailVerified ? (\n                  <span className=\"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\">\n                    ✓ Email Verified\n                  </span>\n                ) : (\n                  <span className=\"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\">\n                    Please verify your email\n                  </span>\n                )}\n              </p>\n              <Link \n                to=\"/dashboard\" \n                className=\"text-sm bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors\"\n              >\n                Go to Dashboard →\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {/* Hero Section */}\n      <HeroSection />\n\n      {/* Instagram CTA Section */}\n      <InstagramCTA />\n\n      {/* CTA Section */}\n      <CTASection />\n      \n      {/* Features Section */}\n      <FeaturesSection />\n      \n      {/* Stats Section */}\n      <StatsSection />\n\n      {/* Services Section */}\n      <ServicesSection />\n    </div>\n  );\n};\n\n// Hero Section Component\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-primary-dark text-white overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"></div>\n        <div className=\"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\">\n        {/* Hero Content */}\n        <div className=\"md:w-1/2 mb-10 md:mb-0\">\n          <motion.h1 \n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            Your Trusted Bitcoin Exchange & Trading Partner\n          </motion.h1>\n          <motion.p \n            className=\"text-lg md:text-xl text-gray-300 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. \n            We make Bitcoin trading accessible for everyone.\n          </motion.p>\n          <motion.div \n            className=\"flex flex-wrap gap-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            <Link \n              to=\"/signup\"\n              className=\"btn-primary inline-flex items-center text-lg px-8 py-3\"\n            >\n              Get Started <FaArrowRight className=\"ml-2\" />\n            </Link>\n            <a \n              href=\"https://wa.me/2348163309355\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\"\n            >\n              Contact Us\n            </a>\n          </motion.div>\n        </div>\n        \n        {/* Hero Image */}\n        <motion.div \n          className=\"md:w-1/2 flex justify-center\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.7 }}\n        >\n          <div className=\"relative\">\n            <div className=\"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"></div>\n            <motion.div\n              animate={{ \n                rotateY: [0, 360],\n              }}\n              transition={{ \n                duration: 20,\n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              className=\"relative z-10\"\n            >\n              <img src={bitcoinHero} alt=\"Bitcoin\" className=\"w-64 h-64 md:w-80 md:h-80\" />\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Features Section Component with Scroll Animation\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: <FaBitcoin className=\"text-4xl text-yellow-500\" />,\n      title: \"Secure Bitcoin Exchange\",\n      description: \"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"\n    },\n    {\n      icon: <FaChartLine className=\"text-4xl text-accent\" />,\n      title: \"Advanced Trading Tools\",\n      description: \"Access professional trading tools and real-time market data for informed decisions.\"\n    },\n    {\n      icon: <FaShieldAlt className=\"text-4xl text-green-500\" />,\n      title: \"Maximum Security\",\n      description: \"Your assets are protected with industry-leading security measures and encryption.\"\n    },\n    {\n      icon: <FaUserTie className=\"text-4xl text-purple-500\" />,\n      title: \"Expert Support\",\n      description: \"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Why Choose BlazeTrade\" \n          subtitle=\"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\">\n          {features.map((feature, index) => (\n            <FeatureCard \n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Services Section Component\nconst ServicesSection = () => {\n  const services = [\n    {\n      title: \"Bitcoin Exchange\",\n      description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",\n      link: \"/services#bitcoin-exchange\"\n    },\n    {\n      title: \"Crypto Trading\",\n      description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",\n      link: \"/services#crypto-trading\"\n    },\n    {\n      title: \"Market Analysis\",\n      description: \"Get detailed market insights and analysis to make informed trading decisions.\",\n      link: \"/services#market-analysis\"\n    },\n    {\n      title: \"Portfolio Management\",\n      description: \"Professional management of your cryptocurrency portfolio for optimal returns.\",\n      link: \"/services#portfolio-management\"\n    },\n    {\n      title: \"Security Solutions\",\n      description: \"Advanced security solutions to protect your digital assets and investments.\",\n      link: \"/services#security-solutions\"\n    },\n    {\n      title: \"Consulting Services\",\n      description: \"Expert consulting services for individuals and businesses entering the crypto space.\",\n      link: \"/services#consulting\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Our Services\" \n          subtitle=\"Comprehensive cryptocurrency services tailored to your needs\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\">\n          {services.map((service, index) => (\n            <ServiceCard \n              key={index}\n              title={service.title}\n              description={service.description}\n              link={service.link}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Stats Section Component\nconst StatsSection = () => {\n  const stats = [\n    { value: \"10K+\", label: \"Active Users\" },\n    { value: \"$250M+\", label: \"Monthly Volume\" },\n    { value: \"99.9%\", label: \"Uptime\" },\n    { value: \"24/7\", label: \"Support\" }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"py-16 bg-primary-dark text-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.2\n              }\n            }\n          }}\n        >\n          {stats.map((stat, index) => (\n            <motion.div \n              key={index}\n              variants={{\n                hidden: { opacity: 0, y: 20 },\n                visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n              }}\n            >\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{stat.value}</div>\n              <div className=\"text-gray-300\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// CTA Section Component\nconst CTASection = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Ready to trade?</h2>\n          <p className=\"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Hit the link below to start trading your cryptocurrencies with Blaze Trade.\n          </p>\n          <a \n            href=\"https://wa.me/2348163309355\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-primary text-lg px-8 py-3\"\n          >\n            Trade Now <FaArrowRight className=\"ml-2\" />\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Reusable Section Header Component\nconst SectionHeader = ({ title, subtitle }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"text-center max-w-3xl mx-auto\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n      }}\n    >\n      <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">{title}</h2>\n      <p className=\"text-lg text-gray-600\">{subtitle}</p>\n    </motion.div>\n  );\n};\n\n// Feature Card Component with Animation\nconst FeatureCard = ({ icon, title, description, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"mb-4\">{icon}</div>\n      <h3 className=\"text-xl font-semibold mb-2 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600\">{description}</p>\n    </motion.div>\n  );\n};\n\n// Service Card Component with Animation\nconst ServiceCard = ({ title, description, link, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <h3 className=\"text-xl font-semibold mb-3 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600 mb-4\">{description}</p>\n      <Link \n        to={link} \n        className=\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\"\n      >\n        Learn More <FaArrowRight className=\"ml-2\" />\n      </Link>\n    </motion.div>\n  );\n};\n\n// Instagram CTA Section\nconst InstagramCTA = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"text-center max-w-3xl mx-auto\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Follow Us on Instagram</h2>\n          <p className=\"text-lg text-gray-600 mb-8\">\n            Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\n          </p>\n          <a \n            href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-secondary text-lg px-8 py-3\"\n          >\n            @blaze__trade\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Home;"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACpD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAC7F,OAAOC,WAAW,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMoB,WAAW,GAAGhB,kBAAkB,CAACD,IAAI,EAAGkB,WAAW,IAAK;MAC5D,IAAIA,WAAW,EAAE;QACfF,OAAO,CAACE,WAAW,CAAC;MACtB,CAAC,MAAM;QACLF,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC,CAAC;IAEF,OAAO,MAAMC,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA;IAAKO,SAAS,EAAC,OAAO;IAAAC,QAAA,GAEnBL,IAAI,iBACHH,OAAA;MAAKO,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9ER,OAAA;QAAKO,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BR,OAAA;UAAKO,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDR,OAAA;YAAGO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,gBAClB,eAAAR,OAAA;cAAMO,SAAS,EAAC,WAAW;cAAAC,QAAA,EACtCL,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACM,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,kBACP,EAACX,IAAI,CAACY,aAAa,gBACjBf,OAAA;cAAMO,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAAC;YAErF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEPd,OAAA;cAAMO,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACJd,OAAA,CAACb,IAAI;YACH6B,EAAE,EAAC,YAAY;YACfT,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDd,OAAA,CAACiB,WAAW;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGfd,OAAA,CAACkB,YAAY;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhBd,OAAA,CAACmB,UAAU;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdd,OAAA,CAACoB,eAAe;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBd,OAAA,CAACqB,YAAY;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhBd,OAAA,CAACsB,eAAe;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEV,CAAC;;AAED;AAAAZ,EAAA,CApEMD,IAAI;AAAAsB,EAAA,GAAJtB,IAAI;AAqEV,MAAMgB,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACEjB,OAAA;IAASO,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAEtER,OAAA;MAAKO,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CR,OAAA;QAAKO,SAAS,EAAC;MAAwF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9Gd,OAAA;QAAKO,SAAS,EAAC;MAAiF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CAAC,eAENd,OAAA;MAAKO,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBAEnGR,OAAA;QAAKO,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCR,OAAA,CAACV,MAAM,CAACkC,EAAE;UACRjB,SAAS,EAAC,iDAAiD;UAC3DkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAtB,QAAA,EAC/B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZd,OAAA,CAACV,MAAM,CAACyC,CAAC;UACPxB,SAAS,EAAC,uCAAuC;UACjDkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,EAC3C;QAGD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACXd,OAAA,CAACV,MAAM,CAAC2C,GAAG;UACT1B,SAAS,EAAC,sBAAsB;UAChCkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,gBAE1CR,OAAA,CAACb,IAAI;YACH6B,EAAE,EAAC,SAAS;YACZT,SAAS,EAAC,wDAAwD;YAAAC,QAAA,GACnE,cACa,eAAAR,OAAA,CAACH,YAAY;cAACU,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACPd,OAAA;YACEkC,IAAI,EAAC,6BAA6B;YAClCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB7B,SAAS,EAAC,kHAAkH;YAAAC,QAAA,EAC7H;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNd,OAAA,CAACV,MAAM,CAAC2C,GAAG;QACT1B,SAAS,EAAC,8BAA8B;QACxCkB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEW,KAAK,EAAE;QAAI,CAAE;QACpCT,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEW,KAAK,EAAE;QAAE,CAAE;QAClCR,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAtB,QAAA,eAE9BR,OAAA;UAAKO,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBR,OAAA;YAAKO,SAAS,EAAC;UAA2I;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjKd,OAAA,CAACV,MAAM,CAAC2C,GAAG;YACTL,OAAO,EAAE;cACPU,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG;YAClB,CAAE;YACFT,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZS,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR,CAAE;YACFlC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAEzBR,OAAA;cAAK0C,GAAG,EAAE5C,WAAY;cAAC6C,GAAG,EAAC,SAAS;cAACpC,SAAS,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA8B,GAAA,GAjFM3B,WAAW;AAkFjB,MAAMG,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMyB,QAAQ,GAAG,CACf;IACEC,IAAI,eAAE9C,OAAA,CAACP,SAAS;MAACc,SAAS,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDiC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAE9C,OAAA,CAACN,WAAW;MAACa,SAAS,EAAC;IAAsB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtDiC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAE9C,OAAA,CAACL,WAAW;MAACY,SAAS,EAAC;IAAyB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDiC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAE9C,OAAA,CAACJ,SAAS;MAACW,SAAS,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDiC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEhD,OAAA;IAASO,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrCR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BR,OAAA,CAACiD,aAAa;QACZF,KAAK,EAAC,uBAAuB;QAC7BG,QAAQ,EAAC;MAA6G;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC,eAEFd,OAAA;QAAKO,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEqC,QAAQ,CAACM,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BrD,OAAA,CAACsD,WAAW;UAEVR,IAAI,EAAEM,OAAO,CAACN,IAAK;UACnBC,KAAK,EAAEK,OAAO,CAACL,KAAM;UACrBC,WAAW,EAAEI,OAAO,CAACJ,WAAY;UACjCK,KAAK,EAAEA;QAAM,GAJRA,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAAyC,GAAA,GAhDMnC,eAAe;AAiDrB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMkC,QAAQ,GAAG,CACf;IACET,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,sFAAsF;IACnGS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0EAA0E;IACvFS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,+EAA+E;IAC5FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,+EAA+E;IAC5FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,6EAA6E;IAC1FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,sFAAsF;IACnGS,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEzD,OAAA;IAASO,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BR,OAAA,CAACiD,aAAa;QACZF,KAAK,EAAC,cAAc;QACpBG,QAAQ,EAAC;MAA8D;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAEFd,OAAA;QAAKO,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEgD,QAAQ,CAACL,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3BrD,OAAA,CAAC2D,WAAW;UAEVZ,KAAK,EAAEW,OAAO,CAACX,KAAM;UACrBC,WAAW,EAAEU,OAAO,CAACV,WAAY;UACjCS,IAAI,EAAEC,OAAO,CAACD,IAAK;UACnBJ,KAAK,EAAEA;QAAM,GAJRA,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA8C,GAAA,GA1DMtC,eAAe;AA2DrB,MAAMD,YAAY,GAAGA,CAAA,KAAM;EAAAwC,GAAA;EACzB,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAe,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC5C;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,CACpC;EAED,MAAMC,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA;IAASO,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eACnDR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BR,OAAA,CAACV,MAAM,CAAC2C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,mDAAmD;QAC7DkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE;UAAE,CAAC;UACtB+C,OAAO,EAAE;YACP/C,OAAO,EAAE,CAAC;YACVG,UAAU,EAAE;cACV6C,eAAe,EAAE;YACnB;UACF;QACF,CAAE;QAAAlE,QAAA,EAEDsD,KAAK,CAACX,GAAG,CAAC,CAACwB,IAAI,EAAEtB,KAAK,kBACrBrD,OAAA,CAACV,MAAM,CAAC2C,GAAG;UAETsC,QAAQ,EAAE;YACRC,MAAM,EAAE;cAAE9C,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAC;YAC7B8C,OAAO,EAAE;cAAE/C,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;cAAEE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI;YAAE;UAC7D,CAAE;UAAAtB,QAAA,gBAEFR,OAAA;YAAKO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAEmE,IAAI,CAACZ;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEd,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEmE,IAAI,CAACX;UAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAP5CuC,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA+C,GAAA,CAxDMxC,YAAY;EAAA,QAQC9B,YAAY,EACPC,SAAS;AAAA;AAAAoF,GAAA,GAT3BvD,YAAY;AAyDlB,MAAMF,UAAU,GAAGA,CAAA,KAAM;EAAA0D,GAAA;EACvB,MAAMZ,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA;IAASO,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrCR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BR,OAAA,CAACV,MAAM,CAAC2C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,gEAAgE;QAC1EkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAC;UAC7B8C,OAAO,EAAE;YAAE/C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI;UAAE;QAC7D,CAAE;QAAAtB,QAAA,gBAEFR,OAAA;UAAIO,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEd,OAAA;UAAGO,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJd,OAAA;UACEkC,IAAI,EAAC,6BAA6B;UAClCC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,wDAAwD;UAAAC,QAAA,GACnE,YACW,eAAAR,OAAA,CAACH,YAAY;YAACU,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA+D,GAAA,CA5CM1D,UAAU;EAAA,QACG5B,YAAY,EACPC,SAAS;AAAA;AAAAsF,GAAA,GAF3B3D,UAAU;AA6ChB,MAAM8B,aAAa,GAAGA,CAAC;EAAEF,KAAK;EAAEG;AAAS,CAAC,KAAK;EAAA6B,GAAA;EAC7C,MAAMd,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA,CAACV,MAAM,CAAC2C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,+BAA+B;IACzCkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QAAE/C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;IAC7D,CAAE;IAAAtB,QAAA,gBAEFR,OAAA;MAAIO,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAClFd,OAAA;MAAGO,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAE0C;IAAQ;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC;AAEjB,CAAC;;AAED;AAAAiE,GAAA,CA9BM9B,aAAa;EAAA,QACA1D,YAAY,EACPC,SAAS;AAAA;AAAAwF,GAAA,GAF3B/B,aAAa;AA+BnB,MAAMK,WAAW,GAAGA,CAAC;EAAER,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEK;AAAM,CAAC,KAAK;EAAA4B,GAAA;EAC3D,MAAMhB,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA,CAACV,MAAM,CAAC2C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,kFAAkF;IAC5FkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QACP/C,OAAO,EAAE,CAAC;QACVC,CAAC,EAAE,CAAC;QACJE,UAAU,EAAE;UACVC,QAAQ,EAAE,GAAG;UACbE,KAAK,EAAEqB,KAAK,GAAG;QACjB;MACF;IACF,CAAE;IAAA7C,QAAA,gBAEFR,OAAA;MAAKO,SAAS,EAAC,MAAM;MAAAC,QAAA,EAAEsC;IAAI;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAClCd,OAAA;MAAIO,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACzEd,OAAA;MAAGO,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEwC;IAAW;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpC,CAAC;AAEjB,CAAC;;AAED;AAAAmE,GAAA,CAtCM3B,WAAW;EAAA,QACE/D,YAAY,EACPC,SAAS;AAAA;AAAA0F,GAAA,GAF3B5B,WAAW;AAuCjB,MAAMK,WAAW,GAAGA,CAAC;EAAEZ,KAAK;EAAEC,WAAW;EAAES,IAAI;EAAEJ;AAAM,CAAC,KAAK;EAAA8B,GAAA;EAC3D,MAAMlB,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA,CAACV,MAAM,CAAC2C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,iGAAiG;IAC3GkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QACP/C,OAAO,EAAE,CAAC;QACVC,CAAC,EAAE,CAAC;QACJE,UAAU,EAAE;UACVC,QAAQ,EAAE,GAAG;UACbE,KAAK,EAAEqB,KAAK,GAAG;QACjB;MACF;IACF,CAAE;IAAA7C,QAAA,gBAEFR,OAAA;MAAIO,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACzEd,OAAA;MAAGO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAEwC;IAAW;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnDd,OAAA,CAACb,IAAI;MACH6B,EAAE,EAAEyC,IAAK;MACTlD,SAAS,EAAC,kGAAkG;MAAAC,QAAA,GAC7G,aACY,eAAAR,OAAA,CAACH,YAAY;QAACU,SAAS,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;;AAED;AAAAqE,GAAA,CA3CMxB,WAAW;EAAA,QACEpE,YAAY,EACPC,SAAS;AAAA;AAAA4F,GAAA,GAF3BzB,WAAW;AA4CjB,MAAMzC,YAAY,GAAGA,CAAA,KAAM;EAAAmE,GAAA;EACzB,MAAMpB,QAAQ,GAAG1E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC2E,GAAG,EAAEC,MAAM,CAAC,GAAG3E,SAAS,CAAC;IAC9B4E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFpF,SAAS,CAAC,MAAM;IACd,IAAIkF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACEnE,OAAA;IAASO,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCR,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BR,OAAA,CAACV,MAAM,CAAC2C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,+BAA+B;QACzCkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAC;UAC7B8C,OAAO,EAAE;YAAE/C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI;UAAE;QAC7D,CAAE;QAAAtB,QAAA,gBAEFR,OAAA;UAAIO,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjGd,OAAA;UAAGO,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJd,OAAA;UACEkC,IAAI,EAAC,sDAAsD;UAC3DC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACrE;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACuE,GAAA,CA1CInE,YAAY;EAAA,QACC3B,YAAY,EACPC,SAAS;AAAA;AAAA8F,GAAA,GAF3BpE,YAAY;AA4ClB,eAAejB,IAAI;AAAC,IAAAsB,EAAA,EAAAqB,GAAA,EAAAW,GAAA,EAAAK,GAAA,EAAAgB,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAhE,EAAA;AAAAgE,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}