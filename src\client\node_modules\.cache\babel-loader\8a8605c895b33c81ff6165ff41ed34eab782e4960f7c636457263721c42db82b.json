{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport AuthHeader from '../components/AuthHeader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const onSubmit = async data => {\n    try {\n      const response = await axios.post('/api/auth/login', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center min-h-screen bg-gray-900 text-white pt-16\",\n    children: [/*#__PURE__*/_jsxDEV(AuthHeader, {\n      title: \"Log In to Your Account\",\n      subtitle: \"Welcome back! Please enter your details to access your account.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md p-8 mt-8 space-y-6 bg-gray-800 rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            ...register('username', {\n              required: 'Username is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.username.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            ...register('password', {\n              required: 'Password is required'\n            }),\n            className: \"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-500\",\n            children: errors.password.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-center text-gray-400\",\n        children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/signup\",\n          className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n          children: \"Sign up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"hvZci8bfHEyrOM2r7yZN4BE/YxQ=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useForm", "axios", "Link", "useNavigate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "onSubmit", "data", "response", "post", "localStorage", "setItem", "token", "error", "console", "className", "children", "title", "subtitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "username", "message", "password", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React from 'react';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport AuthHeader from '../components/AuthHeader';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n\n  const onSubmit = async (data) => {\n    try {\n      const response = await axios.post('/api/auth/login', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      console.error('Login failed:', error);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center min-h-screen bg-gray-900 text-white pt-16\">\n      <AuthHeader \n        title=\"Log In to Your Account\"\n        subtitle=\"Welcome back! Please enter your details to access your account.\"\n      />\n      <div className=\"w-full max-w-md p-8 mt-8 space-y-6 bg-gray-800 rounded-lg shadow-md\">\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Username</label>\n            <input\n              type=\"text\"\n              {...register('username', { required: 'Username is required' })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300\">Password</label>\n            <input\n              type=\"password\"\n              {...register('password', { required: 'Password is required' })}\n              className=\"w-full px-3 py-2 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500\"\n            />\n            {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n          </div>\n          <div>\n            <button\n              type=\"submit\"\n              className=\"w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n            >\n              Log in\n            </button>\n          </div>\n        </form>\n        <p className=\"text-sm text-center text-gray-400\">\n          Don't have an account?{' '}\n          <Link to=\"/signup\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n            Sign up\n          </Link>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnE,MAAMa,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,iBAAiB,EAAEF,IAAI,CAAC;MAC1DG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACD,IAAI,CAACK,KAAK,CAAC;MAClDP,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,oBACEf,OAAA;IAAKiB,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBACnFlB,OAAA,CAACF,UAAU;MACTqB,KAAK,EAAC,wBAAwB;MAC9BC,QAAQ,EAAC;IAAiE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eACFxB,OAAA;MAAKiB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAClFlB,OAAA;QAAMQ,QAAQ,EAAEJ,YAAY,CAACI,QAAQ,CAAE;QAACS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC3DlB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ExB,OAAA;YACEyB,IAAI,EAAC,MAAM;YAAA,GACPtB,QAAQ,CAAC,UAAU,EAAE;cAAEuB,QAAQ,EAAE;YAAuB,CAAC,CAAC;YAC9DT,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAACqB,QAAQ,iBAAI3B,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAACqB,QAAQ,CAACC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAOiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ExB,OAAA;YACEyB,IAAI,EAAC,UAAU;YAAA,GACXtB,QAAQ,CAAC,UAAU,EAAE;cAAEuB,QAAQ,EAAE;YAAuB,CAAC,CAAC;YAC9DT,SAAS,EAAC;UAA6H;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxI,CAAC,EACDlB,MAAM,CAACuB,QAAQ,iBAAI7B,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEZ,MAAM,CAACuB,QAAQ,CAACD;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNxB,OAAA;UAAAkB,QAAA,eACElB,OAAA;YACEyB,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,gKAAgK;YAAAC,QAAA,EAC3K;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACPxB,OAAA;QAAGiB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,GAAC,wBACzB,EAAC,GAAG,eAC1BlB,OAAA,CAACJ,IAAI;UAACkC,EAAE,EAAC,SAAS;UAACb,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAEjF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA1DID,KAAK;EAAA,QACiDP,OAAO,EAChDG,WAAW;AAAA;AAAAkC,EAAA,GAFxB9B,KAAK;AA4DX,eAAeA,KAAK;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}