{"ast": null, "code": "import { useEffect } from 'react';\nimport { addDomEvent } from './add-dom-event.mjs';\n\n/**\n * Attaches an event listener directly to the provided DOM element.\n *\n * Bypassing <PERSON>act's event system can be desirable, for instance when attaching non-passive\n * event handlers.\n *\n * ```jsx\n * const ref = useRef(null)\n *\n * useDomEvent(ref, 'wheel', onWheel, { passive: false })\n *\n * return <div ref={ref} />\n * ```\n *\n * @param ref - React.RefObject that's been provided to the element you want to bind the listener to.\n * @param eventName - Name of the event you want listen for.\n * @param handler - Function to fire when receiving the event.\n * @param options - Options to pass to `Event.addEventListener`.\n *\n * @public\n */\nfunction useDomEvent(ref, eventName, handler, options) {\n  useEffect(() => {\n    const element = ref.current;\n    if (handler && element) {\n      return addDomEvent(element, eventName, handler, options);\n    }\n  }, [ref, eventName, handler, options]);\n}\nexport { useDomEvent };", "map": {"version": 3, "names": ["useEffect", "addDomEvent", "useDomEvent", "ref", "eventName", "handler", "options", "element", "current"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/events/use-dom-event.mjs"], "sourcesContent": ["import { useEffect } from 'react';\nimport { addDomEvent } from './add-dom-event.mjs';\n\n/**\n * Attaches an event listener directly to the provided DOM element.\n *\n * Bypassing <PERSON>act's event system can be desirable, for instance when attaching non-passive\n * event handlers.\n *\n * ```jsx\n * const ref = useRef(null)\n *\n * useDomEvent(ref, 'wheel', onWheel, { passive: false })\n *\n * return <div ref={ref} />\n * ```\n *\n * @param ref - React.RefObject that's been provided to the element you want to bind the listener to.\n * @param eventName - Name of the event you want listen for.\n * @param handler - Function to fire when receiving the event.\n * @param options - Options to pass to `Event.addEventListener`.\n *\n * @public\n */\nfunction useDomEvent(ref, eventName, handler, options) {\n    useEffect(() => {\n        const element = ref.current;\n        if (handler && element) {\n            return addDomEvent(element, eventName, handler, options);\n        }\n    }, [ref, eventName, handler, options]);\n}\n\nexport { useDomEvent };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,QAAQ,qBAAqB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACnDN,SAAS,CAAC,MAAM;IACZ,MAAMO,OAAO,GAAGJ,GAAG,CAACK,OAAO;IAC3B,IAAIH,OAAO,IAAIE,OAAO,EAAE;MACpB,OAAON,WAAW,CAACM,OAAO,EAAEH,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;IAC5D;EACJ,CAAC,EAAE,CAACH,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC1C;AAEA,SAASJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}