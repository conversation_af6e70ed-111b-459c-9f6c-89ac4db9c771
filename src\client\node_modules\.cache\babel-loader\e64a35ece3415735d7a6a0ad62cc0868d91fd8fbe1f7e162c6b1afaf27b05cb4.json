{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{FaTelegram,FaPhone,FaMapMarkerAlt,FaInstagram,FaWhatsapp}from'react-icons/fa';import blazeTradeLogo from'../assets/blazetrade-logo.png';import qrCodeImage from'../assets/images/qr-code.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Footer=()=>{const currentYear=new Date().getFullYear();return/*#__PURE__*/_jsx(\"footer\",{className:\"bg-primary-dark text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(\"img\",{src:blazeTradeLogo,alt:\"BlazeTrade Logo\",className:\"w-6 h-6 rounded\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold\",children:\"BlazeTrade\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-4\",children:\"Your trusted partner for Bitcoin exchange and trading services. We provide secure, reliable, and professional cryptocurrency solutions.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4\",children:[/*#__PURE__*/_jsx(SocialIcon,{icon:/*#__PURE__*/_jsx(FaInstagram,{}),href:\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\"}),/*#__PURE__*/_jsx(SocialIcon,{icon:/*#__PURE__*/_jsx(FaWhatsapp,{}),href:\"https://wa.me/+2348163309355\"}),/*#__PURE__*/_jsx(SocialIcon,{icon:/*#__PURE__*/_jsx(FaTelegram,{}),href:\"https://t.me/blazetrad\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Quick Links\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(FooterLink,{to:\"/\",label:\"Home\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/about\",label:\"About Us\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/services\",label:\"Our Services\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/contact\",label:\"Contact Us\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/privacy-policy\",label:\"Privacy Policy\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/terms-of-service\",label:\"Terms of Service\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Our Services\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(FooterLink,{to:\"/services#bitcoin-exchange\",label:\"Bitcoin Exchange\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/services#crypto-trading\",label:\"Crypto Trading\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/services#market-analysis\",label:\"Market Analysis\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/services#security-solutions\",label:\"Security Solutions\"}),/*#__PURE__*/_jsx(FooterLink,{to:\"/services#consulting\",label:\"Consulting Services\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Contact Us\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2 text-gray-300\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"p\",{children:\"Victoria island, Lagos, Nigeria, 101241\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"tel:+2348163309355\",className:\"hover:text-accent transition-colors\",children:\"+234 ************\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"mailto:<EMAIL>\",className:\"hover:text-accent transition-colors\",children:\"<EMAIL>\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-semibold mb-2\",children:\"Scan to Chat\"}),/*#__PURE__*/_jsx(\"img\",{src:qrCodeImage,alt:\"QR Code for WhatsApp\",className:\"w-24 h-24 rounded-lg\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Follow Us\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-4\",children:\"Stay updated with our latest news and offers on our official Instagram page.\"}),/*#__PURE__*/_jsxs(\"a\",{href:\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"inline-flex items-center text-accent hover:text-white transition-colors\",children:[/*#__PURE__*/_jsx(FaInstagram,{className:\"mr-2\"}),\"@blaze__trade\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-400\",children:[\"\\xA9 \",currentYear,\" BlazeTrade. All rights reserved.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 md:mt-0\",children:[/*#__PURE__*/_jsx(Link,{to:\"/privacy-policy\",className:\"text-sm text-gray-400 hover:text-white mr-4\",children:\"Privacy Policy\"}),/*#__PURE__*/_jsx(Link,{to:\"/terms-of-service\",className:\"text-sm text-gray-400 hover:text-white\",children:\"Terms of Service\"})]})]})]})});};const SocialIcon=_ref=>{let{icon,href}=_ref;return/*#__PURE__*/_jsx(\"a\",{href:href,target:\"_blank\",rel:\"noopener noreferrer\",className:\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\",children:icon});};const FooterLink=_ref2=>{let{to,label}=_ref2;return/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:to,className:\"text-gray-300 hover:text-white transition-colors duration-300\",children:label})});};export default Footer;", "map": {"version": 3, "names": ["React", "Link", "FaTelegram", "FaPhone", "FaMapMarkerAlt", "FaInstagram", "FaWhatsapp", "blazeTradeLogo", "qrCodeImage", "jsx", "_jsx", "jsxs", "_jsxs", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "SocialIcon", "icon", "href", "FooterLink", "to", "label", "target", "rel", "_ref", "_ref2"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTelegram, FaPhone, FaMapMarkerAlt, FaInstagram, FaWhatsapp } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport qrCodeImage from '../assets/images/qr-code.jpg';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-primary-dark text-white\">\n      <div className=\"container-custom py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"w-6 h-6 rounded\" />\n              <span className=\"text-xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Your trusted partner for Bitcoin exchange and trading services. We provide secure, reliable, and professional cryptocurrency solutions.\n            </p>\n            <div className=\"flex space-x-4\">\n              <SocialIcon icon={<FaInstagram />} href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" />\n              <SocialIcon icon={<FaWhatsapp />} href=\"https://wa.me/+2348163309355\" />\n              <SocialIcon icon={<FaTelegram />} href=\"https://t.me/blazetrad\" />\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/\" label=\"Home\" />\n              <FooterLink to=\"/about\" label=\"About Us\" />\n              <FooterLink to=\"/services\" label=\"Our Services\" />\n              <FooterLink to=\"/contact\" label=\"Contact Us\" />\n              <FooterLink to=\"/privacy-policy\" label=\"Privacy Policy\" />\n              <FooterLink to=\"/terms-of-service\" label=\"Terms of Service\" />\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Our Services</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/services#bitcoin-exchange\" label=\"Bitcoin Exchange\" />\n              <FooterLink to=\"/services#crypto-trading\" label=\"Crypto Trading\" />\n              <FooterLink to=\"/services#market-analysis\" label=\"Market Analysis\" />\n              <FooterLink to=\"/services#security-solutions\" label=\"Security Solutions\" />\n              <FooterLink to=\"/services#consulting\" label=\"Consulting Services\" />\n            </ul>\n          </div>\n\n          {/* Contact Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><p>Victoria island, Lagos, Nigeria, 101241</p></li>\n              <li><a href=\"tel:+2348163309355\" className=\"hover:text-accent transition-colors\">+234 ************</a></li>\n              <li><a href=\"mailto:<EMAIL>\" className=\"hover:text-accent transition-colors\"><EMAIL></a></li>\n            </ul>\n            <div className=\"mt-4\">\n              <h4 className=\"text-md font-semibold mb-2\">Scan to Chat</h4>\n              <img src={qrCodeImage} alt=\"QR Code for WhatsApp\" className=\"w-24 h-24 rounded-lg\" />\n            </div>\n          </div>\n\n          {/* Follow Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Follow Us</h3>\n            <p className=\"text-gray-300 mb-4\">Stay updated with our latest news and offers on our official Instagram page.</p>\n            <a href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"inline-flex items-center text-accent hover:text-white transition-colors\">\n              <FaInstagram className=\"mr-2\" />\n              @blaze__trade\n            </a>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-gray-400\">\n            &copy; {currentYear} BlazeTrade. All rights reserved.\n          </p>\n          <div className=\"mt-4 md:mt-0\">\n            <Link to=\"/privacy-policy\" className=\"text-sm text-gray-400 hover:text-white mr-4\">\n              Privacy Policy\n            </Link>\n            <Link to=\"/terms-of-service\" className=\"text-sm text-gray-400 hover:text-white\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nconst SocialIcon = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nconst FooterLink = ({ to, label }) => {\n  return (\n    <li>\n      <Link \n        to={to} \n        className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n      >\n        {label}\n      </Link>\n    </li>\n  );\n};\n\nexport default Footer;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,UAAU,CAAEC,OAAO,CAAEC,cAAc,CAAEC,WAAW,CAAEC,UAAU,KAAQ,gBAAgB,CAC7F,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAC1D,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAE5C,mBACEN,IAAA,WAAQO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cAC5CN,KAAA,QAAKK,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCN,KAAA,QAAKK,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAEnEN,KAAA,QAAAM,QAAA,eACEN,KAAA,QAAKK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CR,IAAA,QAAKS,GAAG,CAAEZ,cAAe,CAACa,GAAG,CAAC,iBAAiB,CAACH,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAC9EP,IAAA,SAAMO,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EAClD,CAAC,cACNR,IAAA,MAAGO,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,yIAElC,CAAG,CAAC,cACJN,KAAA,QAAKK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BR,IAAA,CAACW,UAAU,EAACC,IAAI,cAAEZ,IAAA,CAACL,WAAW,GAAE,CAAE,CAACkB,IAAI,CAAC,sDAAsD,CAAE,CAAC,cACjGb,IAAA,CAACW,UAAU,EAACC,IAAI,cAAEZ,IAAA,CAACJ,UAAU,GAAE,CAAE,CAACiB,IAAI,CAAC,8BAA8B,CAAE,CAAC,cACxEb,IAAA,CAACW,UAAU,EAACC,IAAI,cAAEZ,IAAA,CAACR,UAAU,GAAE,CAAE,CAACqB,IAAI,CAAC,wBAAwB,CAAE,CAAC,EAC/D,CAAC,EACH,CAAC,cAGNX,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3DN,KAAA,OAAIK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBR,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,GAAG,CAACC,KAAK,CAAC,MAAM,CAAE,CAAC,cAClChB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAE,CAAC,cAC3ChB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,WAAW,CAACC,KAAK,CAAC,cAAc,CAAE,CAAC,cAClDhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,UAAU,CAACC,KAAK,CAAC,YAAY,CAAE,CAAC,cAC/ChB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,iBAAiB,CAACC,KAAK,CAAC,gBAAgB,CAAE,CAAC,cAC1DhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,mBAAmB,CAACC,KAAK,CAAC,kBAAkB,CAAE,CAAC,EAC5D,CAAC,EACF,CAAC,cAGNd,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC5DN,KAAA,OAAIK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBR,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,4BAA4B,CAACC,KAAK,CAAC,kBAAkB,CAAE,CAAC,cACvEhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,0BAA0B,CAACC,KAAK,CAAC,gBAAgB,CAAE,CAAC,cACnEhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,2BAA2B,CAACC,KAAK,CAAC,iBAAiB,CAAE,CAAC,cACrEhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,8BAA8B,CAACC,KAAK,CAAC,oBAAoB,CAAE,CAAC,cAC3EhB,IAAA,CAACc,UAAU,EAACC,EAAE,CAAC,sBAAsB,CAACC,KAAK,CAAC,qBAAqB,CAAE,CAAC,EAClE,CAAC,EACF,CAAC,cAGNd,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAC1DN,KAAA,OAAIK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,MAAAQ,QAAA,CAAG,yCAAuC,CAAG,CAAC,CAAI,CAAC,cACvDR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,MAAGa,IAAI,CAAC,oBAAoB,CAACN,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,CAAI,CAAC,cAC3GR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,MAAGa,IAAI,CAAC,+BAA+B,CAACN,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,wBAAsB,CAAG,CAAC,CAAI,CAAC,EACzH,CAAC,cACLN,KAAA,QAAKK,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBR,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC5DR,IAAA,QAAKS,GAAG,CAAEX,WAAY,CAACY,GAAG,CAAC,sBAAsB,CAACH,SAAS,CAAC,sBAAsB,CAAE,CAAC,EAClF,CAAC,EACH,CAAC,cAGNL,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACzDR,IAAA,MAAGO,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,8EAA4E,CAAG,CAAC,cAClHN,KAAA,MAAGW,IAAI,CAAC,sDAAsD,CAACI,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAACX,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAC1LR,IAAA,CAACL,WAAW,EAACY,SAAS,CAAC,MAAM,CAAE,CAAC,gBAElC,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENL,KAAA,QAAKK,SAAS,CAAC,4FAA4F,CAAAC,QAAA,eACzGN,KAAA,MAAGK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,OAC5B,CAACJ,WAAW,CAAC,mCACtB,EAAG,CAAC,cACJF,KAAA,QAAKK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BR,IAAA,CAACT,IAAI,EAACwB,EAAE,CAAC,iBAAiB,CAACR,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,gBAEnF,CAAM,CAAC,cACPR,IAAA,CAACT,IAAI,EAACwB,EAAE,CAAC,mBAAmB,CAACR,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,kBAEhF,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,KAAM,CAAAG,UAAU,CAAGQ,IAAA,EAAoB,IAAnB,CAAEP,IAAI,CAAEC,IAAK,CAAC,CAAAM,IAAA,CAChC,mBACEnB,IAAA,MACEa,IAAI,CAAEA,IAAK,CACXI,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBX,SAAS,CAAC,kHAAkH,CAAAC,QAAA,CAE3HI,IAAI,CACJ,CAAC,CAER,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGM,KAAA,EAAmB,IAAlB,CAAEL,EAAE,CAAEC,KAAM,CAAC,CAAAI,KAAA,CAC/B,mBACEpB,IAAA,OAAAQ,QAAA,cACER,IAAA,CAACT,IAAI,EACHwB,EAAE,CAAEA,EAAG,CACPR,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAExEQ,KAAK,CACF,CAAC,CACL,CAAC,CAET,CAAC,CAED,cAAe,CAAAb,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}