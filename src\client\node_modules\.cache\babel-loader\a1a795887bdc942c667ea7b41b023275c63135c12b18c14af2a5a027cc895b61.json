{"ast": null, "code": "'use strict';\n\n/**\n * Helper to convert an array of objects to JSON\n */\nmodule.exports = function arrayToJSON(arr) {\n  return arr.map(item => {\n    if (typeof item === 'object' && item !== null && typeof item.toJSON === 'function') {\n      return item.toJSON();\n    }\n    return item;\n  });\n};", "map": {"version": 3, "names": ["module", "exports", "arrayToJSON", "arr", "map", "item", "toJSON"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/array-to-json.js"], "sourcesContent": ["'use strict';\n\n/**\n * Helper to convert an array of objects to JSON\n */\nmodule.exports = function arrayToJSON(arr) {\n  return arr.map(item => {\n    if (typeof item === 'object' && item !== null && typeof item.toJSON === 'function') {\n      return item.toJSON();\n    }\n    return item;\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,GAAG,EAAE;EACzC,OAAOA,GAAG,CAACC,GAAG,CAACC,IAAI,IAAI;IACrB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOA,IAAI,CAACC,MAAM,KAAK,UAAU,EAAE;MAClF,OAAOD,IAAI,CAACC,MAAM,CAAC,CAAC;IACtB;IACA,OAAOD,IAAI;EACb,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}