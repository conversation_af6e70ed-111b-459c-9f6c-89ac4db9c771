{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\Chatbot.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaRobot, FaTimes, FaPaperPlane, FaUser } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Chatbot = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const token = localStorage.getItem('token');\n  const [messages, setMessages] = useState([]);\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [awaitingYesNo, setAwaitingYesNo] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Predefined responses for the chatbot\n  const botResponses = {\n    greetings: token ? [\"Hello! Welcome back to BlazeTrade. How can I assist you today?\", \"Welcome back! I'm here to help with any questions about your account or our services.\"] : [\"Hello! Welcome to BlazeTrade. Login to start trading.\", \"Hi there! Please login to access all our trading features.\"],\n    about: [\"BlazeTrade is a professional Bitcoin exchange and trading platform established in 2018. We provide secure and reliable cryptocurrency services with a focus on user experience and security.\", \"Founded with a mission to make cryptocurrency trading accessible to everyone, BlazeTrade offers a range of services including Bitcoin exchange, trading, and buying of giftcards.\", \"BlazeTrade is a leading cryptocurrency platform serving over 10,000 active users worldwide. Our team consists of blockchain experts and financial professionals dedicated to providing the best trading experience.\"],\n    services: [\"Our services include Bitcoin exchange, cryptocurrency trading, market analysis, portfolio management, security solutions, and consulting services.\", \"At BlazeTrade, we offer comprehensive cryptocurrency services including buying and selling Bitcoin, trading strategies, market insights, and personalized portfolio management.\", \"BlazeTrade provides institutional-grade trading tools with real-time market data, advanced charting, and algorithmic trading options for both beginners and professional traders.\"],\n    security: [\"Security is our top priority. We implement industry-leading security measures including cold storage, two-factor authentication, and regular security audits to protect your assets.\", \"BlazeTrade uses advanced encryption and multi-signature technology to ensure the highest level of security for your cryptocurrency assets.\", \"We keep 95% of user funds in cold storage protected by multi-signature technology. Our platform undergoes regular penetration testing and security audits by third-party experts.\"],\n    fees: [\"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.5% depending on your trading volume. Please visit our services page for detailed information.\", \"BlazeTrade offers competitive fees with discounts for high-volume traders. Our basic trading fee is 0.2% per transaction.\", \"We offer tiered fee discounts based on 30-day trading volume. VIP clients trading over $1M monthly enjoy fees as low as 0.05% and dedicated account managers.\"],\n    contact: [\"You can contact our support <NAME_EMAIL> or call us at +****************. We're available 24/7 to assist you.\", \"For any inquiries, please email <NAME_EMAIL> or use the contact form on our website. Our team is ready to help!\", \"Our headquarters is located in New York with regional offices in London, Singapore, and Tokyo. Technical support is available 24/7 via live chat, email, or phone.\"],\n    advantages: [\"BlazeTrade offers several advantages including institutional-grade security, 24/7 customer support, competitive fees, and a user-friendly interface designed for both beginners and professionals.\", \"What sets BlazeTrade apart is our combination of advanced trading tools, educational resources, and personalized portfolio management services tailored to each client's needs.\"],\n    history: [\"BlazeTrade was founded in 2018 by a team of blockchain enthusiasts and financial experts with a vision to make cryptocurrency trading accessible, secure, and transparent for everyone.\", \"Since our founding, we've grown to serve clients in over 100 countries, processed more than $5 billion in trading volume, and maintained a 99.9% platform uptime.\"],\n    team: [\"Our leadership team includes former executives from major financial institutions and blockchain pioneers with over 50 years of combined experience in fintech and cryptocurrency markets.\", \"BlazeTrade employs over 120 professionals worldwide, including blockchain developers, security experts, financial analysts, and customer support specialists.\"],\n    default: [\"I'm not sure I understand. Could you please rephrase your question?\", \"I don't have information on that specific topic. Would you like to know about our services, security measures, or how to contact us?\", \"For more detailed information, please contact our support <NAME_EMAIL>.\"],\n    goodbye: [\"Goodbye! Have a great day!\"],\n    help: [\"Sure! I can assist you with any questions you have about our services, security measures, or how to contact us.\", \"I'm here to help! Let me know if you have any specific questions or need assistance.\"],\n    how_to_trade_with_blazetrade: [\"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"],\n    thanks: [\"You're welcome! Is there anything else I can help you with?\", \"Happy to help! Let me know if you have any other questions.\", \"No problem! Feel free to ask if anything else comes to mind.\"],\n    yes: [\"Great! How can I help you?\", \"Sure, what do you need help with?\", \"I am here to help. What is your question?\"],\n    no: [\"Alright, have a great day!\", \"No problem, feel free to reach out if you need anything else.\", \"Okay, goodbye!\"],\n    trade: [\"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"]\n  };\n\n  // Function to scroll to the bottom of the chat\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Function to toggle the chatbot\n  const toggleChat = () => {\n    setIsOpen(!isOpen);\n    if (!isOpen && messages.length === 0) {\n      // Add welcome message when opening for the first time\n      const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n      setTimeout(() => {\n        setMessages([{\n          text: randomGreeting,\n          sender: 'bot'\n        }]);\n      }, 500);\n    }\n  };\n\n  // Function to handle sending a message\n  const handleEndChat = () => {\n    setMessages([]);\n    setInputValue('');\n    const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n    setTimeout(() => {\n      setMessages([{\n        text: randomGreeting,\n        sender: 'bot'\n      }]);\n    }, 500);\n  };\n  const handleSendMessage = e => {\n    e.preventDefault();\n    if (inputValue.trim() === '') return;\n\n    // Add user message\n    const userMessage = {\n      text: inputValue,\n      sender: 'user'\n    };\n    setMessages([...messages, userMessage]);\n    setInputValue('');\n    setIsTyping(true);\n\n    // Simulate bot thinking and respond after a delay\n    setTimeout(() => {\n      const botMessage = {\n        text: getBotResponse(inputValue),\n        sender: 'bot'\n      };\n      setMessages(prevMessages => [...prevMessages, botMessage]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds\n  };\n\n  // Function to determine bot response based on user input\n  const getBotResponse = input => {\n    const lowerInput = input.toLowerCase();\n\n    // Check for keywords in the input\n    if (/(hi|hello|hey|greetings)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.greetings);\n    } else if (/(about|who are you|company|brand)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.about);\n    } else if (/(services|offer|provide|trading|exchange)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.services);\n    } else if (/(secure|security|safe|protection)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.security);\n    } else if (/(fee|cost|price|charge)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.fees);\n    } else if (/(contact|email|phone|reach|support)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.contact);\n    } else if (/(advantage|benefit|better|why choose|why use)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.advantages);\n    } else if (/(history|founded|start|begin|origin)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.history);\n    } else if (/(team|staff|employee|expert|founder)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.team);\n    } else if (/(thanks|thank you|appreciate it)/i.test(lowerInput)) {\n      setAwaitingYesNo(true);\n      return getRandomResponse(botResponses.thanks);\n    } else if (awaitingYesNo) {\n      if (/(yes|yeah|sure)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.yes);\n      } else if (/(no|nope|nah)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.no);\n      } else {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.default);\n      }\n    } else if (/(how to trade|trade with you|start trading)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.trade);\n    } else {\n      return getRandomResponse(botResponses.default);\n    }\n  };\n\n  // Function to get a random response from an array\n  const getRandomResponse = responseArray => {\n    return responseArray[Math.floor(Math.random() * responseArray.length)];\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      className: \"fixed bottom-6 right-6 w-14 h-14 rounded-full bg-primary-dark text-white flex items-center justify-center shadow-lg hover:bg-primary-light transition-colors duration-300 z-50\",\n      onClick: toggleChat,\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: isOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 19\n      }, this) : /*#__PURE__*/_jsxDEV(FaRobot, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"fixed bottom-24 right-6 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-xl overflow-hidden z-50 flex flex-col\",\n        initial: {\n          opacity: 0,\n          y: 20,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          y: 20,\n          scale: 0.9\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-primary-dark text-white p-4 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"BlazeTrade Assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleEndChat,\n              className: \"text-white hover:text-gray-300 transition-colors mr-2\",\n              children: \"End Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleChat,\n              className: \"text-white hover:text-gray-300 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4 overflow-y-auto bg-gray-50\",\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: `flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`,\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `max-w-[80%] p-3 rounded-lg ${message.sender === 'user' ? 'bg-primary-dark text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-2\",\n                children: [message.sender === 'bot' && /*#__PURE__*/_jsxDEV(FaRobot, {\n                  className: \"mt-1 text-primary-dark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: message.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this), message.sender === 'user' && /*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"mt-1 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this)), isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex justify-start mb-3\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none max-w-[80%]\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",\n                  style: {\n                    animationDelay: '0ms'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",\n                  style: {\n                    animationDelay: '150ms'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",\n                  style: {\n                    animationDelay: '300ms'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"p-3 bg-gray-100 border-t border-gray-200 flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: inputValue,\n            onChange: e => setInputValue(e.target.value),\n            placeholder: \"Type your message...\",\n            className: \"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-dark text-gray-800\",\n            autoComplete: \"off\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-primary-dark text-white px-4 py-2 rounded-r-md hover:bg-primary-light transition-colors flex items-center justify-center\",\n            disabled: inputValue.trim() === '',\n            children: /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Chatbot, \"0Mj6z5xwKQhV79JTX+Z73BCVOMk=\");\n_c = Chatbot;\nexport default Chatbot;\nvar _c;\n$RefreshReg$(_c, \"Chatbot\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "FaRobot", "FaTimes", "FaPaperPlane", "FaUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "isOpen", "setIsOpen", "token", "localStorage", "getItem", "messages", "setMessages", "inputValue", "setInputValue", "isTyping", "setIsTyping", "awaitingYesNo", "setAwaitingYesNo", "messagesEndRef", "botResponses", "greetings", "about", "services", "security", "fees", "contact", "advantages", "history", "team", "default", "goodbye", "help", "how_to_trade_with_<PERSON><PERSON>de", "thanks", "yes", "no", "trade", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "toggleChat", "length", "randomGreeting", "Math", "floor", "random", "setTimeout", "text", "sender", "handleEndChat", "handleSendMessage", "e", "preventDefault", "trim", "userMessage", "botMessage", "getBotResponse", "prevMessages", "input", "lowerInput", "toLowerCase", "test", "getRandomResponse", "responseArray", "children", "button", "className", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "map", "message", "index", "style", "animationDelay", "ref", "onSubmit", "type", "value", "onChange", "target", "placeholder", "autoComplete", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Chatbot.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaRobot, FaTimes, FaPaperPlane, FaUser } from 'react-icons/fa';\n\nconst Chatbot = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const token = localStorage.getItem('token');\n  const [messages, setMessages] = useState([]);\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [awaitingYesNo, setAwaitingYesNo] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Predefined responses for the chatbot\n  const botResponses = {\n    greetings: token \n      ? [\n          \"Hello! Welcome back to BlazeTrade. How can I assist you today?\",\n          \"Welcome back! I'm here to help with any questions about your account or our services.\",\n        ]\n      : [\n          \"Hello! Welcome to BlazeTrade. Login to start trading.\",\n          \"Hi there! Please login to access all our trading features.\",\n        ],\n    about: [\n      \"BlazeTrade is a professional Bitcoin exchange and trading platform established in 2018. We provide secure and reliable cryptocurrency services with a focus on user experience and security.\",\n      \"Founded with a mission to make cryptocurrency trading accessible to everyone, BlazeTrade offers a range of services including Bitcoin exchange, trading, and buying of giftcards.\",\n      \"BlazeTrade is a leading cryptocurrency platform serving over 10,000 active users worldwide. Our team consists of blockchain experts and financial professionals dedicated to providing the best trading experience.\"\n    ],\n    services: [\n      \"Our services include Bitcoin exchange, cryptocurrency trading, market analysis, portfolio management, security solutions, and consulting services.\",\n      \"At BlazeTrade, we offer comprehensive cryptocurrency services including buying and selling Bitcoin, trading strategies, market insights, and personalized portfolio management.\",\n      \"BlazeTrade provides institutional-grade trading tools with real-time market data, advanced charting, and algorithmic trading options for both beginners and professional traders.\"\n    ],\n    security: [\n      \"Security is our top priority. We implement industry-leading security measures including cold storage, two-factor authentication, and regular security audits to protect your assets.\",\n      \"BlazeTrade uses advanced encryption and multi-signature technology to ensure the highest level of security for your cryptocurrency assets.\",\n      \"We keep 95% of user funds in cold storage protected by multi-signature technology. Our platform undergoes regular penetration testing and security audits by third-party experts.\"\n    ],\n    fees: [\n      \"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.5% depending on your trading volume. Please visit our services page for detailed information.\",\n      \"BlazeTrade offers competitive fees with discounts for high-volume traders. Our basic trading fee is 0.2% per transaction.\",\n      \"We offer tiered fee discounts based on 30-day trading volume. VIP clients trading over $1M monthly enjoy fees as low as 0.05% and dedicated account managers.\"\n    ],\n    contact: [\n      \"You can contact our support <NAME_EMAIL> or call us at +****************. We're available 24/7 to assist you.\",\n      \"For any inquiries, please email <NAME_EMAIL> or use the contact form on our website. Our team is ready to help!\",\n      \"Our headquarters is located in New York with regional offices in London, Singapore, and Tokyo. Technical support is available 24/7 via live chat, email, or phone.\"\n    ],\n    advantages: [\n      \"BlazeTrade offers several advantages including institutional-grade security, 24/7 customer support, competitive fees, and a user-friendly interface designed for both beginners and professionals.\",\n      \"What sets BlazeTrade apart is our combination of advanced trading tools, educational resources, and personalized portfolio management services tailored to each client's needs.\"\n    ],\n    history: [\n      \"BlazeTrade was founded in 2018 by a team of blockchain enthusiasts and financial experts with a vision to make cryptocurrency trading accessible, secure, and transparent for everyone.\",\n      \"Since our founding, we've grown to serve clients in over 100 countries, processed more than $5 billion in trading volume, and maintained a 99.9% platform uptime.\"\n    ],\n    team: [\n      \"Our leadership team includes former executives from major financial institutions and blockchain pioneers with over 50 years of combined experience in fintech and cryptocurrency markets.\",\n      \"BlazeTrade employs over 120 professionals worldwide, including blockchain developers, security experts, financial analysts, and customer support specialists.\"\n    ],\n    default: [\n      \"I'm not sure I understand. Could you please rephrase your question?\",\n      \"I don't have information on that specific topic. Would you like to know about our services, security measures, or how to contact us?\",\n      \"For more detailed information, please contact our support <NAME_EMAIL>.\"\n    ],\n    goodbye: [\n      \"Goodbye! Have a great day!\",\n    ],\n    help: [\n      \"Sure! I can assist you with any questions you have about our services, security measures, or how to contact us.\",\n      \"I'm here to help! Let me know if you have any specific questions or need assistance.\"\n    ],\n    how_to_trade_with_blazetrade: [\n      \"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\",\n    ],\n    thanks: [\n      \"You're welcome! Is there anything else I can help you with?\",\n      \"Happy to help! Let me know if you have any other questions.\",\n      \"No problem! Feel free to ask if anything else comes to mind.\"\n    ],\n    yes: [\n      \"Great! How can I help you?\",\n      \"Sure, what do you need help with?\",\n      \"I am here to help. What is your question?\",\n    ],\n    no: [\n      \"Alright, have a great day!\",\n      \"No problem, feel free to reach out if you need anything else.\",\n      \"Okay, goodbye!\",\n    ],\n    trade: [\n      \"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"\n    ]\n  };\n\n  // Function to scroll to the bottom of the chat\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Function to toggle the chatbot\n  const toggleChat = () => {\n    setIsOpen(!isOpen);\n    if (!isOpen && messages.length === 0) {\n      // Add welcome message when opening for the first time\n      const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n      setTimeout(() => {\n        setMessages([{ text: randomGreeting, sender: 'bot' }]);\n      }, 500);\n    }\n  };\n\n  // Function to handle sending a message\n  const handleEndChat = () => {\n    setMessages([]);\n    setInputValue('');\n    const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n    setTimeout(() => {\n      setMessages([{ text: randomGreeting, sender: 'bot' }]);\n    }, 500);\n  };\n\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    if (inputValue.trim() === '') return;\n\n    // Add user message\n    const userMessage = { text: inputValue, sender: 'user' };\n    setMessages([...messages, userMessage]);\n    setInputValue('');\n    setIsTyping(true);\n\n    // Simulate bot thinking and respond after a delay\n    setTimeout(() => {\n      const botMessage = { text: getBotResponse(inputValue), sender: 'bot' };\n      setMessages(prevMessages => [...prevMessages, botMessage]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds\n  };\n\n  // Function to determine bot response based on user input\n  const getBotResponse = (input) => {\n    const lowerInput = input.toLowerCase();\n    \n    // Check for keywords in the input\n    if (/(hi|hello|hey|greetings)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.greetings);\n    } else if (/(about|who are you|company|brand)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.about);\n    } else if (/(services|offer|provide|trading|exchange)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.services);\n    } else if (/(secure|security|safe|protection)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.security);\n    } else if (/(fee|cost|price|charge)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.fees);\n    } else if (/(contact|email|phone|reach|support)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.contact);\n    } else if (/(advantage|benefit|better|why choose|why use)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.advantages);\n    } else if (/(history|founded|start|begin|origin)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.history);\n    } else if (/(team|staff|employee|expert|founder)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.team);\n    } else if (/(thanks|thank you|appreciate it)/i.test(lowerInput)) {\n      setAwaitingYesNo(true);\n      return getRandomResponse(botResponses.thanks);\n    } else if (awaitingYesNo) {\n      if (/(yes|yeah|sure)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.yes);\n      } else if (/(no|nope|nah)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.no);\n      } else {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.default);\n      }\n    } else if (/(how to trade|trade with you|start trading)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.trade);\n    } else {\n      return getRandomResponse(botResponses.default);\n    }\n  };\n\n  // Function to get a random response from an array\n  const getRandomResponse = (responseArray) => {\n    return responseArray[Math.floor(Math.random() * responseArray.length)];\n  };\n\n  return (\n    <>\n      {/* Chat toggle button */}\n      <motion.button\n        className=\"fixed bottom-6 right-6 w-14 h-14 rounded-full bg-primary-dark text-white flex items-center justify-center shadow-lg hover:bg-primary-light transition-colors duration-300 z-50\"\n        onClick={toggleChat}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {isOpen ? <FaTimes size={20} /> : <FaRobot size={24} />}\n      </motion.button>\n\n      {/* Chat window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"fixed bottom-24 right-6 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-xl overflow-hidden z-50 flex flex-col\"\n            initial={{ opacity: 0, y: 20, scale: 0.9 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: 20, scale: 0.9 }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Chat header */}\n            <div className=\"bg-primary-dark text-white p-4 flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <FaRobot />\n                <span className=\"font-medium\">BlazeTrade Assistant</span>\n              </div>\n              <div>\n                <button \n                  onClick={handleEndChat}\n                  className=\"text-white hover:text-gray-300 transition-colors mr-2\"\n                >\n                  End Chat\n                </button>\n                <button \n                  onClick={toggleChat}\n                  className=\"text-white hover:text-gray-300 transition-colors\"\n                >\n                  <FaTimes />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat messages */}\n            <div className=\"flex-1 p-4 overflow-y-auto bg-gray-50\">\n              {messages.map((message, index) => (\n                <motion.div\n                  key={index}\n                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <div \n                    className={`max-w-[80%] p-3 rounded-lg ${message.sender === 'user' \n                      ? 'bg-primary-dark text-white rounded-br-none' \n                      : 'bg-gray-200 text-gray-800 rounded-bl-none'}`}\n                  >\n                    <div className=\"flex items-start space-x-2\">\n                      {message.sender === 'bot' && (\n                        <FaRobot className=\"mt-1 text-primary-dark\" />\n                      )}\n                      <div>{message.text}</div>\n                      {message.sender === 'user' && (\n                        <FaUser className=\"mt-1 text-white\" />\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n              {isTyping && (\n                <motion.div \n                  className=\"flex justify-start mb-3\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                >\n                  <div className=\"bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none max-w-[80%]\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '0ms' }}></div>\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '150ms' }}></div>\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '300ms' }}></div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Chat input */}\n            <form onSubmit={handleSendMessage} className=\"p-3 bg-gray-100 border-t border-gray-200 flex\">\n              <input\n                type=\"text\"\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder=\"Type your message...\"\n                className=\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-dark text-gray-800\"\n                autoComplete=\"off\"\n              />\n              <button \n                type=\"submit\"\n                className=\"bg-primary-dark text-white px-4 py-2 rounded-r-md hover:bg-primary-light transition-colors flex items-center justify-center\"\n                disabled={inputValue.trim() === ''}\n              >\n                <FaPaperPlane />\n              </button>\n            </form>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Chatbot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,EAAEC,OAAO,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMiB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM4B,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM4B,YAAY,GAAG;IACnBC,SAAS,EAAEb,KAAK,GACZ,CACE,gEAAgE,EAChE,uFAAuF,CACxF,GACD,CACE,uDAAuD,EACvD,4DAA4D,CAC7D;IACLc,KAAK,EAAE,CACL,8LAA8L,EAC9L,mLAAmL,EACnL,qNAAqN,CACtN;IACDC,QAAQ,EAAE,CACR,oJAAoJ,EACpJ,iLAAiL,EACjL,mLAAmL,CACpL;IACDC,QAAQ,EAAE,CACR,sLAAsL,EACtL,4IAA4I,EAC5I,mLAAmL,CACpL;IACDC,IAAI,EAAE,CACJ,mLAAmL,EACnL,2HAA2H,EAC3H,+JAA+J,CAChK;IACDC,OAAO,EAAE,CACP,uIAAuI,EACvI,uIAAuI,EACvI,oKAAoK,CACrK;IACDC,UAAU,EAAE,CACV,oMAAoM,EACpM,iLAAiL,CAClL;IACDC,OAAO,EAAE,CACP,yLAAyL,EACzL,mKAAmK,CACpK;IACDC,IAAI,EAAE,CACJ,2LAA2L,EAC3L,+JAA+J,CAChK;IACDC,OAAO,EAAE,CACP,qEAAqE,EACrE,sIAAsI,EACtI,2FAA2F,CAC5F;IACDC,OAAO,EAAE,CACP,4BAA4B,CAC7B;IACDC,IAAI,EAAE,CACJ,iHAAiH,EACjH,sFAAsF,CACvF;IACDC,4BAA4B,EAAE,CAC5B,mNAAmN,CACpN;IACDC,MAAM,EAAE,CACN,6DAA6D,EAC7D,6DAA6D,EAC7D,8DAA8D,CAC/D;IACDC,GAAG,EAAE,CACH,4BAA4B,EAC5B,mCAAmC,EACnC,2CAA2C,CAC5C;IACDC,EAAE,EAAE,CACF,4BAA4B,EAC5B,+DAA+D,EAC/D,gBAAgB,CACjB;IACDC,KAAK,EAAE,CACL,mNAAmN;EAEvN,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAApB,cAAc,CAACqB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd6C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC3B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgC,UAAU,GAAGA,CAAA,KAAM;IACvBpC,SAAS,CAAC,CAACD,MAAM,CAAC;IAClB,IAAI,CAACA,MAAM,IAAIK,QAAQ,CAACiC,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,MAAMC,cAAc,GAAGzB,YAAY,CAACC,SAAS,CAACyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG5B,YAAY,CAACC,SAAS,CAACuB,MAAM,CAAC,CAAC;MACxGK,UAAU,CAAC,MAAM;QACfrC,WAAW,CAAC,CAAC;UAAEsC,IAAI,EAAEL,cAAc;UAAEM,MAAM,EAAE;QAAM,CAAC,CAAC,CAAC;MACxD,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxC,WAAW,CAAC,EAAE,CAAC;IACfE,aAAa,CAAC,EAAE,CAAC;IACjB,MAAM+B,cAAc,GAAGzB,YAAY,CAACC,SAAS,CAACyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG5B,YAAY,CAACC,SAAS,CAACuB,MAAM,CAAC,CAAC;IACxGK,UAAU,CAAC,MAAM;MACfrC,WAAW,CAAC,CAAC;QAAEsC,IAAI,EAAEL,cAAc;QAAEM,MAAM,EAAE;MAAM,CAAC,CAAC,CAAC;IACxD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI1C,UAAU,CAAC2C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;;IAE9B;IACA,MAAMC,WAAW,GAAG;MAAEP,IAAI,EAAErC,UAAU;MAAEsC,MAAM,EAAE;IAAO,CAAC;IACxDvC,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE8C,WAAW,CAAC,CAAC;IACvC3C,aAAa,CAAC,EAAE,CAAC;IACjBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAiC,UAAU,CAAC,MAAM;MACf,MAAMS,UAAU,GAAG;QAAER,IAAI,EAAES,cAAc,CAAC9C,UAAU,CAAC;QAAEsC,MAAM,EAAE;MAAM,CAAC;MACtEvC,WAAW,CAACgD,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAEF,UAAU,CAAC,CAAC;MAC1D1C,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,EAAE,IAAI,GAAG8B,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMW,cAAc,GAAIE,KAAK,IAAK;IAChC,MAAMC,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;;IAEtC;IACA,IAAI,2BAA2B,CAACC,IAAI,CAACF,UAAU,CAAC,EAAE;MAChD,OAAOG,iBAAiB,CAAC7C,YAAY,CAACC,SAAS,CAAC;IAClD,CAAC,MAAM,IAAI,oCAAoC,CAAC2C,IAAI,CAACF,UAAU,CAAC,EAAE;MAChE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACE,KAAK,CAAC;IAC9C,CAAC,MAAM,IAAI,4CAA4C,CAAC0C,IAAI,CAACF,UAAU,CAAC,EAAE;MACxE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACG,QAAQ,CAAC;IACjD,CAAC,MAAM,IAAI,oCAAoC,CAACyC,IAAI,CAACF,UAAU,CAAC,EAAE;MAChE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACI,QAAQ,CAAC;IACjD,CAAC,MAAM,IAAI,0BAA0B,CAACwC,IAAI,CAACF,UAAU,CAAC,EAAE;MACtD,OAAOG,iBAAiB,CAAC7C,YAAY,CAACK,IAAI,CAAC;IAC7C,CAAC,MAAM,IAAI,sCAAsC,CAACuC,IAAI,CAACF,UAAU,CAAC,EAAE;MAClE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACM,OAAO,CAAC;IAChD,CAAC,MAAM,IAAI,gDAAgD,CAACsC,IAAI,CAACF,UAAU,CAAC,EAAE;MAC5E,OAAOG,iBAAiB,CAAC7C,YAAY,CAACO,UAAU,CAAC;IACnD,CAAC,MAAM,IAAI,uCAAuC,CAACqC,IAAI,CAACF,UAAU,CAAC,EAAE;MACnE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACQ,OAAO,CAAC;IAChD,CAAC,MAAM,IAAI,uCAAuC,CAACoC,IAAI,CAACF,UAAU,CAAC,EAAE;MACnE,OAAOG,iBAAiB,CAAC7C,YAAY,CAACS,IAAI,CAAC;IAC7C,CAAC,MAAM,IAAI,mCAAmC,CAACmC,IAAI,CAACF,UAAU,CAAC,EAAE;MAC/D5C,gBAAgB,CAAC,IAAI,CAAC;MACtB,OAAO+C,iBAAiB,CAAC7C,YAAY,CAACc,MAAM,CAAC;IAC/C,CAAC,MAAM,IAAIjB,aAAa,EAAE;MACxB,IAAI,kBAAkB,CAAC+C,IAAI,CAACF,UAAU,CAAC,EAAE;QACvC5C,gBAAgB,CAAC,KAAK,CAAC;QACvB,OAAO+C,iBAAiB,CAAC7C,YAAY,CAACe,GAAG,CAAC;MAC5C,CAAC,MAAM,IAAI,gBAAgB,CAAC6B,IAAI,CAACF,UAAU,CAAC,EAAE;QAC5C5C,gBAAgB,CAAC,KAAK,CAAC;QACvB,OAAO+C,iBAAiB,CAAC7C,YAAY,CAACgB,EAAE,CAAC;MAC3C,CAAC,MAAM;QACLlB,gBAAgB,CAAC,KAAK,CAAC;QACvB,OAAO+C,iBAAiB,CAAC7C,YAAY,CAACU,OAAO,CAAC;MAChD;IACF,CAAC,MAAM,IAAI,8CAA8C,CAACkC,IAAI,CAACF,UAAU,CAAC,EAAE;MAC1E,OAAOG,iBAAiB,CAAC7C,YAAY,CAACiB,KAAK,CAAC;IAC9C,CAAC,MAAM;MACL,OAAO4B,iBAAiB,CAAC7C,YAAY,CAACU,OAAO,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAIC,aAAa,IAAK;IAC3C,OAAOA,aAAa,CAACpB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGkB,aAAa,CAACtB,MAAM,CAAC,CAAC;EACxE,CAAC;EAED,oBACE3C,OAAA,CAAAE,SAAA;IAAAgE,QAAA,gBAEElE,OAAA,CAACP,MAAM,CAAC0E,MAAM;MACZC,SAAS,EAAC,gLAAgL;MAC1LC,OAAO,EAAE3B,UAAW;MACpB4B,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MAAAL,QAAA,EAExB7D,MAAM,gBAAGL,OAAA,CAACJ,OAAO;QAAC6E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACL,OAAO;QAAC8E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGhB7E,OAAA,CAACN,eAAe;MAAAwE,QAAA,EACb7D,MAAM,iBACLL,OAAA,CAACP,MAAM,CAACqF,GAAG;QACTV,SAAS,EAAC,4GAA4G;QACtHW,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,EAAE;UAAEV,KAAK,EAAE;QAAI,CAAE;QAC3CW,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEV,KAAK,EAAE;QAAE,CAAE;QACxCY,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,EAAE;UAAEV,KAAK,EAAE;QAAI,CAAE;QACxCa,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAnB,QAAA,gBAG9BlE,OAAA;UAAKoE,SAAS,EAAC,kEAAkE;UAAAF,QAAA,gBAC/ElE,OAAA;YAAKoE,SAAS,EAAC,6BAA6B;YAAAF,QAAA,gBAC1ClE,OAAA,CAACL,OAAO;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX7E,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAF,QAAA,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN7E,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cACEqE,OAAO,EAAElB,aAAc;cACvBiB,SAAS,EAAC,uDAAuD;cAAAF,QAAA,EAClE;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7E,OAAA;cACEqE,OAAO,EAAE3B,UAAW;cACpB0B,SAAS,EAAC,kDAAkD;cAAAF,QAAA,eAE5DlE,OAAA,CAACJ,OAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7E,OAAA;UAAKoE,SAAS,EAAC,uCAAuC;UAAAF,QAAA,GACnDxD,QAAQ,CAAC4E,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BxF,OAAA,CAACP,MAAM,CAACqF,GAAG;YAETV,SAAS,EAAE,QAAQmB,OAAO,CAACrC,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,OAAQ;YACtF6B,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAnB,QAAA,eAE9BlE,OAAA;cACEoE,SAAS,EAAE,8BAA8BmB,OAAO,CAACrC,MAAM,KAAK,MAAM,GAC9D,4CAA4C,GAC5C,2CAA2C,EAAG;cAAAgB,QAAA,eAElDlE,OAAA;gBAAKoE,SAAS,EAAC,4BAA4B;gBAAAF,QAAA,GACxCqB,OAAO,CAACrC,MAAM,KAAK,KAAK,iBACvBlD,OAAA,CAACL,OAAO;kBAACyE,SAAS,EAAC;gBAAwB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC9C,eACD7E,OAAA;kBAAAkE,QAAA,EAAMqB,OAAO,CAACtC;gBAAI;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACxBU,OAAO,CAACrC,MAAM,KAAK,MAAM,iBACxBlD,OAAA,CAACF,MAAM;kBAACsE,SAAS,EAAC;gBAAiB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GApBDW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBA,CACb,CAAC,EACD/D,QAAQ,iBACPd,OAAA,CAACP,MAAM,CAACqF,GAAG;YACTV,SAAS,EAAC,yBAAyB;YACnCW,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YAAAd,QAAA,eAExBlE,OAAA;cAAKoE,SAAS,EAAC,sEAAsE;cAAAF,QAAA,eACnFlE,OAAA;gBAAKoE,SAAS,EAAC,gBAAgB;gBAAAF,QAAA,gBAC7BlE,OAAA;kBAAKoE,SAAS,EAAC,iDAAiD;kBAACqB,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAM;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzG7E,OAAA;kBAAKoE,SAAS,EAAC,iDAAiD;kBAACqB,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAQ;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3G7E,OAAA;kBAAKoE,SAAS,EAAC,iDAAiD;kBAACqB,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAQ;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eACD7E,OAAA;YAAK2F,GAAG,EAAEzE;UAAe;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGN7E,OAAA;UAAM4F,QAAQ,EAAExC,iBAAkB;UAACgB,SAAS,EAAC,+CAA+C;UAAAF,QAAA,gBAC1FlE,OAAA;YACE6F,IAAI,EAAC,MAAM;YACXC,KAAK,EAAElF,UAAW;YAClBmF,QAAQ,EAAG1C,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;YAC/CG,WAAW,EAAC,sBAAsB;YAClC7B,SAAS,EAAC,sHAAsH;YAChI8B,YAAY,EAAC;UAAK;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACF7E,OAAA;YACE6F,IAAI,EAAC,QAAQ;YACbzB,SAAS,EAAC,6HAA6H;YACvI+B,QAAQ,EAAEvF,UAAU,CAAC2C,IAAI,CAAC,CAAC,KAAK,EAAG;YAAAW,QAAA,eAEnClE,OAAA,CAACH,YAAY;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACzE,EAAA,CA9SID,OAAO;AAAAiG,EAAA,GAAPjG,OAAO;AAgTb,eAAeA,OAAO;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}