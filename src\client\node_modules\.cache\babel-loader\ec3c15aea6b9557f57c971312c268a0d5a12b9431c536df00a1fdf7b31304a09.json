{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaInstagram, FaLinkedin, FaEnvelope, FaPhone } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto py-12 px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: blazeTradeLogo,\n              alt: \"BlazeTrade Logo\",\n              className: \"h-8 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold\",\n              children: \"BlazeTrade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm\",\n            children: \"Securely trade and manage your cryptocurrency assets with confidence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/\",\n              label: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/about\",\n              label: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services\",\n              label: \"Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/contact\",\n              label: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Legal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/terms-of-service\",\n              label: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/privacy-policy\",\n              label: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/disclaimer\",\n              label: \"Disclaimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"hover:text-blue-400\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+1234567890\",\n                className: \"hover:text-blue-400\",\n                children: \"+1 (234) 567-890\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"\\xA9 \", currentYear, \" BlazeTrade. All Rights Reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4 mt-4 md:mt-0\",\n          children: [/*#__PURE__*/_jsxDEV(SocialIcon, {\n            icon: /*#__PURE__*/_jsxDEV(FaTwitter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 31\n            }, this),\n            href: \"#\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n            icon: /*#__PURE__*/_jsxDEV(FaFacebook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 31\n            }, this),\n            href: \"#\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n            icon: /*#__PURE__*/_jsxDEV(FaInstagram, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 31\n            }, this),\n            href: \"#\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n            icon: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 31\n            }, this),\n            href: \"#\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nconst SocialIcon = ({\n  icon,\n  href\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"a\", {\n    href: href,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\",\n    children: icon\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SocialIcon;\nconst FooterLink = ({\n  to,\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"li\", {\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: to,\n      className: \"text-gray-300 hover:text-white transition-colors duration-300\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_c3 = FooterLink;\nexport default Footer;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c2, \"SocialIcon\");\n$RefreshReg$(_c3, \"FooterLink\");", "map": {"version": 3, "names": ["React", "Link", "FaTwitter", "FaFacebook", "FaInstagram", "FaLinkedin", "FaEnvelope", "FaPhone", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FooterLink", "to", "label", "href", "SocialIcon", "icon", "_c", "target", "rel", "_c2", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaInstagram, FaLinkedin, FaEnvelope, FaPhone } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          \n          {/* Company Info */}\n          <div className=\"md:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-8 object-contain\" />\n              <span className=\"text-xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-400 text-sm\">\n              Securely trade and manage your cryptocurrency assets with confidence.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/\" label=\"Home\" />\n              <FooterLink to=\"/about\" label=\"About Us\" />\n              <FooterLink to=\"/services\" label=\"Services\" />\n              <FooterLink to=\"/contact\" label=\"Contact\" />\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/terms-of-service\" label=\"Terms of Service\" />\n              <FooterLink to=\"/privacy-policy\" label=\"Privacy Policy\" />\n              <FooterLink to=\"/disclaimer\" label=\"Disclaimer\" />\n            </ul>\n          </div>\n\n          {/* Contact Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-400\">\n              <li className=\"flex items-center\">\n                <FaEnvelope className=\"mr-2\" />\n                <a href=\"mailto:<EMAIL>\" className=\"hover:text-blue-400\"><EMAIL></a>\n              </li>\n              <li className=\"flex items-center\">\n                <FaPhone className=\"mr-2\" />\n                <a href=\"tel:+1234567890\" className=\"hover:text-blue-400\">+1 (234) 567-890</a>\n              </li>\n            </ul>\n          </div>\n\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-gray-500\">\n            &copy; {currentYear} BlazeTrade. All Rights Reserved.\n          </p>\n          <div className=\"flex space-x-4 mt-4 md:mt-0\">\n            <SocialIcon icon={<FaTwitter />} href=\"#\" />\n            <SocialIcon icon={<FaFacebook />} href=\"#\" />\n            <SocialIcon icon={<FaInstagram />} href=\"#\" />\n            <SocialIcon icon={<FaLinkedin />} href=\"#\" />\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nconst SocialIcon = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nconst FooterLink = ({ to, label }) => {\n  return (\n    <li>\n      <Link \n        to={to} \n        className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n      >\n        {label}\n      </Link>\n    </li>\n  );\n};\n\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AACpG,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACxCN,OAAA;MAAKK,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DN,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAGpDN,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BN,OAAA;YAAKK,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CN,OAAA;cAAKO,GAAG,EAAET,cAAe;cAACU,GAAG,EAAC,iBAAiB;cAACH,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFZ,OAAA;cAAMK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNZ,OAAA;YAAGK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,GAAG;cAACC,KAAK,EAAC;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCZ,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,QAAQ;cAACC,KAAK,EAAC;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CZ,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,WAAW;cAACC,KAAK,EAAC;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CZ,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,UAAU;cAACC,KAAK,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,mBAAmB;cAACC,KAAK,EAAC;YAAkB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DZ,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,iBAAiB;cAACC,KAAK,EAAC;YAAgB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DZ,OAAA,CAACa,UAAU;cAACC,EAAE,EAAC,aAAa;cAACC,KAAK,EAAC;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DZ,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCN,OAAA;cAAIK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BN,OAAA,CAACJ,UAAU;gBAACS,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BZ,OAAA;gBAAGgB,IAAI,EAAC,+BAA+B;gBAACX,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACLZ,OAAA;cAAIK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BN,OAAA,CAACH,OAAO;gBAACQ,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BZ,OAAA;gBAAGgB,IAAI,EAAC,iBAAiB;gBAACX,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CAAC,eAENZ,OAAA;QAAKK,SAAS,EAAC,4FAA4F;QAAAC,QAAA,gBACzGN,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OAC5B,EAACJ,WAAW,EAAC,mCACtB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJZ,OAAA;UAAKK,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CN,OAAA,CAACiB,UAAU;YAACC,IAAI,eAAElB,OAAA,CAACR,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACI,IAAI,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CZ,OAAA,CAACiB,UAAU;YAACC,IAAI,eAAElB,OAAA,CAACP,UAAU;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACI,IAAI,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CZ,OAAA,CAACiB,UAAU;YAACC,IAAI,eAAElB,OAAA,CAACN,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACI,IAAI,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CZ,OAAA,CAACiB,UAAU;YAACC,IAAI,eAAElB,OAAA,CAACL,UAAU;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACI,IAAI,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACO,EAAA,GAvEIlB,MAAM;AAyEZ,MAAMgB,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEF;AAAK,CAAC,KAAK;EACrC,oBACEhB,OAAA;IACEgB,IAAI,EAAEA,IAAK;IACXI,MAAM,EAAC,QAAQ;IACfC,GAAG,EAAC,qBAAqB;IACzBhB,SAAS,EAAC,kHAAkH;IAAAC,QAAA,EAE3HY;EAAI;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAER,CAAC;AAACU,GAAA,GAXIL,UAAU;AAahB,MAAMJ,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAM,CAAC,KAAK;EACpC,oBACEf,OAAA;IAAAM,QAAA,eACEN,OAAA,CAACT,IAAI;MACHuB,EAAE,EAAEA,EAAG;MACPT,SAAS,EAAC,+DAA+D;MAAAC,QAAA,EAExES;IAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAET,CAAC;AAACW,GAAA,GAXIV,UAAU;AAahB,eAAeZ,MAAM;AAAC,IAAAkB,EAAA,EAAAG,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}