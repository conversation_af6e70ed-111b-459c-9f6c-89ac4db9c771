{"ast": null, "code": "import{useEffect}from'react';import{useLocation}from'react-router-dom';const ScrollToTop=()=>{const{pathname}=useLocation();useEffect(()=>{window.scrollTo({top:0,behavior:'smooth'});},[pathname]);return null;};export default ScrollToTop;", "map": {"version": 3, "names": ["useEffect", "useLocation", "ScrollToTop", "pathname", "window", "scrollTo", "top", "behavior"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/ScrollToTop.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\n\nconst ScrollToTop = () => {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  }, [pathname]);\n\n  return null;\n};\n\nexport default ScrollToTop;"], "mappings": "AAAA,OAASA,SAAS,KAAQ,OAAO,CACjC,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,QAAS,CAAC,CAAGF,WAAW,CAAC,CAAC,CAElCD,SAAS,CAAC,IAAM,CACdI,MAAM,CAACC,QAAQ,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACjD,CAAC,CAAE,CAACJ,QAAQ,CAAC,CAAC,CAEd,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAAD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}