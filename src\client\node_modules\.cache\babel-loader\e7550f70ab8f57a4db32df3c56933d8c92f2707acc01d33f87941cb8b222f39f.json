{"ast": null, "code": "import { _isFirebaseServerApp, _registerComponent, registerVersion, _getProvider, getApp } from '@firebase/app';\nimport { FirebaseError, isCloudWorkstation, pingServer, updateEmulatorBanner, getModularInstance, getDefaultEmulatorHostnameAndPort } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\nfunction mapValues(\n// { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no, f) {\n  const result = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nfunction encode(data) {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nfunction decode(json) {\n  if (json == null) {\n    return json;\n  }\n  if (json['@type']) {\n    switch (json['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE:\n        {\n          // Technically, this could work return a valid number for malformed\n          // data if there was a number followed by garbage. But it's just not\n          // worth all the extra code to detect that case.\n          const value = Number(json['value']);\n          if (isNaN(value)) {\n            throw new Error('Data cannot be decoded from JSON: ' + json);\n          }\n          return value;\n        }\n      default:\n        {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Functions.\n */\nconst FUNCTIONS_TYPE = 'functions';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nclass FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n  /**\n   * A standard error code that will be returned to the client. This also\n   * determines the HTTP status code of the response, as defined in code.proto.\n   */\n  code, message,\n  /**\n   * Additional details to be converted to JSON and included in the error response.\n   */\n  details) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n    this.details = details;\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status) {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n  }\n  return 'unknown';\n}\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nfunction _errorForResponse(status, bodyJSON) {\n  let code = codeForHTTPStatus(status);\n  // Start with reasonable defaults from the status code.\n  let description = code;\n  let details = undefined;\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n  return new FunctionsError(code, description, details);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nclass ContextProvider {\n  constructor(app, authProvider, messagingProvider, appCheckProvider) {\n    this.app = app;\n    this.auth = null;\n    this.messaging = null;\n    this.appCheck = null;\n    this.serverAppAppCheckToken = null;\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({\n      optional: true\n    });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n    if (!this.auth) {\n      authProvider.get().then(auth => this.auth = auth, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.messaging) {\n      messagingProvider.get().then(messaging => this.messaging = messaging, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(appCheck => this.appCheck = appCheck, () => {\n        /* get() never rejects */\n      });\n    }\n  }\n  async getAuthToken() {\n    if (!this.auth) {\n      return undefined;\n    }\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n  async getMessagingToken() {\n    if (!this.messaging || !('Notification' in self) || Notification.permission !== 'granted') {\n      return undefined;\n    }\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n  async getAppCheckToken(limitedUseAppCheckTokens) {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens ? await this.appCheck.getLimitedUseToken() : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n  async getContext(limitedUseAppCheckTokens) {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return {\n      authToken,\n      messagingToken,\n      appCheckToken\n    };\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_REGION = 'us-central1';\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis) {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nclass FunctionsService {\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(app, authProvider, messagingProvider, appCheckProvider, regionOrCustomDomain = DEFAULT_REGION, fetchImpl = (...args) => fetch(...args)) {\n    this.app = app;\n    this.fetchImpl = fetchImpl;\n    this.emulatorOrigin = null;\n    this.contextProvider = new ContextProvider(app, authProvider, messagingProvider, appCheckProvider);\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain = url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n  _delete() {\n    return this.deleteService();\n  }\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name) {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nfunction connectFunctionsEmulator$1(functionsInstance, host, port) {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${useSsl ? 's' : ''}://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nfunction httpsCallable$1(functionsInstance, name, options) {\n  const callable = data => {\n    return call(functionsInstance, name, data, options || {});\n  };\n  callable.stream = (data, options) => {\n    return stream(functionsInstance, name, data, options);\n  };\n  return callable;\n}\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nfunction httpsCallableFromURL$1(functionsInstance, url, options) {\n  const callable = data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n  callable.stream = (data, options) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable;\n}\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(url, body, headers, fetchImpl) {\n  headers['Content-Type'] = 'application/json';\n  let response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(functionsInstance, options) {\n  const headers = {};\n  const context = await functionsInstance.contextProvider.getContext(options.limitedUseAppCheckTokens);\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(functionsInstance, name, data, options) {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(functionsInstance, url, data, options) {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = {\n    data\n  };\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([postJSON(url, body, headers, functionsInstance.fetchImpl), failAfterHandle.promise, functionsInstance.cancelAllRequests]);\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError('cancelled', 'Firebase Functions instance was deleted.');\n  }\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n  return {\n    data: decodedData\n  };\n}\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(functionsInstance, name, data, options) {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(functionsInstance, url, data, options) {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = {\n    data\n  };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n  let response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver;\n  let resultRejecter;\n  const resultPromise = new Promise((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body.getReader();\n  const rstream = createResponseStream(reader, resultResolver, resultRejecter, options?.signal);\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const {\n              value,\n              done\n            } = await rreader.read();\n            return {\n              value: value,\n              done\n            };\n          },\n          async return() {\n            await rreader.cancel();\n            return {\n              done: true,\n              value: undefined\n            };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(reader, resultResolver, resultRejecter, signal) {\n  const processLine = (line, controller) => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump() {\n        if (signal?.aborted) {\n          const error = new FunctionsError('cancelled', 'Request was cancelled');\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const {\n            value,\n            done\n          } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError('cancelled', 'Request was cancelled');\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, {\n            stream: true\n          });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError = error instanceof FunctionsError ? error : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\nconst name = \"@firebase/functions\";\nconst version = \"0.13.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst AUTH_INTERNAL_NAME = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME = 'app-check-internal';\nconst MESSAGING_INTERNAL_NAME = 'messaging-internal';\nfunction registerFunctions(variant) {\n  const factory = (container, {\n    instanceIdentifier: regionOrCustomDomain\n  }) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(app, authProvider, messagingProvider, appCheckProvider, regionOrCustomDomain);\n  };\n  _registerComponent(new Component(FUNCTIONS_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, 'esm2020');\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nfunction getFunctions(app = getApp(), regionOrCustomDomain = DEFAULT_REGION) {\n  // Dependencies\n  const functionsProvider = _getProvider(getModularInstance(app), FUNCTIONS_TYPE);\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nfunction connectFunctionsEmulator(functionsInstance, host, port) {\n  connectFunctionsEmulator$1(getModularInstance(functionsInstance), host, port);\n}\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nfunction httpsCallable(functionsInstance, name, options) {\n  return httpsCallable$1(getModularInstance(functionsInstance), name, options);\n}\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nfunction httpsCallableFromURL(functionsInstance, url, options) {\n  return httpsCallableFromURL$1(getModularInstance(functionsInstance), url, options);\n}\n\n/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\nregisterFunctions();\nexport { FunctionsError, connectFunctionsEmulator, getFunctions, httpsCallable, httpsCallableFromURL };", "map": {"version": 3, "names": ["LONG_TYPE", "UNSIGNED_LONG_TYPE", "mapValues", "o", "f", "result", "key", "hasOwnProperty", "encode", "data", "Number", "valueOf", "isFinite", "Object", "prototype", "toString", "call", "Date", "toISOString", "Array", "isArray", "map", "x", "Error", "decode", "json", "value", "isNaN", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "FirebaseError", "constructor", "code", "message", "details", "setPrototypeOf", "codeForHTTPStatus", "status", "_errorForResponse", "bodyJSON", "description", "undefined", "errorJSON", "error", "e", "ContextProvider", "app", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "serverAppAppCheckToken", "_isFirebaseServerApp", "settings", "appCheckToken", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "getToken", "accessToken", "getMessagingToken", "self", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "DEFAULT_REGION", "responseLineRE", "failAfter", "millis", "timer", "promise", "Promise", "_", "reject", "setTimeout", "cancel", "clearTimeout", "FunctionsService", "regionOrCustomDomain", "fetchImpl", "args", "fetch", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "resolve", "deleteService", "url", "URL", "customDomain", "origin", "pathname", "region", "_delete", "_url", "name", "projectId", "options", "connectFunctionsEmulator$1", "connectFunctionsEmulator", "functionsInstance", "host", "port", "useSsl", "isCloudWorkstation", "pingServer", "updateEmulatorBanner", "httpsCallable$1", "httpsCallable", "callable", "stream", "httpsCallableFromURL$1", "httpsCallableFromURL", "callAtURL", "streamAtURL", "postJSON", "body", "headers", "response", "method", "JSON", "stringify", "makeAuthHeaders", "context", "timeout", "failAfterHandle", "race", "responseData", "decodedData", "signal", "Symbol", "asyncIterator", "next", "resultResolver", "resultRejecter", "resultPromise", "addEventListener", "reader", "<PERSON><PERSON><PERSON><PERSON>", "rstream", "createResponseStream", "rreader", "done", "read", "return", "processLine", "line", "controller", "match", "jsonData", "parse", "enqueue", "decoder", "TextDecoder", "ReadableStream", "start", "currentText", "pump", "aborted", "trim", "close", "lines", "split", "pop", "functionsError", "AUTH_INTERNAL_NAME", "APP_CHECK_INTERNAL_NAME", "MESSAGING_INTERNAL_NAME", "registerFunctions", "variant", "factory", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "_registerComponent", "Component", "setMultipleInstances", "registerVersion", "version", "getFunctions", "getApp", "functionsProvider", "_get<PERSON><PERSON><PERSON>", "getModularInstance", "identifier", "emulator", "getDefaultEmulatorHostnameAndPort"], "sources": ["C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\serializer.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\error.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\context.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\service.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\config.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\api.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\@firebase\\functions\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport { _isFirebaseServerApp, FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  private serverAppAppCheckToken: string | null = null;\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${\n    useSsl ? 's' : ''\n  }://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions();\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH,MAAMA,SAAS,GAAG,gDAAgD;AAClE,MAAMC,kBAAkB,GAAG,iDAAiD;AAE5E,SAASC,SAASA;AAChB;AACA;AACAC,CAAyB,EACzBC,CAA6B;EAE7B,MAAMC,MAAM,GAA+B,EAAE;EAC7C,KAAK,MAAMC,GAAG,IAAIH,CAAC,EAAE;IACnB,IAAIA,CAAC,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;MACzBD,MAAM,CAACC,GAAG,CAAC,GAAGF,CAAC,CAACD,CAAC,CAACG,GAAG,CAAC,CAAC;;;EAG3B,OAAOD,MAAM;AACf;AAEA;;;;;AAKG;AACG,SAAUG,MAAMA,CAACC,IAAa;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;;EAEb,IAAIA,IAAI,YAAYC,MAAM,EAAE;IAC1BD,IAAI,GAAGA,IAAI,CAACE,OAAO,EAAE;;EAEvB,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAIG,QAAQ,CAACH,IAAI,CAAC,EAAE;;;IAG9C,OAAOA,IAAI;;EAEb,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;IACnC,OAAOA,IAAI;;EAEb,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,IAAI,CAAC,KAAK,iBAAiB,EAAE;IAC9D,OAAOA,IAAI;;EAEb,IAAIA,IAAI,YAAYQ,IAAI,EAAE;IACxB,OAAOR,IAAI,CAACS,WAAW,EAAE;;EAE3B,IAAIC,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACY,GAAG,CAACC,CAAC,IAAId,MAAM,CAACc,CAAC,CAAC,CAAC;;EAEjC,IAAI,OAAOb,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1D,OAAOP,SAAS,CAACO,IAAK,EAAEa,CAAC,IAAId,MAAM,CAACc,CAAC,CAAC,CAAC;;;EAGzC,MAAM,IAAIC,KAAK,CAAC,kCAAkC,GAAGd,IAAI,CAAC;AAC5D;AAEA;;;;;AAKG;AACG,SAAUe,MAAMA,CAACC,IAAa;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOA,IAAI;;EAEb,IAAKA,IAAmC,CAAC,OAAO,CAAC,EAAE;IACjD,QAASA,IAAmC,CAAC,OAAO,CAAC;MACnD,KAAKzB,SAAS;;MAEd,KAAKC,kBAAkB;QAAE;;;;UAIvB,MAAMyB,KAAK,GAAGhB,MAAM,CAAEe,IAAmC,CAAC,OAAO,CAAC,CAAC;UACnE,IAAIE,KAAK,CAACD,KAAK,CAAC,EAAE;YAChB,MAAM,IAAIH,KAAK,CAAC,oCAAoC,GAAGE,IAAI,CAAC;;UAE9D,OAAOC,KAAK;;MAEd;QAAS;UACP,MAAM,IAAIH,KAAK,CAAC,oCAAoC,GAAGE,IAAI,CAAC;;;;EAIlE,IAAIN,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACJ,GAAG,CAACC,CAAC,IAAIE,MAAM,CAACF,CAAC,CAAC,CAAC;;EAEjC,IAAI,OAAOG,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1D,OAAOvB,SAAS,CAACuB,IAAK,EAAEH,CAAC,IAAIE,MAAM,CAACF,CAAC,CAAC,CAAC;;;EAGzC,OAAOG,IAAI;AACb;;AC5GA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAMG,cAAc,GAAG,WAAW;;ACpBzC;;;;;;;;;;;;;;;AAeG;AAQH;;;;;;AAMG;AACH,MAAMC,YAAY,GAA2C;EAC3DC,EAAE,EAAE,IAAI;EACRC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,gBAAgB,EAAE,kBAAkB;EACpCC,iBAAiB,EAAE,mBAAmB;EACtCC,SAAS,EAAE,WAAW;EACtBC,cAAc,EAAE,gBAAgB;EAChCC,iBAAiB,EAAE,mBAAmB;EACtCC,eAAe,EAAE,iBAAiB;EAClCC,kBAAkB,EAAE,oBAAoB;EACxCC,mBAAmB,EAAE,qBAAqB;EAC1CC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,eAAe;EAC9BC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE;CACZ;AAED;;;;;;AAMG;AACG,MAAOC,cAAe,SAAQC,aAAa;EAC/C;;AAEG;EACHC;EACE;;;AAGG;EACHC,IAAwB,EACxBC,OAAgB;EAChB;;AAEG;EACMC,OAAiB;IAE1B,KAAK,CAAC,GAAGxB,cAAc,IAAIsB,IAAI,EAAE,EAAEC,OAAO,IAAI,EAAE,CAAC;IAFxC,IAAO,CAAAC,OAAA,GAAPA,OAAO;;;IAMhBvC,MAAM,CAACwC,cAAc,CAAC,IAAI,EAAEN,cAAc,CAACjC,SAAS,CAAC;;AAExD;AAED;;;;;;;AAOG;AACH,SAASwC,iBAAiBA,CAACC,MAAc;;EAEvC,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;IACjC,OAAO,IAAI;;EAEb,QAAQA,MAAM;IACZ,KAAK,CAAC;;MAEJ,OAAO,UAAU;IACnB,KAAK,GAAG;MACN,OAAO,kBAAkB;IAC3B,KAAK,GAAG;MACN,OAAO,iBAAiB;IAC1B,KAAK,GAAG;MACN,OAAO,mBAAmB;IAC5B,KAAK,GAAG;MACN,OAAO,WAAW;IACpB,KAAK,GAAG;MACN,OAAO,SAAS;IAClB,KAAK,GAAG;MACN,OAAO,oBAAoB;IAC7B,KAAK,GAAG;MACN,OAAO,WAAW;IACpB,KAAK,GAAG;MACN,OAAO,UAAU;IACnB,KAAK,GAAG;MACN,OAAO,eAAe;IACxB,KAAK,GAAG;MACN,OAAO,aAAa;IACtB,KAAK,GAAG;MACN,OAAO,mBAAmB;;EAG9B,OAAO,SAAS;AAClB;AAEA;;AAEG;AACa,SAAAC,iBAAiBA,CAC/BD,MAAc,EACdE,QAAiC;EAEjC,IAAIP,IAAI,GAAGI,iBAAiB,CAACC,MAAM,CAAC;;EAGpC,IAAIG,WAAW,GAAWR,IAAI;EAE9B,IAAIE,OAAO,GAAYO,SAAS;;EAGhC,IAAI;IACF,MAAMC,SAAS,GAAGH,QAAQ,IAAIA,QAAQ,CAACI,KAAK;IAC5C,IAAID,SAAS,EAAE;MACb,MAAML,MAAM,GAAGK,SAAS,CAACL,MAAM;MAC/B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,CAAC1B,YAAY,CAAC0B,MAAM,CAAC,EAAE;;UAEzB,OAAO,IAAIR,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC;;QAEnDG,IAAI,GAAGrB,YAAY,CAAC0B,MAAM,CAAC;;;QAI3BG,WAAW,GAAGH,MAAM;;MAGtB,MAAMJ,OAAO,GAAGS,SAAS,CAACT,OAAO;MACjC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BO,WAAW,GAAGP,OAAO;;MAGvBC,OAAO,GAAGQ,SAAS,CAACR,OAAO;MAC3B,IAAIA,OAAO,KAAKO,SAAS,EAAE;QACzBP,OAAO,GAAG5B,MAAM,CAAC4B,OAAO,CAAC;;;GAG9B,CAAC,OAAOU,CAAC,EAAE;;;EAIZ,IAAIZ,IAAI,KAAK,IAAI,EAAE;;;;IAIjB,OAAO,IAAI;;EAGb,OAAO,IAAIH,cAAc,CAACG,IAAI,EAAEQ,WAAW,EAAEN,OAAO,CAAC;AACvD;;AClLA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;MACUW,eAAe;EAK1Bd,YACWe,GAAgB,EACzBC,YAAgD,EAChDC,iBAA2D,EAC3DC,gBAAyD;IAHhD,IAAG,CAAAH,GAAA,GAAHA,GAAG;IALN,IAAI,CAAAI,IAAA,GAAgC,IAAI;IACxC,IAAS,CAAAC,SAAA,GAA6B,IAAI;IAC1C,IAAQ,CAAAC,QAAA,GAAoC,IAAI;IAChD,IAAsB,CAAAC,sBAAA,GAAkB,IAAI;IAOlD,IAAIC,oBAAoB,CAACR,GAAG,CAAC,IAAIA,GAAG,CAACS,QAAQ,CAACC,aAAa,EAAE;MAC3D,IAAI,CAACH,sBAAsB,GAAGP,GAAG,CAACS,QAAQ,CAACC,aAAa;;IAE1D,IAAI,CAACN,IAAI,GAAGH,YAAY,CAACU,YAAY,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IACzD,IAAI,CAACP,SAAS,GAAGH,iBAAiB,CAACS,YAAY,CAAC;MAC9CC,QAAQ,EAAE;IACX,EAAC;IAEF,IAAI,CAAC,IAAI,CAACR,IAAI,EAAE;MACdH,YAAY,CAACY,GAAG,EAAE,CAACC,IAAI,CACrBV,IAAI,IAAK,IAAI,CAACA,IAAI,GAAGA,IAAK,EAC1B,MAAK;;OAEJ,CACF;;IAGH,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACnBH,iBAAiB,CAACW,GAAG,EAAE,CAACC,IAAI,CAC1BT,SAAS,IAAK,IAAI,CAACA,SAAS,GAAGA,SAAU,EACzC,MAAK;;OAEJ,CACF;;IAGH,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClBH,gBAAgB,EAAEU,GAAG,EAAE,CAACC,IAAI,CAC1BR,QAAQ,IAAK,IAAI,CAACA,QAAQ,GAAGA,QAAS,EACtC,MAAK;;OAEJ,CACF;;;EAIL,MAAMS,YAAYA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACX,IAAI,EAAE;MACd,OAAOT,SAAS;;IAGlB,IAAI;MACF,MAAMqB,KAAK,GAAG,MAAM,IAAI,CAACZ,IAAI,CAACa,QAAQ,EAAE;MACxC,OAAOD,KAAK,EAAEE,WAAW;KAC1B,CAAC,OAAOpB,CAAC,EAAE;;MAEV,OAAOH,SAAS;;;EAIpB,MAAMwB,iBAAiBA,CAAA;IACrB,IACE,CAAC,IAAI,CAACd,SAAS,IACf,EAAE,cAAc,IAAIe,IAAI,CAAC,IACzBC,YAAY,CAACC,UAAU,KAAK,SAAS,EACrC;MACA,OAAO3B,SAAS;;IAGlB,IAAI;MACF,OAAO,MAAM,IAAI,CAACU,SAAS,CAACY,QAAQ,EAAE;KACvC,CAAC,OAAOnB,CAAC,EAAE;;;;MAKV,OAAOH,SAAS;;;EAIpB,MAAM4B,gBAAgBA,CACpBC,wBAAkC;IAElC,IAAI,IAAI,CAACjB,sBAAsB,EAAE;MAC/B,OAAO,IAAI,CAACA,sBAAsB;;IAEpC,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjB,MAAMjE,MAAM,GAAGmF,wBAAwB,GACnC,MAAM,IAAI,CAAClB,QAAQ,CAACmB,kBAAkB,EAAE,GACxC,MAAM,IAAI,CAACnB,QAAQ,CAACW,QAAQ,EAAE;MAClC,IAAI5E,MAAM,CAACwD,KAAK,EAAE;;;;QAIhB,OAAO,IAAI;;MAEb,OAAOxD,MAAM,CAAC2E,KAAK;;IAErB,OAAO,IAAI;;EAGb,MAAMU,UAAUA,CAACF,wBAAkC;IACjD,MAAMG,SAAS,GAAG,MAAM,IAAI,CAACZ,YAAY,EAAE;IAC3C,MAAMa,cAAc,GAAG,MAAM,IAAI,CAACT,iBAAiB,EAAE;IACrD,MAAMT,aAAa,GAAG,MAAM,IAAI,CAACa,gBAAgB,CAACC,wBAAwB,CAAC;IAC3E,OAAO;MAAEG,SAAS;MAAEC,cAAc;MAAElB;IAAa,CAAE;;AAEtD;;AC1JD;;;;;;;;;;;;;;;AAeG;AAuBI,MAAMmB,cAAc,GAAG,aAAa;AAE3C,MAAMC,cAAc,GAAG,sBAAsB;AA6B7C;;;;;AAKG;AACH,SAASC,SAASA,CAACC,MAAc;;;;EAI/B,IAAIC,KAAK,GAAe,IAAI;EAC5B,OAAO;IACLC,OAAO,EAAE,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAI;MACjCJ,KAAK,GAAGK,UAAU,CAAC,MAAK;QACtBD,MAAM,CAAC,IAAItD,cAAc,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;OACrE,EAAEiD,MAAM,CAAC;IACZ,CAAC,CAAC;IACFO,MAAM,EAAEA,CAAA,KAAK;MACX,IAAIN,KAAK,EAAE;QACTO,YAAY,CAACP,KAAK,CAAC;;;GAGxB;AACH;AAEA;;;AAGG;MACUQ,gBAAgB;EAQ3B;;;AAGG;EACHxD,WACWA,CAAAe,GAAgB,EACzBC,YAAgD,EAChDC,iBAA2D,EAC3DC,gBAAyD,EACzDuC,oBAAA,GAA+Bb,cAAc,EACpCc,SAAA,GAA0BA,CAAC,GAAGC,IAAI,KAAKC,KAAK,CAAC,GAAGD,IAAI,CAAC;IALrD,IAAG,CAAA5C,GAAA,GAAHA,GAAG;IAKH,IAAS,CAAA2C,SAAA,GAATA,SAAS;IAhBpB,IAAc,CAAAG,cAAA,GAAkB,IAAI;IAkBlC,IAAI,CAACC,eAAe,GAAG,IAAIhD,eAAe,CACxCC,GAAG,EACHC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,CACjB;;IAED,IAAI,CAAC6C,iBAAiB,GAAG,IAAIb,OAAO,CAACc,OAAO,IAAG;MAC7C,IAAI,CAACC,aAAa,GAAG,MAAK;QACxB,OAAOf,OAAO,CAACc,OAAO,CAACA,OAAO,EAAE,CAAC;MACnC,CAAC;IACH,CAAC,CAAC;;IAGF,IAAI;MACF,MAAME,GAAG,GAAG,IAAIC,GAAG,CAACV,oBAAoB,CAAC;MACzC,IAAI,CAACW,YAAY,GACfF,GAAG,CAACG,MAAM,IAAIH,GAAG,CAACI,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAGJ,GAAG,CAACI,QAAQ,CAAC;MACzD,IAAI,CAACC,MAAM,GAAG3B,cAAc;KAC7B,CAAC,OAAO/B,CAAC,EAAE;MACV,IAAI,CAACuD,YAAY,GAAG,IAAI;MACxB,IAAI,CAACG,MAAM,GAAGd,oBAAoB;;;EAItCe,OAAOA,CAAA;IACL,OAAO,IAAI,CAACP,aAAa,EAAE;;EAG7B;;;;AAIG;EACHQ,IAAIA,CAACC,IAAY;IACf,MAAMC,SAAS,GAAG,IAAI,CAAC5D,GAAG,CAAC6D,OAAO,CAACD,SAAS;IAC5C,IAAI,IAAI,CAACd,cAAc,KAAK,IAAI,EAAE;MAChC,MAAMQ,MAAM,GAAG,IAAI,CAACR,cAAc;MAClC,OAAO,GAAGQ,MAAM,IAAIM,SAAS,IAAI,IAAI,CAACJ,MAAM,IAAIG,IAAI,EAAE;;IAGxD,IAAI,IAAI,CAACN,YAAY,KAAK,IAAI,EAAE;MAC9B,OAAO,GAAG,IAAI,CAACA,YAAY,IAAIM,IAAI,EAAE;;IAGvC,OAAO,WAAW,IAAI,CAACH,MAAM,IAAII,SAAS,uBAAuBD,IAAI,EAAE;;AAE1E;AAED;;;;;;;;AAQG;SACaG,0BAAwBC,CACtCC,iBAAmC,EACnCC,IAAY,EACZC,IAAY;EAEZ,MAAMC,MAAM,GAAGC,kBAAkB,CAACH,IAAI,CAAC;EACvCD,iBAAiB,CAAClB,cAAc,GAAG,OACjCqB,MAAM,GAAG,GAAG,GAAG,EACjB,MAAMF,IAAI,IAAIC,IAAI,EAAE;;EAEpB,IAAIC,MAAM,EAAE;IACV,KAAKE,UAAU,CAACL,iBAAiB,CAAClB,cAAc,CAAC;IACjDwB,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC;;AAE3C;AAEA;;;;AAIG;SACaC,eAAaC,CAC3BR,iBAAmC,EACnCL,IAAY,EACZE,OAA8B;EAE9B,MAAMY,QAAQ,GACZhI,IAAyB,IACO;IAChC,OAAOO,IAAI,CAACgH,iBAAiB,EAAEL,IAAI,EAAElH,IAAI,EAAEoH,OAAO,IAAI,EAAE,CAAC;EAC3D,CAAC;EAEDY,QAAQ,CAACC,MAAM,GAAG,CAChBjI,IAAyB,EACzBoH,OAAoC,KAClC;IACF,OAAOa,MAAM,CAACV,iBAAiB,EAAEL,IAAI,EAAElH,IAAI,EAAEoH,OAAO,CAAC;EACvD,CAAC;EAED,OAAOY,QAAgE;AACzE;AAEA;;;;AAIG;SACaE,sBAAoBC,CAKlCZ,iBAAmC,EACnCb,GAAW,EACXU,OAA8B;EAE9B,MAAMY,QAAQ,GACZhI,IAAyB,IACO;IAChC,OAAOoI,SAAS,CAACb,iBAAiB,EAAEb,GAAG,EAAE1G,IAAI,EAAEoH,OAAO,IAAI,EAAE,CAAC;EAC/D,CAAC;EAEDY,QAAQ,CAACC,MAAM,GAAG,CAChBjI,IAAyB,EACzBoH,OAAoC,KAClC;IACF,OAAOiB,WAAW,CAACd,iBAAiB,EAAEb,GAAG,EAAE1G,IAAI,EAAEoH,OAAO,IAAI,EAAE,CAAC;EACjE,CAAC;EACD,OAAOY,QAAgE;AACzE;AAEA;;;;;;AAMG;AACH,eAAeM,QAAQA,CACrB5B,GAAW,EACX6B,IAAa,EACbC,OAAkC,EAClCtC,SAAuB;EAEvBsC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EAE5C,IAAIC,QAAkB;EACtB,IAAI;IACFA,QAAQ,GAAG,MAAMvC,SAAS,CAACQ,GAAG,EAAE;MAC9BgC,MAAM,EAAE,MAAM;MACdH,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC;MAC1BC;IACD,EAAC;GACH,CAAC,OAAOnF,CAAC,EAAE;;;;;IAKV,OAAO;MACLP,MAAM,EAAE,CAAC;MACT9B,IAAI,EAAE;KACP;;EAEH,IAAIA,IAAI,GAA4B,IAAI;EACxC,IAAI;IACFA,IAAI,GAAG,MAAMyH,QAAQ,CAACzH,IAAI,EAAE;GAC7B,CAAC,OAAOqC,CAAC,EAAE;;;EAGZ,OAAO;IACLP,MAAM,EAAE2F,QAAQ,CAAC3F,MAAM;IACvB9B;GACD;AACH;AAEA;;;;;AAKG;AACH,eAAe6H,eAAeA,CAC5BtB,iBAAmC,EACnCH,OAA6B;EAE7B,MAAMoB,OAAO,GAA2B,EAAE;EAC1C,MAAMM,OAAO,GAAG,MAAMvB,iBAAiB,CAACjB,eAAe,CAACrB,UAAU,CAChEmC,OAAO,CAACrC,wBAAwB,CACjC;EACD,IAAI+D,OAAO,CAAC5D,SAAS,EAAE;IACrBsD,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGM,OAAO,CAAC5D,SAAS;;EAE1D,IAAI4D,OAAO,CAAC3D,cAAc,EAAE;IAC1BqD,OAAO,CAAC,4BAA4B,CAAC,GAAGM,OAAO,CAAC3D,cAAc;;EAEhE,IAAI2D,OAAO,CAAC7E,aAAa,KAAK,IAAI,EAAE;IAClCuE,OAAO,CAAC,qBAAqB,CAAC,GAAGM,OAAO,CAAC7E,aAAa;;EAExD,OAAOuE,OAAO;AAChB;AAEA;;;;AAIG;AACH,SAASjI,IAAIA,CACXgH,iBAAmC,EACnCL,IAAY,EACZlH,IAAa,EACboH,OAA6B;EAE7B,MAAMV,GAAG,GAAGa,iBAAiB,CAACN,IAAI,CAACC,IAAI,CAAC;EACxC,OAAOkB,SAAS,CAACb,iBAAiB,EAAEb,GAAG,EAAE1G,IAAI,EAAEoH,OAAO,CAAC;AACzD;AAEA;;;;AAIG;AACH,eAAegB,SAASA,CACtBb,iBAAmC,EACnCb,GAAW,EACX1G,IAAa,EACboH,OAA6B;;EAG7BpH,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC;EACnB,MAAMuI,IAAI,GAAG;IAAEvI;EAAI,CAAE;;EAGrB,MAAMwI,OAAO,GAAG,MAAMK,eAAe,CAACtB,iBAAiB,EAAEH,OAAO,CAAC;;EAGjE,MAAM2B,OAAO,GAAG3B,OAAO,CAAC2B,OAAO,IAAI,KAAK;EAExC,MAAMC,eAAe,GAAG1D,SAAS,CAACyD,OAAO,CAAC;EAC1C,MAAMN,QAAQ,GAAG,MAAM/C,OAAO,CAACuD,IAAI,CAAC,CAClCX,QAAQ,CAAC5B,GAAG,EAAE6B,IAAI,EAAEC,OAAO,EAAEjB,iBAAiB,CAACrB,SAAS,CAAC,EACzD8C,eAAe,CAACvD,OAAO,EACvB8B,iBAAiB,CAAChB,iBAAiB,CACpC,CAAC;;EAGFyC,eAAe,CAAClD,MAAM,EAAE;;EAGxB,IAAI,CAAC2C,QAAQ,EAAE;IACb,MAAM,IAAInG,cAAc,CACtB,WAAW,EACX,0CAA0C,CAC3C;;;EAIH,MAAMc,KAAK,GAAGL,iBAAiB,CAAC0F,QAAQ,CAAC3F,MAAM,EAAE2F,QAAQ,CAACzH,IAAI,CAAC;EAC/D,IAAIoC,KAAK,EAAE;IACT,MAAMA,KAAK;;EAGb,IAAI,CAACqF,QAAQ,CAACzH,IAAI,EAAE;IAClB,MAAM,IAAIsB,cAAc,CAAC,UAAU,EAAE,oCAAoC,CAAC;;EAG5E,IAAI4G,YAAY,GAAGT,QAAQ,CAACzH,IAAI,CAAChB,IAAI;;;EAGrC,IAAI,OAAOkJ,YAAY,KAAK,WAAW,EAAE;IACvCA,YAAY,GAAGT,QAAQ,CAACzH,IAAI,CAACpB,MAAM;;EAErC,IAAI,OAAOsJ,YAAY,KAAK,WAAW,EAAE;;IAEvC,MAAM,IAAI5G,cAAc,CAAC,UAAU,EAAE,iCAAiC,CAAC;;;EAIzE,MAAM6G,WAAW,GAAGpI,MAAM,CAACmI,YAAY,CAAC;EAExC,OAAO;IAAElJ,IAAI,EAAEmJ;EAAW,CAAE;AAC9B;AAEA;;;;;AAKG;AACH,SAASlB,MAAMA,CACbV,iBAAmC,EACnCL,IAAY,EACZlH,IAAa,EACboH,OAAoC;EAEpC,MAAMV,GAAG,GAAGa,iBAAiB,CAACN,IAAI,CAACC,IAAI,CAAC;EACxC,OAAOmB,WAAW,CAACd,iBAAiB,EAAEb,GAAG,EAAE1G,IAAI,EAAEoH,OAAO,IAAI,EAAE,CAAC;AACjE;AAEA;;;;;AAKG;AACH,eAAeiB,WAAWA,CACxBd,iBAAmC,EACnCb,GAAW,EACX1G,IAAa,EACboH,OAAmC;;EAGnCpH,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC;EACnB,MAAMuI,IAAI,GAAG;IAAEvI;EAAI,CAAE;;;EAGrB,MAAMwI,OAAO,GAAG,MAAMK,eAAe,CAACtB,iBAAiB,EAAEH,OAAO,CAAC;EACjEoB,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EAC5CA,OAAO,CAAC,QAAQ,CAAC,GAAG,mBAAmB;EAEvC,IAAIC,QAAkB;EACtB,IAAI;IACFA,QAAQ,GAAG,MAAMlB,iBAAiB,CAACrB,SAAS,CAACQ,GAAG,EAAE;MAChDgC,MAAM,EAAE,MAAM;MACdH,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC;MAC1BC,OAAO;MACPY,MAAM,EAAEhC,OAAO,EAAEgC;IAClB,EAAC;GACH,CAAC,OAAO/F,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYvC,KAAK,IAAIuC,CAAC,CAAC6D,IAAI,KAAK,YAAY,EAAE;MACjD,MAAM9D,KAAK,GAAG,IAAId,cAAc,CAAC,WAAW,EAAE,wBAAwB,CAAC;MACvE,OAAO;QACLtC,IAAI,EAAE0F,OAAO,CAACE,MAAM,CAACxC,KAAK,CAAC;QAC3B6E,MAAM,EAAE;UACN,CAACoB,MAAM,CAACC,aAAa,IAAC;YACpB,OAAO;cACLC,IAAIA,CAAA;gBACF,OAAO7D,OAAO,CAACE,MAAM,CAACxC,KAAK,CAAC;;aAE/B;;QAEJ;OACF;;;;;;IAMH,MAAMA,KAAK,GAAGL,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC;IACxC,OAAO;MACL/C,IAAI,EAAE0F,OAAO,CAACE,MAAM,CAACxC,KAAK,CAAC;;MAE3B6E,MAAM,EAAE;QACN,CAACoB,MAAM,CAACC,aAAa,IAAC;UACpB,OAAO;YACLC,IAAIA,CAAA;cACF,OAAO7D,OAAO,CAACE,MAAM,CAACxC,KAAK,CAAC;;WAE/B;;MAEJ;KACF;;EAEH,IAAIoG,cAAwC;EAC5C,IAAIC,cAAyC;EAC7C,MAAMC,aAAa,GAAG,IAAIhE,OAAO,CAAU,CAACc,OAAO,EAAEZ,MAAM,KAAI;IAC7D4D,cAAc,GAAGhD,OAAO;IACxBiD,cAAc,GAAG7D,MAAM;EACzB,CAAC,CAAC;EACFwB,OAAO,EAAEgC,MAAM,EAAEO,gBAAgB,CAAC,OAAO,EAAE,MAAK;IAC9C,MAAMvG,KAAK,GAAG,IAAId,cAAc,CAAC,WAAW,EAAE,wBAAwB,CAAC;IACvEmH,cAAc,CAACrG,KAAK,CAAC;EACvB,CAAC,CAAC;EACF,MAAMwG,MAAM,GAAGnB,QAAQ,CAACF,IAAK,CAACsB,SAAS,EAAE;EACzC,MAAMC,OAAO,GAAGC,oBAAoB,CAClCH,MAAM,EACNJ,cAAe,EACfC,cAAe,EACfrC,OAAO,EAAEgC,MAAM,CAChB;EACD,OAAO;IACLnB,MAAM,EAAE;MACN,CAACoB,MAAM,CAACC,aAAa,IAAC;QACpB,MAAMU,OAAO,GAAGF,OAAO,CAACD,SAAS,EAAE;QACnC,OAAO;UACL,MAAMN,IAAIA,CAAA;YACR,MAAM;cAAEtI,KAAK;cAAEgJ;YAAI,CAAE,GAAG,MAAMD,OAAO,CAACE,IAAI,EAAE;YAC5C,OAAO;cAAEjJ,KAAK,EAAEA,KAAgB;cAAEgJ;YAAI,CAAE;WACzC;UACD,MAAME,MAAMA,CAAA;YACV,MAAMH,OAAO,CAAClE,MAAM,EAAE;YACtB,OAAO;cAAEmE,IAAI,EAAE,IAAI;cAAEhJ,KAAK,EAAEiC;YAAS,CAAE;;SAE1C;;IAEJ;IACDlD,IAAI,EAAE0J;GACP;AACH;AAEA;;;;;;;;;;;;;;AAcG;AACH,SAASK,oBAAoBA,CAC3BH,MAA+C,EAC/CJ,cAAwC,EACxCC,cAAyC,EACzCL,MAAoB;EAEpB,MAAMgB,WAAW,GAAGA,CAClBC,IAAY,EACZC,UAA2C,KACnC;IACR,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK,CAAClF,cAAc,CAAC;;IAExC,IAAI,CAACkF,KAAK,EAAE;MACV;;IAEF,MAAMvK,IAAI,GAAGuK,KAAK,CAAC,CAAC,CAAC;IACrB,IAAI;MACF,MAAMC,QAAQ,GAAG7B,IAAI,CAAC8B,KAAK,CAACzK,IAAI,CAAC;MACjC,IAAI,QAAQ,IAAIwK,QAAQ,EAAE;QACxBhB,cAAc,CAACzI,MAAM,CAACyJ,QAAQ,CAAC5K,MAAM,CAAC,CAAC;QACvC;;MAEF,IAAI,SAAS,IAAI4K,QAAQ,EAAE;QACzBF,UAAU,CAACI,OAAO,CAAC3J,MAAM,CAACyJ,QAAQ,CAAC9H,OAAO,CAAC,CAAC;QAC5C;;MAEF,IAAI,OAAO,IAAI8H,QAAQ,EAAE;QACvB,MAAMpH,KAAK,GAAGL,iBAAiB,CAAC,CAAC,EAAEyH,QAAQ,CAAC;QAC5CF,UAAU,CAAClH,KAAK,CAACA,KAAK,CAAC;QACvBqG,cAAc,CAACrG,KAAK,CAAC;QACrB;;KAEH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYd,cAAc,EAAE;QACnCgI,UAAU,CAAClH,KAAK,CAACA,KAAK,CAAC;QACvBqG,cAAc,CAACrG,KAAK,CAAC;QACrB;;;;EAIN,CAAC;EAED,MAAMuH,OAAO,GAAG,IAAIC,WAAW,EAAE;EACjC,OAAO,IAAIC,cAAc,CAAC;IACxBC,KAAKA,CAACR,UAAU;MACd,IAAIS,WAAW,GAAG,EAAE;MACpB,OAAOC,IAAI,EAAE;MACb,eAAeA,IAAIA,CAAA;QACjB,IAAI5B,MAAM,EAAE6B,OAAO,EAAE;UACnB,MAAM7H,KAAK,GAAG,IAAId,cAAc,CAC9B,WAAW,EACX,uBAAuB,CACxB;UACDgI,UAAU,CAAClH,KAAK,CAACA,KAAK,CAAC;UACvBqG,cAAc,CAACrG,KAAK,CAAC;UACrB,OAAOsC,OAAO,CAACc,OAAO,EAAE;;QAE1B,IAAI;UACF,MAAM;YAAEvF,KAAK;YAAEgJ;UAAI,CAAE,GAAG,MAAML,MAAM,CAACM,IAAI,EAAE;UAC3C,IAAID,IAAI,EAAE;YACR,IAAIc,WAAW,CAACG,IAAI,EAAE,EAAE;cACtBd,WAAW,CAACW,WAAW,CAACG,IAAI,EAAE,EAAEZ,UAAU,CAAC;;YAE7CA,UAAU,CAACa,KAAK,EAAE;YAClB;;UAEF,IAAI/B,MAAM,EAAE6B,OAAO,EAAE;YACnB,MAAM7H,KAAK,GAAG,IAAId,cAAc,CAC9B,WAAW,EACX,uBAAuB,CACxB;YACDgI,UAAU,CAAClH,KAAK,CAACA,KAAK,CAAC;YACvBqG,cAAc,CAACrG,KAAK,CAAC;YACrB,MAAMwG,MAAM,CAAC9D,MAAM,EAAE;YACrB;;UAEFiF,WAAW,IAAIJ,OAAO,CAAC5J,MAAM,CAACE,KAAK,EAAE;YAAEgH,MAAM,EAAE;UAAI,CAAE,CAAC;UACtD,MAAMmD,KAAK,GAAGL,WAAW,CAACM,KAAK,CAAC,IAAI,CAAC;UACrCN,WAAW,GAAGK,KAAK,CAACE,GAAG,EAAE,IAAI,EAAE;UAC/B,KAAK,MAAMjB,IAAI,IAAIe,KAAK,EAAE;YACxB,IAAIf,IAAI,CAACa,IAAI,EAAE,EAAE;cACfd,WAAW,CAACC,IAAI,CAACa,IAAI,EAAE,EAAEZ,UAAU,CAAC;;;UAGxC,OAAOU,IAAI,EAAE;SACd,CAAC,OAAO5H,KAAK,EAAE;UACd,MAAMmI,cAAc,GAClBnI,KAAK,YAAYd,cAAc,GAC3Bc,KAAK,GACLL,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC;UAChCuH,UAAU,CAAClH,KAAK,CAACmI,cAAc,CAAC;UAChC9B,cAAc,CAAC8B,cAAc,CAAC;;;KAGnC;IACDzF,MAAMA,CAAA;MACJ,OAAO8D,MAAM,CAAC9D,MAAM,EAAE;;EAEzB,EAAC;AACJ;;;;ACrnBA;;;;;;;;;;;;;;;AAeG;AAgBH,MAAM0F,kBAAkB,GAA6B,eAAe;AACpE,MAAMC,uBAAuB,GAC3B,oBAAoB;AACtB,MAAMC,uBAAuB,GAC3B,oBAAoB;AAEhB,SAAUC,iBAAiBA,CAACC,OAAgB;EAChD,MAAMC,OAAO,GAAiCA,CAC5CC,SAA6B,EAC7B;IAAEC,kBAAkB,EAAE9F;EAAoB,CAAE,KAC1C;;IAEF,MAAM1C,GAAG,GAAGuI,SAAS,CAACE,WAAW,CAAC,KAAK,CAAC,CAAC9H,YAAY,EAAE;IACvD,MAAMV,YAAY,GAAGsI,SAAS,CAACE,WAAW,CAACR,kBAAkB,CAAC;IAC9D,MAAM/H,iBAAiB,GAAGqI,SAAS,CAACE,WAAW,CAACN,uBAAuB,CAAC;IACxE,MAAMhI,gBAAgB,GAAGoI,SAAS,CAACE,WAAW,CAACP,uBAAuB,CAAC;;IAGvE,OAAO,IAAIzF,gBAAgB,CACzBzC,GAAG,EACHC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBuC,oBAAoB,CACrB;EACH,CAAC;EAEDgG,kBAAkB,CAChB,IAAIC,SAAS,CACX/K,cAAc,EACd0K,OAAO,EAER,qCAACM,oBAAoB,CAAC,IAAI,CAAC,CAC7B;EAEDC,eAAe,CAAClF,IAAI,EAAEmF,OAAO,EAAET,OAAO,CAAC;;EAEvCQ,eAAe,CAAClF,IAAI,EAAEmF,OAAO,EAAE,SAAkB,CAAC;AACpD;;ACrEA;;;;;;;;;;;;;;;AAeG;AAsBH;;;;;;;AAOG;AACG,SAAUC,YAAYA,CAC1B/I,GAAA,GAAmBgJ,MAAM,EAAE,EAC3BtG,oBAAA,GAA+Bb,cAAc;;EAG7C,MAAMoH,iBAAiB,GAA0BC,YAAY,CAC3DC,kBAAkB,CAACnJ,GAAG,CAAC,EACvBpC,cAAc,CACf;EACD,MAAMoG,iBAAiB,GAAGiF,iBAAiB,CAACtI,YAAY,CAAC;IACvDyI,UAAU,EAAE1G;EACb,EAAC;EACF,MAAM2G,QAAQ,GAAGC,iCAAiC,CAAC,WAAW,CAAC;EAC/D,IAAID,QAAQ,EAAE;IACZtF,wBAAwB,CAACC,iBAAiB,EAAE,GAAGqF,QAAQ,CAAC;;EAE1D,OAAOrF,iBAAiB;AAC1B;AAEA;;;;;;;;AAQG;SACaD,wBAAwBA,CACtCC,iBAA4B,EAC5BC,IAAY,EACZC,IAAY;EAEZJ,0BAAyB,CACvBqF,kBAAkB,CAAmBnF,iBAAqC,CAAC,EAC3EC,IAAI,EACJC,IAAI,CACL;AACH;AAEA;;;;AAIG;SACaM,aAAaA,CAK3BR,iBAA4B,EAC5BL,IAAY,EACZE,OAA8B;EAE9B,OAAOU,eAAc,CACnB4E,kBAAkB,CAAmBnF,iBAAqC,CAAC,EAC3EL,IAAI,EACJE,OAAO,CACR;AACH;AAEA;;;;AAIG;SACae,oBAAoBA,CAKlCZ,iBAA4B,EAC5Bb,GAAW,EACXU,OAA8B;EAE9B,OAAOc,sBAAqB,CAC1BwE,kBAAkB,CAAmBnF,iBAAqC,CAAC,EAC3Eb,GAAG,EACHU,OAAO,CACR;AACH;;AC7HA;;;;AAIG;AAuBHuE,iBAAiB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}