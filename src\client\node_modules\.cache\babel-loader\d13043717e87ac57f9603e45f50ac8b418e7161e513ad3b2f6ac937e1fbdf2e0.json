{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTelegram, FaPhone, FaMapMarkerAlt, FaInstagram, FaWhatsapp } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport qrCodeImage from '../assets/images/qr-code.jpg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-primary-dark text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: blazeTradeLogo,\n              alt: \"BlazeTrade Logo\",\n              className: \"w-6 h-6 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold\",\n              children: \"BlazeTrade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-4\",\n            children: \"Your trusted partner for Bitcoin exchange and trading services. We provide secure, reliable, and professional cryptocurrency solutions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaInstagram, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 33\n              }, this),\n              href: \"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaWhatsapp, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 33\n              }, this),\n              href: \"https://wa.me/+2348163309355\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaTelegram, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 33\n              }, this),\n              href: \"https://t.me/blazetrad\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/\",\n              label: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/about\",\n              label: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services\",\n              label: \"Our Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/contact\",\n              label: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/privacy-policy\",\n              label: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/terms-of-service\",\n              label: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Our Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services#bitcoin-exchange\",\n              label: \"Bitcoin Exchange\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services#crypto-trading\",\n              label: \"Crypto Trading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services#market-analysis\",\n              label: \"Market Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services#security-solutions\",\n              label: \"Security Solutions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services#consulting\",\n              label: \"Consulting Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Victoria island, Lagos, Nigeria, 101241\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+2348163309355\",\n                className: \"hover:text-accent transition-colors\",\n                children: \"+234 ************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"hover:text-accent transition-colors\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-semibold mb-2\",\n              children: \"Scan to Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: qrCodeImage,\n              alt: \"QR Code for WhatsApp\",\n              className: \"w-24 h-24 rounded-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Follow Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-4\",\n            children: \"Stay updated with our latest news and offers on our official Instagram page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"inline-flex items-center text-accent hover:text-white transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FaInstagram, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), \"@blaze__trade\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400\",\n          children: [\"\\xA9 \", currentYear, \" BlazeTrade. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 md:mt-0\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/privacy-policy\",\n            className: \"text-sm text-gray-400 hover:text-white mr-4\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/terms-of-service\",\n            className: \"text-sm text-gray-400 hover:text-white\",\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nconst SocialIcon = ({\n  icon,\n  href\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"a\", {\n    href: href,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\",\n    children: icon\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SocialIcon;\nconst FooterLink = ({\n  to,\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"li\", {\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: to,\n      className: \"text-gray-300 hover:text-white transition-colors duration-300\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_c3 = FooterLink;\nexport default Footer;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c2, \"SocialIcon\");\n$RefreshReg$(_c3, \"FooterLink\");", "map": {"version": 3, "names": ["React", "Link", "FaTelegram", "FaPhone", "FaMapMarkerAlt", "FaInstagram", "FaWhatsapp", "blazeTradeLogo", "qrCodeImage", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SocialIcon", "icon", "href", "FooterLink", "to", "label", "target", "rel", "_c", "_c2", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTelegram, FaPhone, FaMapMarkerAlt, FaInstagram, FaWhatsapp } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport qrCodeImage from '../assets/images/qr-code.jpg';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-primary-dark text-white\">\n      <div className=\"container-custom py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"w-6 h-6 rounded\" />\n              <span className=\"text-xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Your trusted partner for Bitcoin exchange and trading services. We provide secure, reliable, and professional cryptocurrency solutions.\n            </p>\n            <div className=\"flex space-x-4\">\n              <SocialIcon icon={<FaInstagram />} href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" />\n              <SocialIcon icon={<FaWhatsapp />} href=\"https://wa.me/+2348163309355\" />\n              <SocialIcon icon={<FaTelegram />} href=\"https://t.me/blazetrad\" />\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/\" label=\"Home\" />\n              <FooterLink to=\"/about\" label=\"About Us\" />\n              <FooterLink to=\"/services\" label=\"Our Services\" />\n              <FooterLink to=\"/contact\" label=\"Contact Us\" />\n              <FooterLink to=\"/privacy-policy\" label=\"Privacy Policy\" />\n              <FooterLink to=\"/terms-of-service\" label=\"Terms of Service\" />\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Our Services</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/services#bitcoin-exchange\" label=\"Bitcoin Exchange\" />\n              <FooterLink to=\"/services#crypto-trading\" label=\"Crypto Trading\" />\n              <FooterLink to=\"/services#market-analysis\" label=\"Market Analysis\" />\n              <FooterLink to=\"/services#security-solutions\" label=\"Security Solutions\" />\n              <FooterLink to=\"/services#consulting\" label=\"Consulting Services\" />\n            </ul>\n          </div>\n\n          {/* Contact Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><p>Victoria island, Lagos, Nigeria, 101241</p></li>\n              <li><a href=\"tel:+2348163309355\" className=\"hover:text-accent transition-colors\">+234 ************</a></li>\n              <li><a href=\"mailto:<EMAIL>\" className=\"hover:text-accent transition-colors\"><EMAIL></a></li>\n            </ul>\n            <div className=\"mt-4\">\n              <h4 className=\"text-md font-semibold mb-2\">Scan to Chat</h4>\n              <img src={qrCodeImage} alt=\"QR Code for WhatsApp\" className=\"w-24 h-24 rounded-lg\" />\n            </div>\n          </div>\n\n          {/* Follow Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Follow Us</h3>\n            <p className=\"text-gray-300 mb-4\">Stay updated with our latest news and offers on our official Instagram page.</p>\n            <a href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"inline-flex items-center text-accent hover:text-white transition-colors\">\n              <FaInstagram className=\"mr-2\" />\n              @blaze__trade\n            </a>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-gray-400\">\n            &copy; {currentYear} BlazeTrade. All rights reserved.\n          </p>\n          <div className=\"mt-4 md:mt-0\">\n            <Link to=\"/privacy-policy\" className=\"text-sm text-gray-400 hover:text-white mr-4\">\n              Privacy Policy\n            </Link>\n            <Link to=\"/terms-of-service\" className=\"text-sm text-gray-400 hover:text-white\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nconst SocialIcon = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nconst FooterLink = ({ to, label }) => {\n  return (\n    <li>\n      <Link \n        to={to} \n        className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n      >\n        {label}\n      </Link>\n    </li>\n  );\n};\n\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,OAAO,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAC7F,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eAC5CN,OAAA;MAAKK,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCN,OAAA;QAAKK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBAEnEN,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAKK,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CN,OAAA;cAAKO,GAAG,EAAEV,cAAe;cAACW,GAAG,EAAC,iBAAiB;cAACH,SAAS,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9EZ,OAAA;cAAMK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNZ,OAAA;YAAGK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACL,WAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAsD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGZ,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACJ,UAAU;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAA8B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxEZ,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACR,UAAU;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAwB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,GAAG;cAACC,KAAK,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,QAAQ;cAACC,KAAK,EAAC;YAAU;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,WAAW;cAACC,KAAK,EAAC;YAAc;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,UAAU;cAACC,KAAK,EAAC;YAAY;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,iBAAiB;cAACC,KAAK,EAAC;YAAgB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,mBAAmB;cAACC,KAAK,EAAC;YAAkB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,4BAA4B;cAACC,KAAK,EAAC;YAAkB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvEZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,0BAA0B;cAACC,KAAK,EAAC;YAAgB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnEZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,2BAA2B;cAACC,KAAK,EAAC;YAAiB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,8BAA8B;cAACC,KAAK,EAAC;YAAoB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3EZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,sBAAsB;cAACC,KAAK,EAAC;YAAqB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DZ,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCN,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAAM,QAAA,EAAG;cAAuC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDZ,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAGe,IAAI,EAAC,oBAAoB;gBAACV,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3GZ,OAAA;cAAAM,QAAA,eAAIN,OAAA;gBAAGe,IAAI,EAAC,+BAA+B;gBAACV,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC,eACLZ,OAAA;YAAKK,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBN,OAAA;cAAIK,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DZ,OAAA;cAAKO,GAAG,EAAET,WAAY;cAACU,GAAG,EAAC,sBAAsB;cAACH,SAAS,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDZ,OAAA;YAAGK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA4E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClHZ,OAAA;YAAGe,IAAI,EAAC,sDAAsD;YAACI,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAACf,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBAC1LN,OAAA,CAACL,WAAW;cAACU,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENZ,OAAA;QAAKK,SAAS,EAAC,4FAA4F;QAAAC,QAAA,gBACzGN,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OAC5B,EAACJ,WAAW,EAAC,mCACtB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJZ,OAAA;UAAKK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BN,OAAA,CAACT,IAAI;YAAC0B,EAAE,EAAC,iBAAiB;YAACZ,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAEnF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPZ,OAAA,CAACT,IAAI;YAAC0B,EAAE,EAAC,mBAAmB;YAACZ,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACS,EAAA,GAzFIpB,MAAM;AA2FZ,MAAMY,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EACrC,oBACEf,OAAA;IACEe,IAAI,EAAEA,IAAK;IACXI,MAAM,EAAC,QAAQ;IACfC,GAAG,EAAC,qBAAqB;IACzBf,SAAS,EAAC,kHAAkH;IAAAC,QAAA,EAE3HQ;EAAI;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAER,CAAC;AAACU,GAAA,GAXIT,UAAU;AAahB,MAAMG,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAM,CAAC,KAAK;EACpC,oBACElB,OAAA;IAAAM,QAAA,eACEN,OAAA,CAACT,IAAI;MACH0B,EAAE,EAAEA,EAAG;MACPZ,SAAS,EAAC,+DAA+D;MAAAC,QAAA,EAExEY;IAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAET,CAAC;AAACW,GAAA,GAXIP,UAAU;AAahB,eAAef,MAAM;AAAC,IAAAoB,EAAA,EAAAC,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}