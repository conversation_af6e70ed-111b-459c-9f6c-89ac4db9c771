{"ast": null, "code": "import React from'react';import{<PERSON>a<PERSON><PERSON><PERSON><PERSON>,<PERSON>a<PERSON>nvelope,FaPhone,FaMapMarkerAlt}from'react-icons/fa';import blazeTradeLogo from'../assets/blazetrade-logo.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPageFooter=()=>{const currentYear=new Date().getFullYear();return/*#__PURE__*/_jsx(\"footer\",{className:\"bg-primary-dark text-white py-10\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center md:items-start\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3\",children:[/*#__PURE__*/_jsx(\"img\",{src:blazeTradeLogo,alt:\"BlazeTrade Logo\",className:\"w-7 h-7 rounded\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold\",children:\"BlazeTrade\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm max-w-xs\",children:\"Your trusted partner for Bitcoin exchange and trading services.\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-3\",children:\"Contact Us\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2 text-gray-300\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center justify-center md:justify-start\",children:[/*#__PURE__*/_jsx(FaEnvelope,{className:\"mr-2\"}),/*#__PURE__*/_jsx(\"a\",{href:\"mailto:<EMAIL>\",className:\"hover:text-accent\",children:\"<EMAIL>\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center justify-center md:justify-start\",children:[/*#__PURE__*/_jsx(FaPhone,{className:\"mr-2\"}),/*#__PURE__*/_jsx(\"a\",{href:\"tel:+2348163309355\",className:\"hover:text-accent\",children:\"+234 ************\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-3\",children:\"Find Us\"}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-2 text-gray-300\",children:/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center justify-center md:justify-start\",children:[/*#__PURE__*/_jsx(FaMapMarkerAlt,{className:\"mr-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Victoria Island, Lagos, Nigeria\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"inline-flex items-center text-gray-300 hover:text-accent\",children:[/*#__PURE__*/_jsx(FaInstagram,{className:\"mr-2\"}),\"@blaze__trade\"]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"border-t border-gray-700 mt-8 pt-6 text-center\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-400\",children:[\"\\xA9 \",currentYear,\" BlazeTrade. All rights reserved.\"]})})]})});};export default LandingPageFooter;", "map": {"version": 3, "names": ["React", "FaInstagram", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "blazeTradeLogo", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "href", "target", "rel"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/LandingPageFooter.js"], "sourcesContent": ["import React from 'react';\nimport { FaInstagram, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst LandingPageFooter = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-primary-dark text-white py-10\">\n      <div className=\"container-custom\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left\">\n          \n          {/* Company Info */}\n          <div className=\"flex flex-col items-center md:items-start\">\n            <div className=\"flex items-center space-x-2 mb-3\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"w-7 h-7 rounded\" />\n              <span className=\"text-2xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-400 text-sm max-w-xs\">\n              Your trusted partner for Bitcoin exchange and trading services.\n            </p>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaEnvelope className=\"mr-2\" />\n                <a href=\"mailto:<EMAIL>\" className=\"hover:text-accent\"><EMAIL></a>\n              </li>\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaPhone className=\"mr-2\" />\n                <a href=\"tel:+2348163309355\" className=\"hover:text-accent\">+234 ************</a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Location & Social */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">Find Us</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li className=\"flex items-center justify-center md:justify-start\">\n                <FaMapMarkerAlt className=\"mr-2\" />\n                <span>Victoria Island, Lagos, Nigeria</span>\n              </li>\n            </ul>\n            <div className=\"mt-4\">\n                <a href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"inline-flex items-center text-gray-300 hover:text-accent\">\n                    <FaInstagram className=\"mr-2\" />\n                    @blaze__trade\n                </a>\n            </div>\n          </div>\n\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-8 pt-6 text-center\">\n          <p className=\"text-sm text-gray-400\">\n            &copy; {currentYear} BlazeTrade. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default LandingPageFooter;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,UAAU,CAAEC,OAAO,CAAEC,cAAc,KAAQ,gBAAgB,CACjF,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAE5C,mBACEN,IAAA,WAAQO,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAClDN,KAAA,QAAKK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BN,KAAA,QAAKK,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAG7EN,KAAA,QAAKK,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDN,KAAA,QAAKK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CR,IAAA,QAAKS,GAAG,CAAEX,cAAe,CAACY,GAAG,CAAC,iBAAiB,CAACH,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAC9EP,IAAA,SAAMO,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACnD,CAAC,cACNR,IAAA,MAAGO,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,iEAE9C,CAAG,CAAC,EACD,CAAC,cAGNN,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAC1DN,KAAA,OAAIK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCN,KAAA,OAAIK,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC/DR,IAAA,CAACL,UAAU,EAACY,SAAS,CAAC,MAAM,CAAE,CAAC,cAC/BP,IAAA,MAAGW,IAAI,CAAC,+BAA+B,CAACJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,wBAAsB,CAAG,CAAC,EAC9F,CAAC,cACLN,KAAA,OAAIK,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC/DR,IAAA,CAACJ,OAAO,EAACW,SAAS,CAAC,MAAM,CAAE,CAAC,cAC5BP,IAAA,MAAGW,IAAI,CAAC,oBAAoB,CAACJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,EAC9E,CAAC,EACH,CAAC,EACF,CAAC,cAGNN,KAAA,QAAAM,QAAA,eACER,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cACvDR,IAAA,OAAIO,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACrCN,KAAA,OAAIK,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC/DR,IAAA,CAACH,cAAc,EAACU,SAAS,CAAC,MAAM,CAAE,CAAC,cACnCP,IAAA,SAAAQ,QAAA,CAAM,iCAA+B,CAAM,CAAC,EAC1C,CAAC,CACH,CAAC,cACLR,IAAA,QAAKO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBN,KAAA,MAAGS,IAAI,CAAC,sDAAsD,CAACC,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAACN,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACzKR,IAAA,CAACN,WAAW,EAACa,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEpC,EAAG,CAAC,CACH,CAAC,EACH,CAAC,EAEH,CAAC,cAENP,IAAA,QAAKO,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cAC7DN,KAAA,MAAGK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,OAC5B,CAACJ,WAAW,CAAC,mCACtB,EAAG,CAAC,CACD,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}