{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link,useNavigate,useLocation}from'react-router-dom';import{motion}from'framer-motion';import{FaBars,FaTimes}from'react-icons/fa';import blazeTradeLogo from'../assets/blazetrade-logo.png';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Navbar=()=>{const[isOpen,setIsOpen]=useState(false);const[scrolled,setScrolled]=useState(false);const navigate=useNavigate();const token=localStorage.getItem('token');const handleLogout=()=>{localStorage.removeItem('token');navigate('/login');};useEffect(()=>{const handleScroll=()=>{if(window.scrollY>20){setScrolled(true);}else{setScrolled(false);}};window.addEventListener('scroll',handleScroll);return()=>{window.removeEventListener('scroll',handleScroll);};},[]);const toggleMenu=()=>{setIsOpen(!isOpen);};return/*#__PURE__*/_jsxs(\"nav\",{className:`fixed w-full z-50 transition-all duration-300 ${scrolled?'bg-blue-950 shadow-lg py-3':'bg-blue-950/50 backdrop-blur-sm py-4'}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(Link,{to:token?\"/dashboard\":\"/\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"img\",{src:blazeTradeLogo,alt:\"BlazeTrade Logo\",className:\"h-8 object-contain\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-white\",children:\"BlazeTrade\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex items-center space-x-8\",children:token?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavLink,{to:\"/dashboard\",label:\"Home\"}),/*#__PURE__*/_jsx(NavLink,{to:\"/about\",label:\"About\"}),/*#__PURE__*/_jsx(NavLink,{to:\"/services\",label:\"Services\"}),/*#__PURE__*/_jsx(NavLink,{to:\"/contact\",label:\"Contact\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleLogout,className:\"text-white hover:text-blue-300 transition duration-300\",children:\"Logout\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavLink,{to:\"/\",label:\"Home\"}),/*#__PURE__*/_jsx(NavLink,{to:\"/login\",label:\"Login\"}),/*#__PURE__*/_jsx(Link,{to:\"/signup\",className:\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\",children:\"Sign Up\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:hidden flex items-center space-x-4\",children:[!token&&/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"bg-blue-600 text-white text-sm font-semibold px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors\",children:\"Login\"}),/*#__PURE__*/_jsx(\"button\",{onClick:toggleMenu,className:\"text-white focus:outline-none\",children:isOpen?/*#__PURE__*/_jsx(FaTimes,{className:\"h-6 w-6\"}):/*#__PURE__*/_jsx(FaBars,{className:\"h-6 w-6\"})})]})]}),isOpen&&/*#__PURE__*/_jsx(motion.div,{className:\"md:hidden bg-blue-900\",initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},exit:{opacity:0,height:0},transition:{duration:0.3},children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom py-4 flex flex-col space-y-4\",children:token?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(MobileNavLink,{to:\"/dashboard\",label:\"Home\",onClick:toggleMenu}),/*#__PURE__*/_jsx(MobileNavLink,{to:\"/about\",label:\"About\",onClick:toggleMenu}),/*#__PURE__*/_jsx(MobileNavLink,{to:\"/services\",label:\"Services\",onClick:toggleMenu}),/*#__PURE__*/_jsx(MobileNavLink,{to:\"/contact\",label:\"Contact\",onClick:toggleMenu}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{handleLogout();toggleMenu();},className:\"text-white hover:text-blue-300 py-2 block text-left w-full\",children:\"Logout\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(MobileNavLink,{to:\"/\",label:\"Home\",onClick:toggleMenu}),/*#__PURE__*/_jsx(MobileNavLink,{to:\"/login\",label:\"Login\",onClick:toggleMenu}),/*#__PURE__*/_jsx(MobileNavLink,{to:\"/signup\",label:\"Sign Up\",onClick:toggleMenu})]})})})]});};const NavLink=_ref=>{let{to,label}=_ref;const location=useLocation();const isActive=location.pathname===to;return/*#__PURE__*/_jsx(Link,{to:to,className:`text-white hover:text-blue-300 transition duration-300 ${isActive?'text-blue-300':''}`,children:label});};const MobileNavLink=_ref2=>{let{to,label,onClick}=_ref2;const location=useLocation();const isActive=location.pathname===to;return/*#__PURE__*/_jsx(Link,{to:to,className:`py-2 block transition duration-300 ${isActive?'text-blue-300':'text-white'} hover:text-blue-300`,onClick:onClick,children:label});};export default Navbar;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useLocation", "motion", "FaBars", "FaTimes", "blazeTradeLogo", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "isOpen", "setIsOpen", "scrolled", "setScrolled", "navigate", "token", "localStorage", "getItem", "handleLogout", "removeItem", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "toggleMenu", "className", "children", "to", "src", "alt", "NavLink", "label", "onClick", "div", "initial", "opacity", "height", "animate", "exit", "transition", "duration", "MobileNavLink", "_ref", "location", "isActive", "pathname", "_ref2"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Navbar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const navigate = useNavigate();\n  const token = localStorage.getItem('token');\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    navigate('/login');\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 20) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n\n  const toggleMenu = () => {\n    setIsOpen(!isOpen);\n  };\n\n    return (\n    <nav className={`fixed w-full z-50 transition-all duration-300 ${scrolled ? 'bg-blue-950 shadow-lg py-3' : 'bg-blue-950/50 backdrop-blur-sm py-4'}`}>\n      <div className=\"container-custom flex justify-between items-center\">\n        <Link to={token ? \"/dashboard\" : \"/\"} className=\"flex items-center space-x-2\">\n          <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-8 object-contain\" />\n          <span className=\"text-xl font-bold text-white\">BlazeTrade</span>\n        </Link>\n\n        {/* Desktop Menu */}\n        <div className=\"hidden md:flex items-center space-x-8\">\n          {token ? (\n            <>\n              <NavLink to=\"/dashboard\" label=\"Home\" />\n              <NavLink to=\"/about\" label=\"About\" />\n              <NavLink to=\"/services\" label=\"Services\" />\n              <NavLink to=\"/contact\" label=\"Contact\" />\n              <button onClick={handleLogout} className=\"text-white hover:text-blue-300 transition duration-300\">\n                Logout\n              </button>\n            </>\n          ) : (\n            <>\n              <NavLink to=\"/\" label=\"Home\" />\n              <NavLink to=\"/login\" label=\"Login\" />\n              <Link to=\"/signup\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\">\n                Sign Up\n              </Link>\n            </>\n          )}\n        </div>\n\n        {/* Mobile Menu Button */}\n        <div className=\"md:hidden flex items-center space-x-4\">\n          {!token && (\n            <Link to=\"/login\" className=\"bg-blue-600 text-white text-sm font-semibold px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors\">\n              Login\n            </Link>\n          )}\n          <button onClick={toggleMenu} className=\"text-white focus:outline-none\">\n            {isOpen ? <FaTimes className=\"h-6 w-6\" /> : <FaBars className=\"h-6 w-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isOpen && (\n        <motion.div \n          className=\"md:hidden bg-blue-900\"\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"container-custom py-4 flex flex-col space-y-4\">\n            {token ? (\n              <>\n                <MobileNavLink to=\"/dashboard\" label=\"Home\" onClick={toggleMenu} />\n                <MobileNavLink to=\"/about\" label=\"About\" onClick={toggleMenu} />\n                <MobileNavLink to=\"/services\" label=\"Services\" onClick={toggleMenu} />\n                <MobileNavLink to=\"/contact\" label=\"Contact\" onClick={toggleMenu} />\n                <button \n                  onClick={() => { handleLogout(); toggleMenu(); }} \n                  className=\"text-white hover:text-blue-300 py-2 block text-left w-full\"\n                >\n                  Logout\n                </button>\n              </>\n            ) : (\n              <>\n                <MobileNavLink to=\"/\" label=\"Home\" onClick={toggleMenu} />\n                <MobileNavLink to=\"/login\" label=\"Login\" onClick={toggleMenu} />\n                <MobileNavLink to=\"/signup\" label=\"Sign Up\" onClick={toggleMenu} />\n              </>\n            )}\n          </div>\n        </motion.div>\n      )}\n    </nav>\n  );\n};\n\nconst NavLink = ({ to, label }) => {\n  const location = useLocation();\n  const isActive = location.pathname === to;\n\n  return (\n    <Link \n      to={to} \n      className={`text-white hover:text-blue-300 transition duration-300 ${isActive ? 'text-blue-300' : ''}`}>\n      {label}\n    </Link>\n  );\n};\n\nconst MobileNavLink = ({ to, label, onClick }) => {\n  const location = useLocation();\n  const isActive = location.pathname === to;\n\n  return (\n    <Link \n      to={to} \n      className={`py-2 block transition duration-300 ${isActive ? 'text-blue-300' : 'text-white'} hover:text-blue-300`}\n      onClick={onClick}\n    >\n      {label}\n    </Link>\n  );\n};\n\nexport default Navbar;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,MAAM,CAAEC,OAAO,KAAQ,gBAAgB,CAChD,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3D,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAAoB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkB,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAE3C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBF,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC,CAChCL,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAEDnB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyB,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIC,MAAM,CAACC,OAAO,CAAG,EAAE,CAAE,CACvBT,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,IAAM,CACLA,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAEDQ,MAAM,CAACE,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAC/C,MAAO,IAAM,CACXC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAK,UAAU,CAAGA,CAAA,GAAM,CACvBd,SAAS,CAAC,CAACD,MAAM,CAAC,CACpB,CAAC,CAEC,mBACAJ,KAAA,QAAKoB,SAAS,CAAE,iDAAiDd,QAAQ,CAAG,4BAA4B,CAAG,sCAAsC,EAAG,CAAAe,QAAA,eAClJrB,KAAA,QAAKoB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjErB,KAAA,CAACV,IAAI,EAACgC,EAAE,CAAEb,KAAK,CAAG,YAAY,CAAG,GAAI,CAACW,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC3EvB,IAAA,QAAKyB,GAAG,CAAE3B,cAAe,CAAC4B,GAAG,CAAC,iBAAiB,CAACJ,SAAS,CAAC,oBAAoB,CAAE,CAAC,cACjFtB,IAAA,SAAMsB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EAC5D,CAAC,cAGPvB,IAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDZ,KAAK,cACJT,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACEvB,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,YAAY,CAACI,KAAK,CAAC,MAAM,CAAE,CAAC,cACxC5B,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,QAAQ,CAACI,KAAK,CAAC,OAAO,CAAE,CAAC,cACrC5B,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,WAAW,CAACI,KAAK,CAAC,UAAU,CAAE,CAAC,cAC3C5B,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,UAAU,CAACI,KAAK,CAAC,SAAS,CAAE,CAAC,cACzC5B,IAAA,WAAQ6B,OAAO,CAAEf,YAAa,CAACQ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,QAElG,CAAQ,CAAC,EACT,CAAC,cAEHrB,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACEvB,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,GAAG,CAACI,KAAK,CAAC,MAAM,CAAE,CAAC,cAC/B5B,IAAA,CAAC2B,OAAO,EAACH,EAAE,CAAC,QAAQ,CAACI,KAAK,CAAC,OAAO,CAAE,CAAC,cACrC5B,IAAA,CAACR,IAAI,EAACgC,EAAE,CAAC,SAAS,CAACF,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAAC,SAE/H,CAAM,CAAC,EACP,CACH,CACE,CAAC,cAGNrB,KAAA,QAAKoB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EACnD,CAACZ,KAAK,eACLX,IAAA,CAACR,IAAI,EAACgC,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,yGAAyG,CAAAC,QAAA,CAAC,OAEtI,CAAM,CACP,cACDvB,IAAA,WAAQ6B,OAAO,CAAER,UAAW,CAACC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CACnEjB,MAAM,cAAGN,IAAA,CAACH,OAAO,EAACyB,SAAS,CAAC,SAAS,CAAE,CAAC,cAAGtB,IAAA,CAACJ,MAAM,EAAC0B,SAAS,CAAC,SAAS,CAAE,CAAC,CACpE,CAAC,EACN,CAAC,EACH,CAAC,CAGLhB,MAAM,eACLN,IAAA,CAACL,MAAM,CAACmC,GAAG,EACTR,SAAS,CAAC,uBAAuB,CACjCS,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAE,CACnCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAO,CAAE,CACxCE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAE,CAChCG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAd,QAAA,cAE9BvB,IAAA,QAAKsB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAC3DZ,KAAK,cACJT,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACEvB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,YAAY,CAACI,KAAK,CAAC,MAAM,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cACnErB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,QAAQ,CAACI,KAAK,CAAC,OAAO,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cAChErB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,WAAW,CAACI,KAAK,CAAC,UAAU,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cACtErB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,UAAU,CAACI,KAAK,CAAC,SAAS,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cACpErB,IAAA,WACE6B,OAAO,CAAEA,CAAA,GAAM,CAAEf,YAAY,CAAC,CAAC,CAAEO,UAAU,CAAC,CAAC,CAAE,CAAE,CACjDC,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CACvE,QAED,CAAQ,CAAC,EACT,CAAC,cAEHrB,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACEvB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,GAAG,CAACI,KAAK,CAAC,MAAM,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cAC1DrB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,QAAQ,CAACI,KAAK,CAAC,OAAO,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,cAChErB,IAAA,CAACsC,aAAa,EAACd,EAAE,CAAC,SAAS,CAACI,KAAK,CAAC,SAAS,CAACC,OAAO,CAAER,UAAW,CAAE,CAAC,EACnE,CACH,CACE,CAAC,CACI,CACb,EACE,CAAC,CAEV,CAAC,CAED,KAAM,CAAAM,OAAO,CAAGY,IAAA,EAAmB,IAAlB,CAAEf,EAAE,CAAEI,KAAM,CAAC,CAAAW,IAAA,CAC5B,KAAM,CAAAC,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,QAAQ,CAAGD,QAAQ,CAACE,QAAQ,GAAKlB,EAAE,CAEzC,mBACExB,IAAA,CAACR,IAAI,EACHgC,EAAE,CAAEA,EAAG,CACPF,SAAS,CAAE,0DAA0DmB,QAAQ,CAAG,eAAe,CAAG,EAAE,EAAG,CAAAlB,QAAA,CACtGK,KAAK,CACF,CAAC,CAEX,CAAC,CAED,KAAM,CAAAU,aAAa,CAAGK,KAAA,EAA4B,IAA3B,CAAEnB,EAAE,CAAEI,KAAK,CAAEC,OAAQ,CAAC,CAAAc,KAAA,CAC3C,KAAM,CAAAH,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,QAAQ,CAAGD,QAAQ,CAACE,QAAQ,GAAKlB,EAAE,CAEzC,mBACExB,IAAA,CAACR,IAAI,EACHgC,EAAE,CAAEA,EAAG,CACPF,SAAS,CAAE,sCAAsCmB,QAAQ,CAAG,eAAe,CAAG,YAAY,sBAAuB,CACjHZ,OAAO,CAAEA,OAAQ,CAAAN,QAAA,CAEhBK,KAAK,CACF,CAAC,CAEX,CAAC,CAED,cAAe,CAAAvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}