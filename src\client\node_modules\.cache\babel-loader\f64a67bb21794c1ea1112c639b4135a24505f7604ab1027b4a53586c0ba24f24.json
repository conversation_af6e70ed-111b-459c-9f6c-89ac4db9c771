{"ast": null, "code": "import React,{useEffect,useState,useRef}from'react';import{use<PERSON>ara<PERSON>,Link}from'react-router-dom';import axios from'axios';import{FaCheckCircle,FaTimesCircle}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EmailVerification=()=>{const{token}=useParams();const[verificationStatus,setVerificationStatus]=useState('verifying');const[message,setMessage]=useState('Verifying your email address...');const effectRan=useRef(false);useEffect(()=>{if(effectRan.current===false){const verifyEmail=async()=>{try{const res=await axios.get(`/api/auth/verify-email/${token}`);setVerificationStatus('success');setMessage(res.data.msg);}catch(err){var _err$response,_err$response$data;setVerificationStatus('error');setMessage(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.msg)||'An error occurred during verification.');}};if(token){verifyEmail();}// Mark that the effect has run\nreturn()=>{effectRan.current=true;};}},[token]);return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gray-900 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",children:[verificationStatus==='verifying'&&/*#__PURE__*/_jsx(\"div\",{className:\"animate-pulse text-blue-500 text-4xl mb-4\",children:\"...\"}),verificationStatus==='success'&&/*#__PURE__*/_jsx(FaCheckCircle,{className:\"text-6xl text-green-500 mx-auto mb-6\"}),verificationStatus==='error'&&/*#__PURE__*/_jsx(FaTimesCircle,{className:\"text-6xl text-red-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold mb-4\",children:verificationStatus==='success'?'Verification Successful':'Verification Failed'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-8\",children:message}),/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\",children:\"Proceed to Login\"})]})});};export default EmailVerification;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useParams", "Link", "axios", "FaCheckCircle", "FaTimesCircle", "jsx", "_jsx", "jsxs", "_jsxs", "EmailVerification", "token", "verificationStatus", "setVerificationStatus", "message", "setMessage", "effectRan", "current", "verifyEmail", "res", "get", "data", "msg", "err", "_err$response", "_err$response$data", "response", "className", "children", "to"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/EmailVerification.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';\n\nconst EmailVerification = () => {\n  const { token } = useParams();\n  const [verificationStatus, setVerificationStatus] = useState('verifying');\n  const [message, setMessage] = useState('Verifying your email address...');\n  const effectRan = useRef(false);\n\n  useEffect(() => {\n    if (effectRan.current === false) {\n      const verifyEmail = async () => {\n        try {\n          const res = await axios.get(`/api/auth/verify-email/${token}`);\n          setVerificationStatus('success');\n          setMessage(res.data.msg);\n        } catch (err) {\n          setVerificationStatus('error');\n          setMessage(err.response?.data?.msg || 'An error occurred during verification.');\n        }\n      };\n\n      if (token) {\n        verifyEmail();\n      }\n\n      // Mark that the effect has run\n      return () => {\n        effectRan.current = true;\n      };\n    }\n  }, [token]);\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n        {verificationStatus === 'verifying' && (\n          <div className=\"animate-pulse text-blue-500 text-4xl mb-4\">...</div>\n        )}\n        {verificationStatus === 'success' && (\n          <FaCheckCircle className=\"text-6xl text-green-500 mx-auto mb-6\" />\n        )}\n        {verificationStatus === 'error' && (\n          <FaTimesCircle className=\"text-6xl text-red-500 mx-auto mb-6\" />\n        )}\n        <h1 className=\"text-2xl font-bold mb-4\">\n          {verificationStatus === 'success' ? 'Verification Successful' : 'Verification Failed'}\n        </h1>\n        <p className=\"text-gray-300 mb-8\">{message}</p>\n        <Link to=\"/login\" className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\">\n          Proceed to Login\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default EmailVerification;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,CAAEC,aAAa,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,KAAM,CAAC,CAAGV,SAAS,CAAC,CAAC,CAC7B,KAAM,CAACW,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGd,QAAQ,CAAC,WAAW,CAAC,CACzE,KAAM,CAACe,OAAO,CAAEC,UAAU,CAAC,CAAGhB,QAAQ,CAAC,iCAAiC,CAAC,CACzE,KAAM,CAAAiB,SAAS,CAAGhB,MAAM,CAAC,KAAK,CAAC,CAE/BF,SAAS,CAAC,IAAM,CACd,GAAIkB,SAAS,CAACC,OAAO,GAAK,KAAK,CAAE,CAC/B,KAAM,CAAAC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,GAAG,CAAG,KAAM,CAAAhB,KAAK,CAACiB,GAAG,CAAC,0BAA0BT,KAAK,EAAE,CAAC,CAC9DE,qBAAqB,CAAC,SAAS,CAAC,CAChCE,UAAU,CAACI,GAAG,CAACE,IAAI,CAACC,GAAG,CAAC,CAC1B,CAAE,MAAOC,GAAG,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACZZ,qBAAqB,CAAC,OAAO,CAAC,CAC9BE,UAAU,CAAC,EAAAS,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcH,IAAI,UAAAI,kBAAA,iBAAlBA,kBAAA,CAAoBH,GAAG,GAAI,wCAAwC,CAAC,CACjF,CACF,CAAC,CAED,GAAIX,KAAK,CAAE,CACTO,WAAW,CAAC,CAAC,CACf,CAEA;AACA,MAAO,IAAM,CACXF,SAAS,CAACC,OAAO,CAAG,IAAI,CAC1B,CAAC,CACH,CACF,CAAC,CAAE,CAACN,KAAK,CAAC,CAAC,CAEX,mBACEJ,IAAA,QAAKoB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFnB,KAAA,QAAKkB,SAAS,CAAC,kEAAkE,CAAAC,QAAA,EAC9EhB,kBAAkB,GAAK,WAAW,eACjCL,IAAA,QAAKoB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,KAAG,CAAK,CACpE,CACAhB,kBAAkB,GAAK,SAAS,eAC/BL,IAAA,CAACH,aAAa,EAACuB,SAAS,CAAC,sCAAsC,CAAE,CAClE,CACAf,kBAAkB,GAAK,OAAO,eAC7BL,IAAA,CAACF,aAAa,EAACsB,SAAS,CAAC,oCAAoC,CAAE,CAChE,cACDpB,IAAA,OAAIoB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACpChB,kBAAkB,GAAK,SAAS,CAAG,yBAAyB,CAAG,qBAAqB,CACnF,CAAC,cACLL,IAAA,MAAGoB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEd,OAAO,CAAI,CAAC,cAC/CP,IAAA,CAACL,IAAI,EAAC2B,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAAC,kBAE9H,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}