{"ast": null, "code": "import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst svgMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState,\n    onMount: (props, instance, _ref) => {\n      let {\n        renderState,\n        latestValues\n      } = _ref;\n      frame.read(() => {\n        try {\n          renderState.dimensions = typeof instance.getBBox === \"function\" ? instance.getBBox() : instance.getBoundingClientRect();\n        } catch (e) {\n          // Most likely trying to measure an unrendered element under Firefox\n          renderState.dimensions = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n          };\n        }\n      });\n      frame.render(() => {\n        buildSVGAttrs(renderState, latestValues, {\n          enableHardwareAcceleration: false\n        }, isSVGTag(instance.tagName), props.transformTemplate);\n        renderSVG(instance, renderState);\n      });\n    }\n  })\n};\nexport { svgMotionConfig };", "map": {"version": 3, "names": ["renderSVG", "scrapeMotionValuesFromProps", "makeUseVisualState", "createSvgRenderState", "buildSVGAttrs", "isSVGTag", "frame", "svgMotionConfig", "useVisualState", "createRenderState", "onMount", "props", "instance", "_ref", "renderState", "latestValues", "read", "dimensions", "getBBox", "getBoundingClientRect", "e", "x", "y", "width", "height", "render", "enableHardwareAcceleration", "tagName", "transformTemplate"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n        onMount: (props, instance, { renderState, latestValues }) => {\n            frame.read(() => {\n                try {\n                    renderState.dimensions =\n                        typeof instance.getBBox ===\n                            \"function\"\n                            ? instance.getBBox()\n                            : instance.getBoundingClientRect();\n                }\n                catch (e) {\n                    // Most likely trying to measure an unrendered element under Firefox\n                    renderState.dimensions = {\n                        x: 0,\n                        y: 0,\n                        width: 0,\n                        height: 0,\n                    };\n                }\n            });\n            frame.render(() => {\n                buildSVGAttrs(renderState, latestValues, { enableHardwareAcceleration: false }, isSVGTag(instance.tagName), props.transformTemplate);\n                renderSVG(instance, renderState);\n            });\n        },\n    }),\n};\n\nexport { svgMotionConfig };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,eAAe,GAAG;EACpBC,cAAc,EAAEN,kBAAkB,CAAC;IAC/BD,2BAA2B,EAAEA,2BAA2B;IACxDQ,iBAAiB,EAAEN,oBAAoB;IACvCO,OAAO,EAAEA,CAACC,KAAK,EAAEC,QAAQ,EAAAC,IAAA,KAAoC;MAAA,IAAlC;QAAEC,WAAW;QAAEC;MAAa,CAAC,GAAAF,IAAA;MACpDP,KAAK,CAACU,IAAI,CAAC,MAAM;QACb,IAAI;UACAF,WAAW,CAACG,UAAU,GAClB,OAAOL,QAAQ,CAACM,OAAO,KACnB,UAAU,GACRN,QAAQ,CAACM,OAAO,CAAC,CAAC,GAClBN,QAAQ,CAACO,qBAAqB,CAAC,CAAC;QAC9C,CAAC,CACD,OAAOC,CAAC,EAAE;UACN;UACAN,WAAW,CAACG,UAAU,GAAG;YACrBI,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE;UACZ,CAAC;QACL;MACJ,CAAC,CAAC;MACFlB,KAAK,CAACmB,MAAM,CAAC,MAAM;QACfrB,aAAa,CAACU,WAAW,EAAEC,YAAY,EAAE;UAAEW,0BAA0B,EAAE;QAAM,CAAC,EAAErB,QAAQ,CAACO,QAAQ,CAACe,OAAO,CAAC,EAAEhB,KAAK,CAACiB,iBAAiB,CAAC;QACpI5B,SAAS,CAACY,QAAQ,EAAEE,WAAW,CAAC;MACpC,CAAC,CAAC;IACN;EACJ,CAAC;AACL,CAAC;AAED,SAASP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}