{"ast": null, "code": "export { createDomMotionComponent, motion } from './render/dom/motion.mjs';\nexport { m } from './render/dom/motion-minimal.mjs';\nexport { AnimatePresence } from './components/AnimatePresence/index.mjs';\nexport { MotionConfig } from './components/MotionConfig/index.mjs';\nexport { LazyMotion } from './components/LazyMotion/index.mjs';\nexport { LayoutGroup } from './components/LayoutGroup/index.mjs';\nexport { Reorder } from './components/Reorder/index.mjs';\nexport { domAnimation } from './render/dom/features-animation.mjs';\nexport { domMax } from './render/dom/features-max.mjs';\nexport { useMotionValue } from './value/use-motion-value.mjs';\nexport { useMotionTemplate } from './value/use-motion-template.mjs';\nexport { resolveMotionValue } from './value/utils/resolve-motion-value.mjs';\nexport { useTransform } from './value/use-transform.mjs';\nexport { useSpring } from './value/use-spring.mjs';\nexport { useVelocity } from './value/use-velocity.mjs';\nexport { useScroll } from './value/use-scroll.mjs';\nexport { useElementScroll } from './value/scroll/use-element-scroll.mjs';\nexport { useViewportScroll } from './value/scroll/use-viewport-scroll.mjs';\nexport { useTime } from './value/use-time.mjs';\nexport { useWillChange } from './value/use-will-change/index.mjs';\nexport { useMotionValueEvent } from './utils/use-motion-value-event.mjs';\nexport { useReducedMotion } from './utils/reduced-motion/use-reduced-motion.mjs';\nexport { useReducedMotionConfig } from './utils/reduced-motion/use-reduced-motion-config.mjs';\nexport { animationControls } from './animation/hooks/animation-controls.mjs';\nexport { useAnimate } from './animation/hooks/use-animate.mjs';\nexport { useAnimation, useAnimationControls } from './animation/hooks/use-animation.mjs';\nexport { useAnimationFrame } from './utils/use-animation-frame.mjs';\nexport { animateVisualElement } from './animation/interfaces/visual-element.mjs';\nexport { useCycle } from './utils/use-cycle.mjs';\nexport { isValidMotionProp } from './motion/utils/valid-prop.mjs';\nexport { useIsPresent, usePresence } from './components/AnimatePresence/use-presence.mjs';\nexport { useInView } from './utils/use-in-view.mjs';\nexport { DragControls, useDragControls } from './gestures/drag/use-drag-controls.mjs';\nexport { useDomEvent } from './events/use-dom-event.mjs';\nexport { createMotionComponent } from './motion/index.mjs';\nexport { isMotionComponent } from './motion/utils/is-motion-component.mjs';\nexport { unwrapMotionComponent } from './motion/utils/unwrap-motion-component.mjs';\nexport { VisualElement } from './render/VisualElement.mjs';\nexport { addScaleCorrector } from './projection/styles/scale-correction.mjs';\nexport { disableInstantTransitions, useInstantTransition } from './utils/use-instant-transition.mjs';\nexport { useInstantLayoutTransition } from './projection/use-instant-layout-transition.mjs';\nexport { useResetProjection } from './projection/use-reset-projection.mjs';\nexport { buildTransform } from './render/html/utils/build-transform.mjs';\nexport { visualElementStore } from './render/store.mjs';\nexport { animateValue } from './animation/animators/js/index.mjs';\nexport { color } from './value/types/color/index.mjs';\nexport { complex } from './value/types/complex/index.mjs';\nexport { px } from './value/types/numbers/units.mjs';\nexport { MotionGlobalConfig } from './utils/GlobalConfig.mjs';\nexport { startOptimizedAppearAnimation } from './animation/optimized-appear/start.mjs';\nexport { optimizedAppearDataAttribute } from './animation/optimized-appear/data-id.mjs';\nexport { spring } from './animation/generators/spring/index.mjs';\nexport { MotionContext } from './context/MotionContext/index.mjs';\nexport { MotionConfigContext } from './context/MotionConfigContext.mjs';\nexport { PresenceContext } from './context/PresenceContext.mjs';\nexport { LayoutGroupContext } from './context/LayoutGroupContext.mjs';\nexport { SwitchLayoutGroupContext } from './context/SwitchLayoutGroupContext.mjs';\nexport { FlatTree } from './render/utils/flat-tree.mjs';\nexport { DeprecatedLayoutGroupContext } from './context/DeprecatedLayoutGroupContext.mjs';\nexport { useAnimatedState as useDeprecatedAnimatedState } from './animation/hooks/use-animated-state.mjs';\nexport { useInvertedScale as useDeprecatedInvertedScale } from './value/use-inverted-scale.mjs';\nexport { AnimateSharedLayout } from './components/AnimateSharedLayout.mjs';\nexport { MotionValue, motionValue } from './value/index.mjs';\nexport { animate, createScopedAnimate } from './animation/animate.mjs';\nexport { scroll } from './render/dom/scroll/index.mjs';\nexport { scrollInfo } from './render/dom/scroll/track.mjs';\nexport { inView } from './render/dom/viewport/index.mjs';\nexport { stagger } from './animation/utils/stagger.mjs';\nexport { transform } from './utils/transform.mjs';\nexport { clamp } from './utils/clamp.mjs';\nexport { mix } from './utils/mix.mjs';\nexport { pipe } from './utils/pipe.mjs';\nexport { progress } from './utils/progress.mjs';\nexport { wrap } from './utils/wrap.mjs';\nexport { cancelSync, sync } from './frameloop/index-legacy.mjs';\nexport { anticipate } from './easing/anticipate.mjs';\nexport { backIn, backInOut, backOut } from './easing/back.mjs';\nexport { circIn, circInOut, circOut } from './easing/circ.mjs';\nexport { easeIn, easeInOut, easeOut } from './easing/ease.mjs';\nexport { cubicBezier } from './easing/cubic-bezier.mjs';\nexport { mirrorEasing } from './easing/modifiers/mirror.mjs';\nexport { reverseEasing } from './easing/modifiers/reverse.mjs';\nexport { delay } from './utils/delay.mjs';\nexport { distance, distance2D } from './utils/distance.mjs';\nexport { invariant, warning } from './utils/errors.mjs';\nexport { interpolate } from './utils/interpolate.mjs';\nexport { cancelFrame, frame, frameData, steps } from './frameloop/frame.mjs';\nexport { animations } from './motion/features/animations.mjs';\nexport { checkTargetForNewValues } from './render/utils/setters.mjs';\nexport { createBox } from './projection/geometry/models.mjs';\nexport { calcLength } from './projection/geometry/delta-calc.mjs';\nexport { filterProps } from './render/dom/utils/filter-props.mjs';\nexport { makeUseVisualState } from './motion/utils/use-visual-state.mjs';\nexport { isDragActive } from './gestures/drag/utils/lock.mjs';\nexport { addPointerEvent } from './events/add-pointer-event.mjs';\nexport { addPointerInfo } from './events/event-info.mjs';\nexport { isMotionValue } from './value/utils/is-motion-value.mjs';\nexport { isBrowser } from './utils/is-browser.mjs';\nexport { useUnmountEffect } from './utils/use-unmount-effect.mjs';\nexport { useIsomorphicLayoutEffect } from './utils/use-isomorphic-effect.mjs';\nexport { useForceUpdate } from './utils/use-force-update.mjs';", "map": {"version": 3, "names": ["createDomMotionComponent", "motion", "m", "AnimatePresence", "MotionConfig", "LazyMotion", "LayoutGroup", "Reorder", "domAnimation", "domMax", "useMotionValue", "useMotionTemplate", "resolveMotionValue", "useTransform", "useSpring", "useVelocity", "useScroll", "useElementScroll", "useViewportScroll", "useTime", "useWillChange", "useMotionValueEvent", "useReducedMotion", "useReducedMotionConfig", "animationControls", "useAnimate", "useAnimation", "useAnimationControls", "useAnimationFrame", "animateVisualElement", "useCycle", "isValidMotionProp", "useIsPresent", "usePresence", "useInView", "DragControls", "useDragControls", "useDomEvent", "createMotionComponent", "isMotionComponent", "unwrapMotionComponent", "VisualElement", "addScaleCorrector", "disableInstantTransitions", "useInstantTransition", "useInstantLayoutTransition", "useResetProjection", "buildTransform", "visualElementStore", "animateValue", "color", "complex", "px", "MotionGlobalConfig", "startOptimizedAppearAnimation", "optimizedAppearDataAttribute", "spring", "MotionContext", "MotionConfigContext", "PresenceContext", "LayoutGroupContext", "SwitchLayoutGroupContext", "FlatTree", "DeprecatedLayoutGroupContext", "useAnimatedState", "useDeprecatedAnimatedState", "useInvertedScale", "useDeprecatedInvertedScale", "AnimateSharedLayout", "MotionValue", "motionValue", "animate", "createScopedAnimate", "scroll", "scrollInfo", "inView", "stagger", "transform", "clamp", "mix", "pipe", "progress", "wrap", "cancelSync", "sync", "anticipate", "backIn", "backInOut", "backOut", "circIn", "circInOut", "circOut", "easeIn", "easeInOut", "easeOut", "cubicBezier", "mirrorEasing", "reverseEasing", "delay", "distance", "distance2D", "invariant", "warning", "interpolate", "cancelFrame", "frame", "frameData", "steps", "animations", "checkTargetForNewValues", "createBox", "calcLength", "filterProps", "makeUseVisualState", "isDragActive", "addPointerEvent", "addPointerInfo", "isMotionValue", "<PERSON><PERSON><PERSON><PERSON>", "useUnmountEffect", "useIsomorphicLayoutEffect", "useForceUpdate"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/index.mjs"], "sourcesContent": ["export { createDomMotionComponent, motion } from './render/dom/motion.mjs';\nexport { m } from './render/dom/motion-minimal.mjs';\nexport { AnimatePresence } from './components/AnimatePresence/index.mjs';\nexport { MotionConfig } from './components/MotionConfig/index.mjs';\nexport { LazyMotion } from './components/LazyMotion/index.mjs';\nexport { LayoutGroup } from './components/LayoutGroup/index.mjs';\nexport { Reorder } from './components/Reorder/index.mjs';\nexport { domAnimation } from './render/dom/features-animation.mjs';\nexport { domMax } from './render/dom/features-max.mjs';\nexport { useMotionValue } from './value/use-motion-value.mjs';\nexport { useMotionTemplate } from './value/use-motion-template.mjs';\nexport { resolveMotionValue } from './value/utils/resolve-motion-value.mjs';\nexport { useTransform } from './value/use-transform.mjs';\nexport { useSpring } from './value/use-spring.mjs';\nexport { useVelocity } from './value/use-velocity.mjs';\nexport { useScroll } from './value/use-scroll.mjs';\nexport { useElementScroll } from './value/scroll/use-element-scroll.mjs';\nexport { useViewportScroll } from './value/scroll/use-viewport-scroll.mjs';\nexport { useTime } from './value/use-time.mjs';\nexport { useWillChange } from './value/use-will-change/index.mjs';\nexport { useMotionValueEvent } from './utils/use-motion-value-event.mjs';\nexport { useReducedMotion } from './utils/reduced-motion/use-reduced-motion.mjs';\nexport { useReducedMotionConfig } from './utils/reduced-motion/use-reduced-motion-config.mjs';\nexport { animationControls } from './animation/hooks/animation-controls.mjs';\nexport { useAnimate } from './animation/hooks/use-animate.mjs';\nexport { useAnimation, useAnimationControls } from './animation/hooks/use-animation.mjs';\nexport { useAnimationFrame } from './utils/use-animation-frame.mjs';\nexport { animateVisualElement } from './animation/interfaces/visual-element.mjs';\nexport { useCycle } from './utils/use-cycle.mjs';\nexport { isValidMotionProp } from './motion/utils/valid-prop.mjs';\nexport { useIsPresent, usePresence } from './components/AnimatePresence/use-presence.mjs';\nexport { useInView } from './utils/use-in-view.mjs';\nexport { DragControls, useDragControls } from './gestures/drag/use-drag-controls.mjs';\nexport { useDomEvent } from './events/use-dom-event.mjs';\nexport { createMotionComponent } from './motion/index.mjs';\nexport { isMotionComponent } from './motion/utils/is-motion-component.mjs';\nexport { unwrapMotionComponent } from './motion/utils/unwrap-motion-component.mjs';\nexport { VisualElement } from './render/VisualElement.mjs';\nexport { addScaleCorrector } from './projection/styles/scale-correction.mjs';\nexport { disableInstantTransitions, useInstantTransition } from './utils/use-instant-transition.mjs';\nexport { useInstantLayoutTransition } from './projection/use-instant-layout-transition.mjs';\nexport { useResetProjection } from './projection/use-reset-projection.mjs';\nexport { buildTransform } from './render/html/utils/build-transform.mjs';\nexport { visualElementStore } from './render/store.mjs';\nexport { animateValue } from './animation/animators/js/index.mjs';\nexport { color } from './value/types/color/index.mjs';\nexport { complex } from './value/types/complex/index.mjs';\nexport { px } from './value/types/numbers/units.mjs';\nexport { MotionGlobalConfig } from './utils/GlobalConfig.mjs';\nexport { startOptimizedAppearAnimation } from './animation/optimized-appear/start.mjs';\nexport { optimizedAppearDataAttribute } from './animation/optimized-appear/data-id.mjs';\nexport { spring } from './animation/generators/spring/index.mjs';\nexport { MotionContext } from './context/MotionContext/index.mjs';\nexport { MotionConfigContext } from './context/MotionConfigContext.mjs';\nexport { PresenceContext } from './context/PresenceContext.mjs';\nexport { LayoutGroupContext } from './context/LayoutGroupContext.mjs';\nexport { SwitchLayoutGroupContext } from './context/SwitchLayoutGroupContext.mjs';\nexport { FlatTree } from './render/utils/flat-tree.mjs';\nexport { DeprecatedLayoutGroupContext } from './context/DeprecatedLayoutGroupContext.mjs';\nexport { useAnimatedState as useDeprecatedAnimatedState } from './animation/hooks/use-animated-state.mjs';\nexport { useInvertedScale as useDeprecatedInvertedScale } from './value/use-inverted-scale.mjs';\nexport { AnimateSharedLayout } from './components/AnimateSharedLayout.mjs';\nexport { MotionValue, motionValue } from './value/index.mjs';\nexport { animate, createScopedAnimate } from './animation/animate.mjs';\nexport { scroll } from './render/dom/scroll/index.mjs';\nexport { scrollInfo } from './render/dom/scroll/track.mjs';\nexport { inView } from './render/dom/viewport/index.mjs';\nexport { stagger } from './animation/utils/stagger.mjs';\nexport { transform } from './utils/transform.mjs';\nexport { clamp } from './utils/clamp.mjs';\nexport { mix } from './utils/mix.mjs';\nexport { pipe } from './utils/pipe.mjs';\nexport { progress } from './utils/progress.mjs';\nexport { wrap } from './utils/wrap.mjs';\nexport { cancelSync, sync } from './frameloop/index-legacy.mjs';\nexport { anticipate } from './easing/anticipate.mjs';\nexport { backIn, backInOut, backOut } from './easing/back.mjs';\nexport { circIn, circInOut, circOut } from './easing/circ.mjs';\nexport { easeIn, easeInOut, easeOut } from './easing/ease.mjs';\nexport { cubicBezier } from './easing/cubic-bezier.mjs';\nexport { mirrorEasing } from './easing/modifiers/mirror.mjs';\nexport { reverseEasing } from './easing/modifiers/reverse.mjs';\nexport { delay } from './utils/delay.mjs';\nexport { distance, distance2D } from './utils/distance.mjs';\nexport { invariant, warning } from './utils/errors.mjs';\nexport { interpolate } from './utils/interpolate.mjs';\nexport { cancelFrame, frame, frameData, steps } from './frameloop/frame.mjs';\nexport { animations } from './motion/features/animations.mjs';\nexport { checkTargetForNewValues } from './render/utils/setters.mjs';\nexport { createBox } from './projection/geometry/models.mjs';\nexport { calcLength } from './projection/geometry/delta-calc.mjs';\nexport { filterProps } from './render/dom/utils/filter-props.mjs';\nexport { makeUseVisualState } from './motion/utils/use-visual-state.mjs';\nexport { isDragActive } from './gestures/drag/utils/lock.mjs';\nexport { addPointerEvent } from './events/add-pointer-event.mjs';\nexport { addPointerInfo } from './events/event-info.mjs';\nexport { isMotionValue } from './value/utils/is-motion-value.mjs';\nexport { isBrowser } from './utils/is-browser.mjs';\nexport { useUnmountEffect } from './utils/use-unmount-effect.mjs';\nexport { useIsomorphicLayoutEffect } from './utils/use-isomorphic-effect.mjs';\nexport { useForceUpdate } from './utils/use-force-update.mjs';\n"], "mappings": "AAAA,SAASA,wBAAwB,EAAEC,MAAM,QAAQ,yBAAyB;AAC1E,SAASC,CAAC,QAAQ,iCAAiC;AACnD,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,qCAAqC;AACxF,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,YAAY,EAAEC,WAAW,QAAQ,+CAA+C;AACzF,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,YAAY,EAAEC,eAAe,QAAQ,uCAAuC;AACrF,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,yBAAyB,EAAEC,oBAAoB,QAAQ,oCAAoC;AACpG,SAASC,0BAA0B,QAAQ,gDAAgD;AAC3F,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,OAAO,QAAQ,iCAAiC;AACzD,SAASC,EAAE,QAAQ,iCAAiC;AACpD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,6BAA6B,QAAQ,wCAAwC;AACtF,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,MAAM,QAAQ,yCAAyC;AAChE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,wBAAwB,QAAQ,wCAAwC;AACjF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,4BAA4B,QAAQ,4CAA4C;AACzF,SAASC,gBAAgB,IAAIC,0BAA0B,QAAQ,0CAA0C;AACzG,SAASC,gBAAgB,IAAIC,0BAA0B,QAAQ,gCAAgC;AAC/F,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AAC5D,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,yBAAyB;AACtE,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,MAAM,QAAQ,iCAAiC;AACxD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,IAAI,QAAQ,8BAA8B;AAC/D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,mBAAmB;AAC9D,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,mBAAmB;AAC9D,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,sBAAsB;AAC3D,SAASC,SAAS,EAAEC,OAAO,QAAQ,oBAAoB;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAQ,uBAAuB;AAC5E,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,cAAc,QAAQ,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}