{"ast": null, "code": "import { number } from '../../../value/types/numbers/index.mjs';\nconst int = {\n  ...number,\n  transform: Math.round\n};\nexport { int };", "map": {"version": 3, "names": ["number", "int", "transform", "Math", "round"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs"], "sourcesContent": ["import { number } from '../../../value/types/numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,wCAAwC;AAE/D,MAAMC,GAAG,GAAG;EACR,GAAGD,MAAM;EACTE,SAAS,EAAEC,IAAI,CAACC;AACpB,CAAC;AAED,SAASH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}