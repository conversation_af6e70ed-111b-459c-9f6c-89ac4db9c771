{"name": "blazetrad-app", "version": "1.0.0", "description": "Bitcoin exchange and trading website with chatbot", "main": "src/server/server.js", "scripts": {"start": "node src/server/server.js", "server": "nodemon src/server/server.js", "client": "cd src/client && npm start", "dev": "concurrently \"npm run server\" \"npm run client\"", "build": "cd src/client && npm install && npm run build", "install-client": "cd src/client && npm install"}, "keywords": ["bitcoin", "cryptocurrency", "exchange", "trading", "chatbot"], "author": "", "license": "ISC", "dependencies": {"@headlessui/react": "^1.7.17", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^10.16.5", "nodemon": "^3.0.1", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.19.0", "tailwindcss": "^3.3.5"}}