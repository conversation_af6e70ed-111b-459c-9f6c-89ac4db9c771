{"ast": null, "code": "import React from'react';import blazeTrade<PERSON>ogo from'../assets/blazetrade-logo.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AuthFooter=()=>{return/*#__PURE__*/_jsx(\"footer\",{className:\"bg-gray-900 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"img\",{src:blazeTradeLogo,alt:\"BlazeTrade Logo\",className:\"h-8 object-contain mb-2\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"\\xA9 \",new Date().getFullYear(),\" BlazeTrade. All rights reserved.\"]})]})});};export default AuthFooter;", "map": {"version": 3, "names": ["React", "blazeTradeLogo", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "children", "src", "alt", "Date", "getFullYear"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/AuthFooter.js"], "sourcesContent": ["import React from 'react';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst AuthFooter = () => {\n  return (\n    <footer className=\"bg-gray-900 py-4\">\n      <div className=\"container-custom flex flex-col items-center justify-center\">\n        <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-8 object-contain mb-2\" />\n        <p className=\"text-sm text-gray-500\">&copy; {new Date().getFullYear()} BlazeTrade. All rights reserved.</p>\n      </div>\n    </footer>\n  );\n};\n\nexport default AuthFooter;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACEH,IAAA,WAAQI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAClCH,KAAA,QAAKE,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACzEL,IAAA,QAAKM,GAAG,CAAER,cAAe,CAACS,GAAG,CAAC,iBAAiB,CAACH,SAAS,CAAC,yBAAyB,CAAE,CAAC,cACtFF,KAAA,MAAGE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,OAAO,CAAC,GAAI,CAAAG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,mCAAiC,EAAG,CAAC,EACxG,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}