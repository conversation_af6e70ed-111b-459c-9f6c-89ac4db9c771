{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import{FaRobot,FaTimes,FaPaperPlane,FaUser}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Chatbot=()=>{const[isOpen,setIsOpen]=useState(false);const token=localStorage.getItem('token');const[messages,setMessages]=useState([]);const[inputValue,setInputValue]=useState('');const[isTyping,setIsTyping]=useState(false);const[awaitingYesNo,setAwaitingYesNo]=useState(false);const messagesEndRef=useRef(null);// Predefined responses for the chatbot\nconst botResponses={greetings:token?[\"Hello! Welcome back to BlazeTrade. How can I assist you today?\",\"Welcome back! I'm here to help with any questions about your account or our services.\"]:[\"Hello! Welcome to BlazeTrade. Login to start trading.\",\"Hi there! Please login to access all our trading features.\"],about:[\"BlazeTrade is a professional Bitcoin exchange and trading platform established in 2018. We provide secure and reliable cryptocurrency services with a focus on user experience and security.\",\"Founded with a mission to make cryptocurrency trading accessible to everyone, BlazeTrade offers a range of services including Bitcoin exchange, trading, and buying of giftcards.\",\"BlazeTrade is a leading cryptocurrency platform serving over 10,000 active users worldwide. Our team consists of blockchain experts and financial professionals dedicated to providing the best trading experience.\"],services:[\"Our services include Bitcoin exchange, cryptocurrency trading, market analysis, portfolio management, security solutions, and consulting services.\",\"At BlazeTrade, we offer comprehensive cryptocurrency services including buying and selling Bitcoin, trading strategies, market insights, and personalized portfolio management.\",\"BlazeTrade provides institutional-grade trading tools with real-time market data, advanced charting, and algorithmic trading options for both beginners and professional traders.\"],security:[\"Security is our top priority. We implement industry-leading security measures including cold storage, two-factor authentication, and regular security audits to protect your assets.\",\"BlazeTrade uses advanced encryption and multi-signature technology to ensure the highest level of security for your cryptocurrency assets.\",\"We keep 95% of user funds in cold storage protected by multi-signature technology. Our platform undergoes regular penetration testing and security audits by third-party experts.\"],fees:[\"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.5% depending on your trading volume. Please visit our services page for detailed information.\",\"BlazeTrade offers competitive fees with discounts for high-volume traders. Our basic trading fee is 0.2% per transaction.\",\"We offer tiered fee discounts based on 30-day trading volume. VIP clients trading over $1M monthly enjoy fees as low as 0.05% and dedicated account managers.\"],contact:[\"You can contact our support <NAME_EMAIL> or call us at +****************. We're available 24/7 to assist you.\",\"For any inquiries, please email <NAME_EMAIL> or use the contact form on our website. Our team is ready to help!\",\"Our headquarters is located in New York with regional offices in London, Singapore, and Tokyo. Technical support is available 24/7 via live chat, email, or phone.\"],advantages:[\"BlazeTrade offers several advantages including institutional-grade security, 24/7 customer support, competitive fees, and a user-friendly interface designed for both beginners and professionals.\",\"What sets BlazeTrade apart is our combination of advanced trading tools, educational resources, and personalized portfolio management services tailored to each client's needs.\"],history:[\"BlazeTrade was founded in 2018 by a team of blockchain enthusiasts and financial experts with a vision to make cryptocurrency trading accessible, secure, and transparent for everyone.\",\"Since our founding, we've grown to serve clients in over 100 countries, processed more than $5 billion in trading volume, and maintained a 99.9% platform uptime.\"],team:[\"Our leadership team includes former executives from major financial institutions and blockchain pioneers with over 50 years of combined experience in fintech and cryptocurrency markets.\",\"BlazeTrade employs over 120 professionals worldwide, including blockchain developers, security experts, financial analysts, and customer support specialists.\"],default:[\"I'm not sure I understand. Could you please rephrase your question?\",\"I don't have information on that specific topic. Would you like to know about our services, security measures, or how to contact us?\",\"For more detailed information, please contact our support <NAME_EMAIL>.\"],goodbye:[\"Goodbye! Have a great day!\"],help:[\"Sure! I can assist you with any questions you have about our services, security measures, or how to contact us.\",\"I'm here to help! Let me know if you have any specific questions or need assistance.\"],how_to_trade_with_blazetrade:[\"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"],thanks:[\"You're welcome! Is there anything else I can help you with?\",\"Happy to help! Let me know if you have any other questions.\",\"No problem! Feel free to ask if anything else comes to mind.\"],yes:[\"Great! How can I help you?\",\"Sure, what do you need help with?\",\"I am here to help. What is your question?\"],no:[\"Alright, have a great day!\",\"No problem, feel free to reach out if you need anything else.\",\"Okay, goodbye!\"],trade:[\"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"]};// Function to scroll to the bottom of the chat\nconst scrollToBottom=()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});};useEffect(()=>{scrollToBottom();},[messages]);// Function to toggle the chatbot\nconst toggleChat=()=>{setIsOpen(!isOpen);if(!isOpen&&messages.length===0){// Add welcome message when opening for the first time\nconst randomGreeting=botResponses.greetings[Math.floor(Math.random()*botResponses.greetings.length)];setTimeout(()=>{setMessages([{text:randomGreeting,sender:'bot'}]);},500);}};// Function to handle sending a message\nconst handleEndChat=()=>{setMessages([]);setInputValue('');const randomGreeting=botResponses.greetings[Math.floor(Math.random()*botResponses.greetings.length)];setTimeout(()=>{setMessages([{text:randomGreeting,sender:'bot'}]);},500);};const handleSendMessage=e=>{e.preventDefault();if(inputValue.trim()==='')return;// Add user message\nconst userMessage={text:inputValue,sender:'user'};setMessages([...messages,userMessage]);setInputValue('');setIsTyping(true);// Simulate bot thinking and respond after a delay\nsetTimeout(()=>{const botMessage={text:getBotResponse(inputValue),sender:'bot'};setMessages(prevMessages=>[...prevMessages,botMessage]);setIsTyping(false);},1000+Math.random()*1000);// Random delay between 1-2 seconds\n};// Function to determine bot response based on user input\nconst getBotResponse=input=>{const lowerInput=input.toLowerCase();// Check for keywords in the input\nif(/(hi|hello|hey|greetings)/i.test(lowerInput)){return getRandomResponse(botResponses.greetings);}else if(/(about|who are you|company|brand)/i.test(lowerInput)){return getRandomResponse(botResponses.about);}else if(/(services|offer|provide|trading|exchange)/i.test(lowerInput)){return getRandomResponse(botResponses.services);}else if(/(secure|security|safe|protection)/i.test(lowerInput)){return getRandomResponse(botResponses.security);}else if(/(fee|cost|price|charge)/i.test(lowerInput)){return getRandomResponse(botResponses.fees);}else if(/(contact|email|phone|reach|support)/i.test(lowerInput)){return getRandomResponse(botResponses.contact);}else if(/(advantage|benefit|better|why choose|why use)/i.test(lowerInput)){return getRandomResponse(botResponses.advantages);}else if(/(history|founded|start|begin|origin)/i.test(lowerInput)){return getRandomResponse(botResponses.history);}else if(/(team|staff|employee|expert|founder)/i.test(lowerInput)){return getRandomResponse(botResponses.team);}else if(/(thanks|thank you|appreciate it)/i.test(lowerInput)){setAwaitingYesNo(true);return getRandomResponse(botResponses.thanks);}else if(awaitingYesNo){if(/(yes|yeah|sure)/i.test(lowerInput)){setAwaitingYesNo(false);return getRandomResponse(botResponses.yes);}else if(/(no|nope|nah)/i.test(lowerInput)){setAwaitingYesNo(false);return getRandomResponse(botResponses.no);}else{setAwaitingYesNo(false);return getRandomResponse(botResponses.default);}}else if(/(how to trade|trade with you|start trading)/i.test(lowerInput)){return getRandomResponse(botResponses.trade);}else{return getRandomResponse(botResponses.default);}};// Function to get a random response from an array\nconst getRandomResponse=responseArray=>{return responseArray[Math.floor(Math.random()*responseArray.length)];};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(motion.button,{className:\"fixed bottom-6 right-6 w-14 h-14 rounded-full bg-primary-dark text-white flex items-center justify-center shadow-lg hover:bg-primary-light transition-colors duration-300 z-50\",onClick:toggleChat,whileHover:{scale:1.1},whileTap:{scale:0.9},children:isOpen?/*#__PURE__*/_jsx(FaTimes,{size:20}):/*#__PURE__*/_jsx(FaRobot,{size:24})}),/*#__PURE__*/_jsx(AnimatePresence,{children:isOpen&&/*#__PURE__*/_jsxs(motion.div,{className:\"fixed bottom-24 right-6 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-xl overflow-hidden z-50 flex flex-col\",initial:{opacity:0,y:20,scale:0.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:0.9},transition:{duration:0.3},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-primary-dark text-white p-4 flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(FaRobot,{}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"BlazeTrade Assistant\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleEndChat,className:\"text-white hover:text-gray-300 transition-colors mr-2\",children:\"End Chat\"}),/*#__PURE__*/_jsx(\"button\",{onClick:toggleChat,className:\"text-white hover:text-gray-300 transition-colors\",children:/*#__PURE__*/_jsx(FaTimes,{})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 p-4 overflow-y-auto bg-gray-50\",children:[messages.map((message,index)=>/*#__PURE__*/_jsx(motion.div,{className:`flex ${message.sender==='user'?'justify-end':'justify-start'} mb-3`,initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:0.3},children:/*#__PURE__*/_jsx(\"div\",{className:`max-w-[80%] p-3 rounded-lg ${message.sender==='user'?'bg-primary-dark text-white rounded-br-none':'bg-gray-200 text-gray-800 rounded-bl-none'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-2\",children:[message.sender==='bot'&&/*#__PURE__*/_jsx(FaRobot,{className:\"mt-1 text-primary-dark\"}),/*#__PURE__*/_jsx(\"div\",{children:message.text}),message.sender==='user'&&/*#__PURE__*/_jsx(FaUser,{className:\"mt-1 text-white\"})]})})},index)),isTyping&&/*#__PURE__*/_jsx(motion.div,{className:\"flex justify-start mb-3\",initial:{opacity:0},animate:{opacity:1},children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none max-w-[80%]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",style:{animationDelay:'0ms'}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",style:{animationDelay:'150ms'}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\",style:{animationDelay:'300ms'}})]})})}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSendMessage,className:\"p-3 bg-gray-100 border-t border-gray-200 flex\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:inputValue,onChange:e=>setInputValue(e.target.value),placeholder:\"Type your message...\",className:\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-dark text-gray-800\",autoComplete:\"off\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"bg-primary-dark text-white px-4 py-2 rounded-r-md hover:bg-primary-light transition-colors flex items-center justify-center\",disabled:inputValue.trim()==='',children:/*#__PURE__*/_jsx(FaPaperPlane,{})})]})]})})]});};export default Chatbot;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "FaRobot", "FaTimes", "FaPaperPlane", "FaUser", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "isOpen", "setIsOpen", "token", "localStorage", "getItem", "messages", "setMessages", "inputValue", "setInputValue", "isTyping", "setIsTyping", "awaitingYesNo", "setAwaitingYesNo", "messagesEndRef", "botResponses", "greetings", "about", "services", "security", "fees", "contact", "advantages", "history", "team", "default", "goodbye", "help", "how_to_trade_with_<PERSON><PERSON>de", "thanks", "yes", "no", "trade", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "toggleChat", "length", "randomGreeting", "Math", "floor", "random", "setTimeout", "text", "sender", "handleEndChat", "handleSendMessage", "e", "preventDefault", "trim", "userMessage", "botMessage", "getBotResponse", "prevMessages", "input", "lowerInput", "toLowerCase", "test", "getRandomResponse", "responseArray", "children", "button", "className", "onClick", "whileHover", "scale", "whileTap", "size", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "map", "message", "index", "style", "animationDelay", "ref", "onSubmit", "type", "value", "onChange", "target", "placeholder", "autoComplete", "disabled"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Chatbot.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaRobot, FaTimes, FaPaperPlane, FaUser } from 'react-icons/fa';\n\nconst Chatbot = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const token = localStorage.getItem('token');\n  const [messages, setMessages] = useState([]);\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [awaitingYesNo, setAwaitingYesNo] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Predefined responses for the chatbot\n  const botResponses = {\n    greetings: token \n      ? [\n          \"Hello! Welcome back to BlazeTrade. How can I assist you today?\",\n          \"Welcome back! I'm here to help with any questions about your account or our services.\",\n        ]\n      : [\n          \"Hello! Welcome to BlazeTrade. Login to start trading.\",\n          \"Hi there! Please login to access all our trading features.\",\n        ],\n    about: [\n      \"BlazeTrade is a professional Bitcoin exchange and trading platform established in 2018. We provide secure and reliable cryptocurrency services with a focus on user experience and security.\",\n      \"Founded with a mission to make cryptocurrency trading accessible to everyone, BlazeTrade offers a range of services including Bitcoin exchange, trading, and buying of giftcards.\",\n      \"BlazeTrade is a leading cryptocurrency platform serving over 10,000 active users worldwide. Our team consists of blockchain experts and financial professionals dedicated to providing the best trading experience.\"\n    ],\n    services: [\n      \"Our services include Bitcoin exchange, cryptocurrency trading, market analysis, portfolio management, security solutions, and consulting services.\",\n      \"At BlazeTrade, we offer comprehensive cryptocurrency services including buying and selling Bitcoin, trading strategies, market insights, and personalized portfolio management.\",\n      \"BlazeTrade provides institutional-grade trading tools with real-time market data, advanced charting, and algorithmic trading options for both beginners and professional traders.\"\n    ],\n    security: [\n      \"Security is our top priority. We implement industry-leading security measures including cold storage, two-factor authentication, and regular security audits to protect your assets.\",\n      \"BlazeTrade uses advanced encryption and multi-signature technology to ensure the highest level of security for your cryptocurrency assets.\",\n      \"We keep 95% of user funds in cold storage protected by multi-signature technology. Our platform undergoes regular penetration testing and security audits by third-party experts.\"\n    ],\n    fees: [\n      \"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.5% depending on your trading volume. Please visit our services page for detailed information.\",\n      \"BlazeTrade offers competitive fees with discounts for high-volume traders. Our basic trading fee is 0.2% per transaction.\",\n      \"We offer tiered fee discounts based on 30-day trading volume. VIP clients trading over $1M monthly enjoy fees as low as 0.05% and dedicated account managers.\"\n    ],\n    contact: [\n      \"You can contact our support <NAME_EMAIL> or call us at +****************. We're available 24/7 to assist you.\",\n      \"For any inquiries, please email <NAME_EMAIL> or use the contact form on our website. Our team is ready to help!\",\n      \"Our headquarters is located in New York with regional offices in London, Singapore, and Tokyo. Technical support is available 24/7 via live chat, email, or phone.\"\n    ],\n    advantages: [\n      \"BlazeTrade offers several advantages including institutional-grade security, 24/7 customer support, competitive fees, and a user-friendly interface designed for both beginners and professionals.\",\n      \"What sets BlazeTrade apart is our combination of advanced trading tools, educational resources, and personalized portfolio management services tailored to each client's needs.\"\n    ],\n    history: [\n      \"BlazeTrade was founded in 2018 by a team of blockchain enthusiasts and financial experts with a vision to make cryptocurrency trading accessible, secure, and transparent for everyone.\",\n      \"Since our founding, we've grown to serve clients in over 100 countries, processed more than $5 billion in trading volume, and maintained a 99.9% platform uptime.\"\n    ],\n    team: [\n      \"Our leadership team includes former executives from major financial institutions and blockchain pioneers with over 50 years of combined experience in fintech and cryptocurrency markets.\",\n      \"BlazeTrade employs over 120 professionals worldwide, including blockchain developers, security experts, financial analysts, and customer support specialists.\"\n    ],\n    default: [\n      \"I'm not sure I understand. Could you please rephrase your question?\",\n      \"I don't have information on that specific topic. Would you like to know about our services, security measures, or how to contact us?\",\n      \"For more detailed information, please contact our support <NAME_EMAIL>.\"\n    ],\n    goodbye: [\n      \"Goodbye! Have a great day!\",\n    ],\n    help: [\n      \"Sure! I can assist you with any questions you have about our services, security measures, or how to contact us.\",\n      \"I'm here to help! Let me know if you have any specific questions or need assistance.\"\n    ],\n    how_to_trade_with_blazetrade: [\n      \"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\",\n    ],\n    thanks: [\n      \"You're welcome! Is there anything else I can help you with?\",\n      \"Happy to help! Let me know if you have any other questions.\",\n      \"No problem! Feel free to ask if anything else comes to mind.\"\n    ],\n    yes: [\n      \"Great! How can I help you?\",\n      \"Sure, what do you need help with?\",\n      \"I am here to help. What is your question?\",\n    ],\n    no: [\n      \"Alright, have a great day!\",\n      \"No problem, feel free to reach out if you need anything else.\",\n      \"Okay, goodbye!\",\n    ],\n    trade: [\n      \"To trade with BlazeTrade, you'll need to send a whatsapp message to blazetrade @+2348163309355. Once you send us a message, you can access our trading platform and start trading your favorite cryptocurrencies.\"\n    ]\n  };\n\n  // Function to scroll to the bottom of the chat\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Function to toggle the chatbot\n  const toggleChat = () => {\n    setIsOpen(!isOpen);\n    if (!isOpen && messages.length === 0) {\n      // Add welcome message when opening for the first time\n      const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n      setTimeout(() => {\n        setMessages([{ text: randomGreeting, sender: 'bot' }]);\n      }, 500);\n    }\n  };\n\n  // Function to handle sending a message\n  const handleEndChat = () => {\n    setMessages([]);\n    setInputValue('');\n    const randomGreeting = botResponses.greetings[Math.floor(Math.random() * botResponses.greetings.length)];\n    setTimeout(() => {\n      setMessages([{ text: randomGreeting, sender: 'bot' }]);\n    }, 500);\n  };\n\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    if (inputValue.trim() === '') return;\n\n    // Add user message\n    const userMessage = { text: inputValue, sender: 'user' };\n    setMessages([...messages, userMessage]);\n    setInputValue('');\n    setIsTyping(true);\n\n    // Simulate bot thinking and respond after a delay\n    setTimeout(() => {\n      const botMessage = { text: getBotResponse(inputValue), sender: 'bot' };\n      setMessages(prevMessages => [...prevMessages, botMessage]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds\n  };\n\n  // Function to determine bot response based on user input\n  const getBotResponse = (input) => {\n    const lowerInput = input.toLowerCase();\n    \n    // Check for keywords in the input\n    if (/(hi|hello|hey|greetings)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.greetings);\n    } else if (/(about|who are you|company|brand)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.about);\n    } else if (/(services|offer|provide|trading|exchange)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.services);\n    } else if (/(secure|security|safe|protection)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.security);\n    } else if (/(fee|cost|price|charge)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.fees);\n    } else if (/(contact|email|phone|reach|support)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.contact);\n    } else if (/(advantage|benefit|better|why choose|why use)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.advantages);\n    } else if (/(history|founded|start|begin|origin)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.history);\n    } else if (/(team|staff|employee|expert|founder)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.team);\n    } else if (/(thanks|thank you|appreciate it)/i.test(lowerInput)) {\n      setAwaitingYesNo(true);\n      return getRandomResponse(botResponses.thanks);\n    } else if (awaitingYesNo) {\n      if (/(yes|yeah|sure)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.yes);\n      } else if (/(no|nope|nah)/i.test(lowerInput)) {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.no);\n      } else {\n        setAwaitingYesNo(false);\n        return getRandomResponse(botResponses.default);\n      }\n    } else if (/(how to trade|trade with you|start trading)/i.test(lowerInput)) {\n      return getRandomResponse(botResponses.trade);\n    } else {\n      return getRandomResponse(botResponses.default);\n    }\n  };\n\n  // Function to get a random response from an array\n  const getRandomResponse = (responseArray) => {\n    return responseArray[Math.floor(Math.random() * responseArray.length)];\n  };\n\n  return (\n    <>\n      {/* Chat toggle button */}\n      <motion.button\n        className=\"fixed bottom-6 right-6 w-14 h-14 rounded-full bg-primary-dark text-white flex items-center justify-center shadow-lg hover:bg-primary-light transition-colors duration-300 z-50\"\n        onClick={toggleChat}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {isOpen ? <FaTimes size={20} /> : <FaRobot size={24} />}\n      </motion.button>\n\n      {/* Chat window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"fixed bottom-24 right-6 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-xl overflow-hidden z-50 flex flex-col\"\n            initial={{ opacity: 0, y: 20, scale: 0.9 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: 20, scale: 0.9 }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Chat header */}\n            <div className=\"bg-primary-dark text-white p-4 flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <FaRobot />\n                <span className=\"font-medium\">BlazeTrade Assistant</span>\n              </div>\n              <div>\n                <button \n                  onClick={handleEndChat}\n                  className=\"text-white hover:text-gray-300 transition-colors mr-2\"\n                >\n                  End Chat\n                </button>\n                <button \n                  onClick={toggleChat}\n                  className=\"text-white hover:text-gray-300 transition-colors\"\n                >\n                  <FaTimes />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat messages */}\n            <div className=\"flex-1 p-4 overflow-y-auto bg-gray-50\">\n              {messages.map((message, index) => (\n                <motion.div\n                  key={index}\n                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <div \n                    className={`max-w-[80%] p-3 rounded-lg ${message.sender === 'user' \n                      ? 'bg-primary-dark text-white rounded-br-none' \n                      : 'bg-gray-200 text-gray-800 rounded-bl-none'}`}\n                  >\n                    <div className=\"flex items-start space-x-2\">\n                      {message.sender === 'bot' && (\n                        <FaRobot className=\"mt-1 text-primary-dark\" />\n                      )}\n                      <div>{message.text}</div>\n                      {message.sender === 'user' && (\n                        <FaUser className=\"mt-1 text-white\" />\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n              {isTyping && (\n                <motion.div \n                  className=\"flex justify-start mb-3\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                >\n                  <div className=\"bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none max-w-[80%]\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '0ms' }}></div>\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '150ms' }}></div>\n                      <div className=\"w-2 h-2 rounded-full bg-gray-500 animate-bounce\" style={{ animationDelay: '300ms' }}></div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Chat input */}\n            <form onSubmit={handleSendMessage} className=\"p-3 bg-gray-100 border-t border-gray-200 flex\">\n              <input\n                type=\"text\"\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder=\"Type your message...\"\n                className=\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-dark text-gray-800\"\n                autoComplete=\"off\"\n              />\n              <button \n                type=\"submit\"\n                className=\"bg-primary-dark text-white px-4 py-2 rounded-r-md hover:bg-primary-light transition-colors flex items-center justify-center\"\n                disabled={inputValue.trim() === ''}\n              >\n                <FaPaperPlane />\n              </button>\n            </form>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Chatbot;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,OAAO,CAAEC,OAAO,CAAEC,YAAY,CAAEC,MAAM,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExE,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAAkB,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC2B,aAAa,CAAEC,gBAAgB,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAA6B,cAAc,CAAG5B,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAA6B,YAAY,CAAG,CACnBC,SAAS,CAAEb,KAAK,CACZ,CACE,gEAAgE,CAChE,uFAAuF,CACxF,CACD,CACE,uDAAuD,CACvD,4DAA4D,CAC7D,CACLc,KAAK,CAAE,CACL,8LAA8L,CAC9L,mLAAmL,CACnL,qNAAqN,CACtN,CACDC,QAAQ,CAAE,CACR,oJAAoJ,CACpJ,iLAAiL,CACjL,mLAAmL,CACpL,CACDC,QAAQ,CAAE,CACR,sLAAsL,CACtL,4IAA4I,CAC5I,mLAAmL,CACpL,CACDC,IAAI,CAAE,CACJ,mLAAmL,CACnL,2HAA2H,CAC3H,+JAA+J,CAChK,CACDC,OAAO,CAAE,CACP,uIAAuI,CACvI,uIAAuI,CACvI,oKAAoK,CACrK,CACDC,UAAU,CAAE,CACV,oMAAoM,CACpM,iLAAiL,CAClL,CACDC,OAAO,CAAE,CACP,yLAAyL,CACzL,mKAAmK,CACpK,CACDC,IAAI,CAAE,CACJ,2LAA2L,CAC3L,+JAA+J,CAChK,CACDC,OAAO,CAAE,CACP,qEAAqE,CACrE,sIAAsI,CACtI,2FAA2F,CAC5F,CACDC,OAAO,CAAE,CACP,4BAA4B,CAC7B,CACDC,IAAI,CAAE,CACJ,iHAAiH,CACjH,sFAAsF,CACvF,CACDC,4BAA4B,CAAE,CAC5B,mNAAmN,CACpN,CACDC,MAAM,CAAE,CACN,6DAA6D,CAC7D,6DAA6D,CAC7D,8DAA8D,CAC/D,CACDC,GAAG,CAAE,CACH,4BAA4B,CAC5B,mCAAmC,CACnC,2CAA2C,CAC5C,CACDC,EAAE,CAAE,CACF,4BAA4B,CAC5B,+DAA+D,CAC/D,gBAAgB,CACjB,CACDC,KAAK,CAAE,CACL,mNAAmN,CAEvN,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAC3B,CAAAA,qBAAA,CAAApB,cAAc,CAACqB,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAEDlD,SAAS,CAAC,IAAM,CACd8C,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAAC3B,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAgC,UAAU,CAAGA,CAAA,GAAM,CACvBpC,SAAS,CAAC,CAACD,MAAM,CAAC,CAClB,GAAI,CAACA,MAAM,EAAIK,QAAQ,CAACiC,MAAM,GAAK,CAAC,CAAE,CACpC;AACA,KAAM,CAAAC,cAAc,CAAGzB,YAAY,CAACC,SAAS,CAACyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG5B,YAAY,CAACC,SAAS,CAACuB,MAAM,CAAC,CAAC,CACxGK,UAAU,CAAC,IAAM,CACfrC,WAAW,CAAC,CAAC,CAAEsC,IAAI,CAAEL,cAAc,CAAEM,MAAM,CAAE,KAAM,CAAC,CAAC,CAAC,CACxD,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1BxC,WAAW,CAAC,EAAE,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACjB,KAAM,CAAA+B,cAAc,CAAGzB,YAAY,CAACC,SAAS,CAACyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG5B,YAAY,CAACC,SAAS,CAACuB,MAAM,CAAC,CAAC,CACxGK,UAAU,CAAC,IAAM,CACfrC,WAAW,CAAC,CAAC,CAAEsC,IAAI,CAAEL,cAAc,CAAEM,MAAM,CAAE,KAAM,CAAC,CAAC,CAAC,CACxD,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,CAAC,EAAK,CAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI1C,UAAU,CAAC2C,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,OAE9B;AACA,KAAM,CAAAC,WAAW,CAAG,CAAEP,IAAI,CAAErC,UAAU,CAAEsC,MAAM,CAAE,MAAO,CAAC,CACxDvC,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAE8C,WAAW,CAAC,CAAC,CACvC3C,aAAa,CAAC,EAAE,CAAC,CACjBE,WAAW,CAAC,IAAI,CAAC,CAEjB;AACAiC,UAAU,CAAC,IAAM,CACf,KAAM,CAAAS,UAAU,CAAG,CAAER,IAAI,CAAES,cAAc,CAAC9C,UAAU,CAAC,CAAEsC,MAAM,CAAE,KAAM,CAAC,CACtEvC,WAAW,CAACgD,YAAY,EAAI,CAAC,GAAGA,YAAY,CAAEF,UAAU,CAAC,CAAC,CAC1D1C,WAAW,CAAC,KAAK,CAAC,CACpB,CAAC,CAAE,IAAI,CAAG8B,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,IAAI,CAAC,CAAE;AACnC,CAAC,CAED;AACA,KAAM,CAAAW,cAAc,CAAIE,KAAK,EAAK,CAChC,KAAM,CAAAC,UAAU,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CAEtC;AACA,GAAI,2BAA2B,CAACC,IAAI,CAACF,UAAU,CAAC,CAAE,CAChD,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACC,SAAS,CAAC,CAClD,CAAC,IAAM,IAAI,oCAAoC,CAAC2C,IAAI,CAACF,UAAU,CAAC,CAAE,CAChE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACE,KAAK,CAAC,CAC9C,CAAC,IAAM,IAAI,4CAA4C,CAAC0C,IAAI,CAACF,UAAU,CAAC,CAAE,CACxE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACG,QAAQ,CAAC,CACjD,CAAC,IAAM,IAAI,oCAAoC,CAACyC,IAAI,CAACF,UAAU,CAAC,CAAE,CAChE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACI,QAAQ,CAAC,CACjD,CAAC,IAAM,IAAI,0BAA0B,CAACwC,IAAI,CAACF,UAAU,CAAC,CAAE,CACtD,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACK,IAAI,CAAC,CAC7C,CAAC,IAAM,IAAI,sCAAsC,CAACuC,IAAI,CAACF,UAAU,CAAC,CAAE,CAClE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACM,OAAO,CAAC,CAChD,CAAC,IAAM,IAAI,gDAAgD,CAACsC,IAAI,CAACF,UAAU,CAAC,CAAE,CAC5E,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACO,UAAU,CAAC,CACnD,CAAC,IAAM,IAAI,uCAAuC,CAACqC,IAAI,CAACF,UAAU,CAAC,CAAE,CACnE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACQ,OAAO,CAAC,CAChD,CAAC,IAAM,IAAI,uCAAuC,CAACoC,IAAI,CAACF,UAAU,CAAC,CAAE,CACnE,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACS,IAAI,CAAC,CAC7C,CAAC,IAAM,IAAI,mCAAmC,CAACmC,IAAI,CAACF,UAAU,CAAC,CAAE,CAC/D5C,gBAAgB,CAAC,IAAI,CAAC,CACtB,MAAO,CAAA+C,iBAAiB,CAAC7C,YAAY,CAACc,MAAM,CAAC,CAC/C,CAAC,IAAM,IAAIjB,aAAa,CAAE,CACxB,GAAI,kBAAkB,CAAC+C,IAAI,CAACF,UAAU,CAAC,CAAE,CACvC5C,gBAAgB,CAAC,KAAK,CAAC,CACvB,MAAO,CAAA+C,iBAAiB,CAAC7C,YAAY,CAACe,GAAG,CAAC,CAC5C,CAAC,IAAM,IAAI,gBAAgB,CAAC6B,IAAI,CAACF,UAAU,CAAC,CAAE,CAC5C5C,gBAAgB,CAAC,KAAK,CAAC,CACvB,MAAO,CAAA+C,iBAAiB,CAAC7C,YAAY,CAACgB,EAAE,CAAC,CAC3C,CAAC,IAAM,CACLlB,gBAAgB,CAAC,KAAK,CAAC,CACvB,MAAO,CAAA+C,iBAAiB,CAAC7C,YAAY,CAACU,OAAO,CAAC,CAChD,CACF,CAAC,IAAM,IAAI,8CAA8C,CAACkC,IAAI,CAACF,UAAU,CAAC,CAAE,CAC1E,MAAO,CAAAG,iBAAiB,CAAC7C,YAAY,CAACiB,KAAK,CAAC,CAC9C,CAAC,IAAM,CACL,MAAO,CAAA4B,iBAAiB,CAAC7C,YAAY,CAACU,OAAO,CAAC,CAChD,CACF,CAAC,CAED;AACA,KAAM,CAAAmC,iBAAiB,CAAIC,aAAa,EAAK,CAC3C,MAAO,CAAAA,aAAa,CAACpB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGkB,aAAa,CAACtB,MAAM,CAAC,CAAC,CACxE,CAAC,CAED,mBACE1C,KAAA,CAAAE,SAAA,EAAA+D,QAAA,eAEEnE,IAAA,CAACP,MAAM,CAAC2E,MAAM,EACZC,SAAS,CAAC,gLAAgL,CAC1LC,OAAO,CAAE3B,UAAW,CACpB4B,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CAAAL,QAAA,CAExB7D,MAAM,cAAGN,IAAA,CAACJ,OAAO,EAAC8E,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG1E,IAAA,CAACL,OAAO,EAAC+E,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1C,CAAC,cAGhB1E,IAAA,CAACN,eAAe,EAAAyE,QAAA,CACb7D,MAAM,eACLJ,KAAA,CAACT,MAAM,CAACkF,GAAG,EACTN,SAAS,CAAC,4GAA4G,CACtHO,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CAC3CO,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEN,KAAK,CAAE,CAAE,CAAE,CACxCQ,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACxCS,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAf,QAAA,eAG9BjE,KAAA,QAAKmE,SAAS,CAAC,kEAAkE,CAAAF,QAAA,eAC/EjE,KAAA,QAAKmE,SAAS,CAAC,6BAA6B,CAAAF,QAAA,eAC1CnE,IAAA,CAACL,OAAO,GAAE,CAAC,cACXK,IAAA,SAAMqE,SAAS,CAAC,aAAa,CAAAF,QAAA,CAAC,sBAAoB,CAAM,CAAC,EACtD,CAAC,cACNjE,KAAA,QAAAiE,QAAA,eACEnE,IAAA,WACEsE,OAAO,CAAElB,aAAc,CACvBiB,SAAS,CAAC,uDAAuD,CAAAF,QAAA,CAClE,UAED,CAAQ,CAAC,cACTnE,IAAA,WACEsE,OAAO,CAAE3B,UAAW,CACpB0B,SAAS,CAAC,kDAAkD,CAAAF,QAAA,cAE5DnE,IAAA,CAACJ,OAAO,GAAE,CAAC,CACL,CAAC,EACN,CAAC,EACH,CAAC,cAGNM,KAAA,QAAKmE,SAAS,CAAC,uCAAuC,CAAAF,QAAA,EACnDxD,QAAQ,CAACwE,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3BrF,IAAA,CAACP,MAAM,CAACkF,GAAG,EAETN,SAAS,CAAE,QAAQe,OAAO,CAACjC,MAAM,GAAK,MAAM,CAAG,aAAa,CAAG,eAAe,OAAQ,CACtFyB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAf,QAAA,cAE9BnE,IAAA,QACEqE,SAAS,CAAE,8BAA8Be,OAAO,CAACjC,MAAM,GAAK,MAAM,CAC9D,4CAA4C,CAC5C,2CAA2C,EAAG,CAAAgB,QAAA,cAElDjE,KAAA,QAAKmE,SAAS,CAAC,4BAA4B,CAAAF,QAAA,EACxCiB,OAAO,CAACjC,MAAM,GAAK,KAAK,eACvBnD,IAAA,CAACL,OAAO,EAAC0E,SAAS,CAAC,wBAAwB,CAAE,CAC9C,cACDrE,IAAA,QAAAmE,QAAA,CAAMiB,OAAO,CAAClC,IAAI,CAAM,CAAC,CACxBkC,OAAO,CAACjC,MAAM,GAAK,MAAM,eACxBnD,IAAA,CAACF,MAAM,EAACuE,SAAS,CAAC,iBAAiB,CAAE,CACtC,EACE,CAAC,CACH,CAAC,EApBDgB,KAqBK,CACb,CAAC,CACDtE,QAAQ,eACPf,IAAA,CAACP,MAAM,CAACkF,GAAG,EACTN,SAAS,CAAC,yBAAyB,CACnCO,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CAAAV,QAAA,cAExBnE,IAAA,QAAKqE,SAAS,CAAC,sEAAsE,CAAAF,QAAA,cACnFjE,KAAA,QAAKmE,SAAS,CAAC,gBAAgB,CAAAF,QAAA,eAC7BnE,IAAA,QAAKqE,SAAS,CAAC,iDAAiD,CAACiB,KAAK,CAAE,CAAEC,cAAc,CAAE,KAAM,CAAE,CAAM,CAAC,cACzGvF,IAAA,QAAKqE,SAAS,CAAC,iDAAiD,CAACiB,KAAK,CAAE,CAAEC,cAAc,CAAE,OAAQ,CAAE,CAAM,CAAC,cAC3GvF,IAAA,QAAKqE,SAAS,CAAC,iDAAiD,CAACiB,KAAK,CAAE,CAAEC,cAAc,CAAE,OAAQ,CAAE,CAAM,CAAC,EACxG,CAAC,CACH,CAAC,CACI,CACb,cACDvF,IAAA,QAAKwF,GAAG,CAAErE,cAAe,CAAE,CAAC,EACzB,CAAC,cAGNjB,KAAA,SAAMuF,QAAQ,CAAEpC,iBAAkB,CAACgB,SAAS,CAAC,+CAA+C,CAAAF,QAAA,eAC1FnE,IAAA,UACE0F,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE9E,UAAW,CAClB+E,QAAQ,CAAGtC,CAAC,EAAKxC,aAAa,CAACwC,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAC/CG,WAAW,CAAC,sBAAsB,CAClCzB,SAAS,CAAC,sHAAsH,CAChI0B,YAAY,CAAC,KAAK,CACnB,CAAC,cACF/F,IAAA,WACE0F,IAAI,CAAC,QAAQ,CACbrB,SAAS,CAAC,6HAA6H,CACvI2B,QAAQ,CAAEnF,UAAU,CAAC2C,IAAI,CAAC,CAAC,GAAK,EAAG,CAAAW,QAAA,cAEnCnE,IAAA,CAACH,YAAY,GAAE,CAAC,CACV,CAAC,EACL,CAAC,EACG,CACb,CACc,CAAC,EAClB,CAAC,CAEP,CAAC,CAED,cAAe,CAAAQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}