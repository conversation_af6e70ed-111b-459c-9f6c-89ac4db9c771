{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion,useAnimation}from'framer-motion';import{useInView}from'react-intersection-observer';import{FaEnvelope,FaPhone,FaMapMarkerAlt,FaPaperPlane,FaInstagram}from'react-icons/fa';import{Link,NavLink}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Contact=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"pt-16\",children:[/*#__PURE__*/_jsx(ContactHero,{}),/*#__PURE__*/_jsx(ContactSection,{}),/*#__PURE__*/_jsx(MapSection,{}),/*#__PURE__*/_jsx(ContactCTA,{})]});};// Contact Hero Section\nconst ContactHero=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"relative bg-primary-dark text-white py-20\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"container-custom relative z-10\",children:/*#__PURE__*/_jsxs(motion.div,{className:\"max-w-3xl\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5},children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-bold mb-6\",children:\"Contact Us\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-300 mb-8\",children:\"Have questions about our Bitcoin exchange and trading services? Our team is here to help. Reach out to us using the contact information below.\"})]})})]});};// Contact Form and Info Section\nconst ContactSection=()=>{const[formData,setFormData]=useState({name:'',email:'',subject:'',message:''});const[formStatus,setFormStatus]=useState({submitted:false,success:false,message:''});const handleChange=e=>{const{name,value}=e.target;setFormData(prevState=>({...prevState,[name]:value}));};const handleSubmit=e=>{e.preventDefault();const{name,email,subject,message}=formData;const whatsappMessage=`Name: ${name}\\nEmail: ${email}\\nSubject: ${subject}\\nMessage: ${message}`;const whatsappUrl=`https://wa.me/2348163309355?text=${encodeURIComponent(whatsappMessage)}`;window.open(whatsappUrl,'_blank');// Reset form after submission\nsetFormData({name:'',email:'',subject:'',message:''});// Optional: Show a success message\nsetFormStatus({submitted:true,success:true,message:'You will be redirected to WhatsApp to send your message.'});// Reset form status after 5 seconds\nsetTimeout(()=>{setFormStatus({submitted:false,success:false,message:''});},5000);};const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"grid grid-cols-1 lg:grid-cols-2 gap-12\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.3}}},children:[/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold mb-6 text-primary-dark\",children:\"Send Us a Message\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-8\",children:\"Fill out the form below and our team will get back to you as soon as possible.\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Your Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:formData.name,onChange:handleChange,className:\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleChange,className:\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"subject\",className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Subject\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"subject\",name:\"subject\",value:formData.subject,onChange:handleChange,className:\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"message\",className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Message\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"message\",name:\"message\",value:formData.message,onChange:handleChange,rows:\"5\",className:\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\",required:true})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"btn-primary flex items-center justify-center w-full\",children:[\"Send Message \",/*#__PURE__*/_jsx(FaPaperPlane,{className:\"ml-2\"})]})}),formStatus.submitted&&/*#__PURE__*/_jsx(motion.div,{className:`p-4 rounded-md ${formStatus.success?'bg-green-100 text-green-700':'bg-red-100 text-red-700'}`,initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:0.3},children:formStatus.message})]})]}),/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,x:30},visible:{opacity:1,x:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold mb-6 text-primary-dark\",children:\"Contact Information\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-8\",children:\"You can reach out to us through the following channels. Our support team is available 24/7 to assist you.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(ContactInfoItem,{icon:/*#__PURE__*/_jsx(FaEnvelope,{className:\"text-primary-dark\"}),title:\"Email Us\",content:\"<EMAIL>\",link:\"mailto:<EMAIL>\"}),/*#__PURE__*/_jsx(ContactInfoItem,{icon:/*#__PURE__*/_jsx(FaPhone,{className:\"text-accent\"}),title:\"Phone\",content:\"+234 ************\",link:\"tel:+2348163309355\"}),/*#__PURE__*/_jsx(ContactInfoItem,{icon:/*#__PURE__*/_jsx(FaMapMarkerAlt,{className:\"text-accent\"}),title:\"Address\",content:\"Victoria island, Lagos, Nigeria, 101241\",link:\"https://maps.google.com/?q=Victoria+island,+Lagos,+Nigeria\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4 text-primary-dark\",children:\"Follow Us\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex space-x-4\",children:/*#__PURE__*/_jsx(SocialLink,{icon:/*#__PURE__*/_jsx(FaInstagram,{}),href:\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8 p-6 bg-gray-50 rounded-lg border border-gray-100\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2 text-primary-dark\",children:\"Business Hours\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:\"Our support team is available 24/7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Monday - Friday:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"9:00 AM - 6:00 PM\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Saturday:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"10:00 AM - 4:00 PM\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Sunday:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Closed\"})]})]})]})]})]})})});};// Map Section\nconst MapSection=()=>{return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50 py-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-96 bg-gray-300 w-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-full flex items-center justify-center bg-primary-dark bg-opacity-10\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(FaMapMarkerAlt,{className:\"text-5xl text-primary-dark mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-primary-dark\",children:\"Bblazetrade Headquarters\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Victoria island, Lagos, Nigeria, 101241\"})]})})})});};// Contact CTA Section\nconst ContactCTA=()=>{const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"text-center max-w-3xl mx-auto\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-6 text-primary-dark\",children:\"Ready to Start Trading?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mb-8\",children:\"Join thousands of traders who trust Blazetrade for their cryptocurrency exchange and trading needs. Sign up today and experience the difference.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"https://wa.me/2348163309355\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"btn-primary\",children:\"Chat with us\"}),/*#__PURE__*/_jsx(Link,{to:\"/about\",className:\"btn-secondary\",children:\"Learn More\"})]})]})})});};// Contact Info Item Component\nconst ContactInfoItem=_ref=>{let{icon,title,content,link}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full bg-primary-dark bg-opacity-10 flex items-center justify-center mr-4 flex-shrink-0\",children:icon}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"a\",{href:link,className:\"text-gray-600 hover:text-accent transition-colors duration-300\",target:\"_blank\",rel:\"noopener noreferrer\",children:content})]})]});};// Social Link Component\nconst SocialLink=_ref2=>{let{icon,href}=_ref2;return/*#__PURE__*/_jsx(\"a\",{href:href,target:\"_blank\",rel:\"noopener noreferrer\",className:\"w-10 h-10 rounded-full bg-primary-dark bg-opacity-10 flex items-center justify-center hover:bg-primary-dark hover:text-white transition-all duration-300 text-primary-dark\",children:icon});};export default Contact;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAnimation", "useInView", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "FaPaperPlane", "FaInstagram", "Link", "NavLink", "jsx", "_jsx", "jsxs", "_jsxs", "Contact", "className", "children", "ContactHero", "ContactSection", "MapSection", "ContactCTA", "div", "initial", "opacity", "y", "animate", "transition", "duration", "formData", "setFormData", "name", "email", "subject", "message", "formStatus", "setFormStatus", "submitted", "success", "handleChange", "e", "value", "target", "prevState", "handleSubmit", "preventDefault", "whatsappMessage", "whatsappUrl", "encodeURIComponent", "window", "open", "setTimeout", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "x", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "rows", "ContactInfoItem", "icon", "title", "content", "link", "SocialLink", "href", "rel", "to", "_ref", "_ref2"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Contact.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaPaperPlane, FaInstagram } from 'react-icons/fa';\nimport { Link, NavLink } from 'react-router-dom';\n\nconst Contact = () => {\n\n  return (\n    <div className=\"pt-16\">\n      {/* Hero Section */}\n      <ContactHero />\n      \n      {/* Contact Form and Info */}\n      <ContactSection />\n      \n      {/* Map Section */}\n      <MapSection />\n      \n      {/* Contact CTA */}\n      <ContactCTA />\n    </div>\n  );\n};\n\n// Contact Hero Section\nconst ContactHero = () => {\n  return (\n    <section className=\"relative bg-primary-dark text-white py-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"></div>\n        <div className=\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10\">\n        <motion.div \n          className=\"max-w-3xl\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">Contact Us</h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Have questions about our Bitcoin exchange and trading services? \n            Our team is here to help. Reach out to us using the contact information below.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Contact Form and Info Section\nconst ContactSection = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [formStatus, setFormStatus] = useState({\n    submitted: false,\n    success: false,\n    message: ''\n  });\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    const { name, email, subject, message } = formData;\n    const whatsappMessage = `Name: ${name}\\nEmail: ${email}\\nSubject: ${subject}\\nMessage: ${message}`;\n    const whatsappUrl = `https://wa.me/2348163309355?text=${encodeURIComponent(whatsappMessage)}`;\n    window.open(whatsappUrl, '_blank');\n\n    // Reset form after submission\n    setFormData({\n      name: '',\n      email: '',\n      subject: '',\n      message: ''\n    });\n\n    // Optional: Show a success message\n    setFormStatus({\n      submitted: true,\n      success: true,\n      message: 'You will be redirected to WhatsApp to send your message.'\n    });\n\n    // Reset form status after 5 seconds\n    setTimeout(() => {\n      setFormStatus({\n        submitted: false,\n        success: false,\n        message: ''\n      });\n    }, 5000);\n  };\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.3\n              }\n            }\n          }}\n        >\n          {/* Contact Form */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: -30 },\n              visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }\n            }}\n          >\n            <h2 className=\"text-3xl font-bold mb-6 text-primary-dark\">Send Us a Message</h2>\n            <p className=\"text-gray-600 mb-8\">\n              Fill out the form below and our team will get back to you as soon as possible.\n            </p>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">Your Name</label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">Email Address</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-1\">Subject</label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-1\">Message</label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows=\"5\"\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-dark focus:border-transparent text-gray-900\"\n                  required\n                ></textarea>\n              </div>\n              \n              <div>\n                <button \n                  type=\"submit\" \n                  className=\"btn-primary flex items-center justify-center w-full\"\n                >\n                  Send Message <FaPaperPlane className=\"ml-2\" />\n                </button>\n              </div>\n              \n              {formStatus.submitted && (\n                <motion.div \n                  className={`p-4 rounded-md ${formStatus.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  {formStatus.message}\n                </motion.div>\n              )}\n            </form>\n          </motion.div>\n          \n          {/* Contact Information */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: 30 },\n              visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }\n            }}\n          >\n            <h2 className=\"text-3xl font-bold mb-6 text-primary-dark\">Contact Information</h2>\n            <p className=\"text-gray-600 mb-8\">\n              You can reach out to us through the following channels. Our support team is available 24/7 to assist you.\n            </p>\n            \n            <div className=\"space-y-6\">\n              <ContactInfoItem \n                icon={<FaEnvelope className=\"text-primary-dark\" />}\n                title=\"Email Us\"\n                content=\"<EMAIL>\"\n                link=\"mailto:<EMAIL>\"\n              />\n              \n              <ContactInfoItem \n                icon={<FaPhone className=\"text-accent\" />}\n                title=\"Phone\"\n                content=\"+234 ************\"\n                link=\"tel:+2348163309355\"\n              />\n              \n              <ContactInfoItem \n                icon={<FaMapMarkerAlt className=\"text-accent\" />}\n                title=\"Address\"\n                content=\"Victoria island, Lagos, Nigeria, 101241\"\n                link=\"https://maps.google.com/?q=Victoria+island,+Lagos,+Nigeria\"\n              />\n            </div>\n            \n            <div className=\"mt-8\">\n              <h3 className=\"text-lg font-semibold mb-4 text-primary-dark\">Follow Us</h3>\n              <div className=\"flex space-x-4\">\n                <SocialLink icon={<FaInstagram />} href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" />\n                \n              </div>\n            </div>\n            \n            <div className=\"mt-8 p-6 bg-gray-50 rounded-lg border border-gray-100\">\n              <h3 className=\"text-lg font-semibold mb-2 text-primary-dark\">Business Hours</h3>\n              <p className=\"text-gray-600 mb-4\">Our support team is available 24/7</p>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Monday - Friday:</span>\n                  <span className=\"font-medium\">9:00 AM - 6:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Saturday:</span>\n                  <span className=\"font-medium\">10:00 AM - 4:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Sunday:</span>\n                  <span className=\"font-medium\">Closed</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Map Section\nconst MapSection = () => {\n  return (\n    <section className=\"section bg-gray-50 py-0\">\n      <div className=\"h-96 bg-gray-300 w-full\">\n        {/* In a real application, you would embed a Google Map or other map service here */}\n        <div className=\"w-full h-full flex items-center justify-center bg-primary-dark bg-opacity-10\">\n          <div className=\"text-center\">\n            <FaMapMarkerAlt className=\"text-5xl text-primary-dark mx-auto mb-4\" />\n            <h3 className=\"text-xl font-semibold text-primary-dark\">Bblazetrade Headquarters</h3>\n            <p className=\"text-gray-600\">Victoria island, Lagos, Nigeria, 101241</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Contact CTA Section\nconst ContactCTA = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"text-center max-w-3xl mx-auto\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6 text-primary-dark\">Ready to Start Trading?</h2>\n          <p className=\"text-lg text-gray-600 mb-8\">\n            Join thousands of traders who trust Blazetrade for their cryptocurrency exchange and trading needs.\n            Sign up today and experience the difference.\n          </p>\n          <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4\">\n            <a href=\"https://wa.me/2348163309355\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"btn-primary\">Chat with us</a>\n            <Link to=\"/about\" className=\"btn-secondary\">Learn More</Link>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Contact Info Item Component\nconst ContactInfoItem = ({ icon, title, content, link }) => {\n  return (\n    <div className=\"flex items-start\">\n      <div className=\"w-10 h-10 rounded-full bg-primary-dark bg-opacity-10 flex items-center justify-center mr-4 flex-shrink-0\">\n        {icon}\n      </div>\n      <div>\n        <h3 className=\"text-lg font-semibold text-primary-dark\">{title}</h3>\n        <a \n          href={link} \n          className=\"text-gray-600 hover:text-accent transition-colors duration-300\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          {content}\n        </a>\n      </div>\n    </div>\n  );\n};\n\n// Social Link Component\nconst SocialLink = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-10 h-10 rounded-full bg-primary-dark bg-opacity-10 flex items-center justify-center hover:bg-primary-dark hover:text-white transition-all duration-300 text-primary-dark\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nexport default Contact;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,YAAY,KAAQ,eAAe,CACpD,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OAASC,UAAU,CAAEC,OAAO,CAAEC,cAAc,CAAEC,YAAY,CAAEC,WAAW,KAAQ,gBAAgB,CAC/F,OAASC,IAAI,CAAEC,OAAO,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAEpB,mBACED,KAAA,QAAKE,SAAS,CAAC,OAAO,CAAAC,QAAA,eAEpBL,IAAA,CAACM,WAAW,GAAE,CAAC,cAGfN,IAAA,CAACO,cAAc,GAAE,CAAC,cAGlBP,IAAA,CAACQ,UAAU,GAAE,CAAC,cAGdR,IAAA,CAACS,UAAU,GAAE,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAH,WAAW,CAAGA,CAAA,GAAM,CACxB,mBACEJ,KAAA,YAASE,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eAE5DH,KAAA,QAAKE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CL,IAAA,QAAKI,SAAS,CAAC,qEAAqE,CAAM,CAAC,cAC3FJ,IAAA,QAAKI,SAAS,CAAC,wEAAwE,CAAM,CAAC,EAC3F,CAAC,cAENJ,IAAA,QAAKI,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CH,KAAA,CAACb,MAAM,CAACqB,GAAG,EACTN,SAAS,CAAC,WAAW,CACrBO,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,eAE9BL,IAAA,OAAII,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACnEL,IAAA,MAAGI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gJAG1C,CAAG,CAAC,EACM,CAAC,CACV,CAAC,EACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAG/B,QAAQ,CAAC,CACvCgC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,CAC3CsC,SAAS,CAAE,KAAK,CAChBC,OAAO,CAAE,KAAK,CACdJ,OAAO,CAAE,EACX,CAAC,CAAC,CAEF,KAAM,CAAAK,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAET,IAAI,CAAEU,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCZ,WAAW,CAACa,SAAS,GAAK,CACxB,GAAGA,SAAS,CACZ,CAACZ,IAAI,EAAGU,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIJ,CAAC,EAAK,CAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClB,KAAM,CAAEd,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAGL,QAAQ,CAClD,KAAM,CAAAiB,eAAe,CAAG,SAASf,IAAI,YAAYC,KAAK,cAAcC,OAAO,cAAcC,OAAO,EAAE,CAClG,KAAM,CAAAa,WAAW,CAAG,oCAAoCC,kBAAkB,CAACF,eAAe,CAAC,EAAE,CAC7FG,MAAM,CAACC,IAAI,CAACH,WAAW,CAAE,QAAQ,CAAC,CAElC;AACAjB,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CAAC,CAEF;AACAE,aAAa,CAAC,CACZC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbJ,OAAO,CAAE,0DACX,CAAC,CAAC,CAEF;AACAiB,UAAU,CAAC,IAAM,CACff,aAAa,CAAC,CACZC,SAAS,CAAE,KAAK,CAChBC,OAAO,CAAE,KAAK,CACdJ,OAAO,CAAE,EACX,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAkB,QAAQ,CAAGlD,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACmD,GAAG,CAAEC,MAAM,CAAC,CAAGnD,SAAS,CAAC,CAC9BoD,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFxD,SAAS,CAAC,IAAM,CACd,GAAIsD,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE1C,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCL,IAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BH,KAAA,CAACb,MAAM,CAACqB,GAAG,EACT+B,GAAG,CAAEA,GAAI,CACTrC,SAAS,CAAC,wCAAwC,CAClDO,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAE0B,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEnC,OAAO,CAAE,CAAE,CAAC,CACtBoC,OAAO,CAAE,CACPpC,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVkC,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAA5C,QAAA,eAGFH,KAAA,CAACb,MAAM,CAACqB,GAAG,EACToC,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEnC,OAAO,CAAE,CAAC,CAAEsC,CAAC,CAAE,CAAC,EAAG,CAAC,CAC9BF,OAAO,CAAE,CAAEpC,OAAO,CAAE,CAAC,CAAEsC,CAAC,CAAE,CAAC,CAAEnC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAX,QAAA,eAEFL,IAAA,OAAII,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAChFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gFAElC,CAAG,CAAC,cAEJH,KAAA,SAAMiD,QAAQ,CAAEnB,YAAa,CAAC5B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDH,KAAA,QAAAG,QAAA,eACEL,IAAA,UAAOoD,OAAO,CAAC,MAAM,CAAChD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAChGL,IAAA,UACEqD,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,MAAM,CACTnC,IAAI,CAAC,MAAM,CACXU,KAAK,CAAEZ,QAAQ,CAACE,IAAK,CACrBoC,QAAQ,CAAE5B,YAAa,CACvBvB,SAAS,CAAC,mJAAmJ,CAC7JoD,QAAQ,MACT,CAAC,EACC,CAAC,cAENtD,KAAA,QAAAG,QAAA,eACEL,IAAA,UAAOoD,OAAO,CAAC,OAAO,CAAChD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cACrGL,IAAA,UACEqD,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACVnC,IAAI,CAAC,OAAO,CACZU,KAAK,CAAEZ,QAAQ,CAACG,KAAM,CACtBmC,QAAQ,CAAE5B,YAAa,CACvBvB,SAAS,CAAC,mJAAmJ,CAC7JoD,QAAQ,MACT,CAAC,EACC,CAAC,cAENtD,KAAA,QAAAG,QAAA,eACEL,IAAA,UAAOoD,OAAO,CAAC,SAAS,CAAChD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACjGL,IAAA,UACEqD,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,SAAS,CACZnC,IAAI,CAAC,SAAS,CACdU,KAAK,CAAEZ,QAAQ,CAACI,OAAQ,CACxBkC,QAAQ,CAAE5B,YAAa,CACvBvB,SAAS,CAAC,mJAAmJ,CAC7JoD,QAAQ,MACT,CAAC,EACC,CAAC,cAENtD,KAAA,QAAAG,QAAA,eACEL,IAAA,UAAOoD,OAAO,CAAC,SAAS,CAAChD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACjGL,IAAA,aACEsD,EAAE,CAAC,SAAS,CACZnC,IAAI,CAAC,SAAS,CACdU,KAAK,CAAEZ,QAAQ,CAACK,OAAQ,CACxBiC,QAAQ,CAAE5B,YAAa,CACvB8B,IAAI,CAAC,GAAG,CACRrD,SAAS,CAAC,mJAAmJ,CAC7JoD,QAAQ,MACC,CAAC,EACT,CAAC,cAENxD,IAAA,QAAAK,QAAA,cACEH,KAAA,WACEmD,IAAI,CAAC,QAAQ,CACbjD,SAAS,CAAC,qDAAqD,CAAAC,QAAA,EAChE,eACc,cAAAL,IAAA,CAACL,YAAY,EAACS,SAAS,CAAC,MAAM,CAAE,CAAC,EACxC,CAAC,CACN,CAAC,CAELmB,UAAU,CAACE,SAAS,eACnBzB,IAAA,CAACX,MAAM,CAACqB,GAAG,EACTN,SAAS,CAAE,kBAAkBmB,UAAU,CAACG,OAAO,CAAG,6BAA6B,CAAG,yBAAyB,EAAG,CAC9Gf,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,CAE7BkB,UAAU,CAACD,OAAO,CACT,CACb,EACG,CAAC,EACG,CAAC,cAGbpB,KAAA,CAACb,MAAM,CAACqB,GAAG,EACToC,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEnC,OAAO,CAAE,CAAC,CAAEsC,CAAC,CAAE,EAAG,CAAC,CAC7BF,OAAO,CAAE,CAAEpC,OAAO,CAAE,CAAC,CAAEsC,CAAC,CAAE,CAAC,CAAEnC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAX,QAAA,eAEFL,IAAA,OAAII,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAClFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2GAElC,CAAG,CAAC,cAEJH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBL,IAAA,CAAC0D,eAAe,EACdC,IAAI,cAAE3D,IAAA,CAACR,UAAU,EAACY,SAAS,CAAC,mBAAmB,CAAE,CAAE,CACnDwD,KAAK,CAAC,UAAU,CAChBC,OAAO,CAAC,wBAAwB,CAChCC,IAAI,CAAC,+BAA+B,CACrC,CAAC,cAEF9D,IAAA,CAAC0D,eAAe,EACdC,IAAI,cAAE3D,IAAA,CAACP,OAAO,EAACW,SAAS,CAAC,aAAa,CAAE,CAAE,CAC1CwD,KAAK,CAAC,OAAO,CACbC,OAAO,CAAC,mBAAmB,CAC3BC,IAAI,CAAC,oBAAoB,CAC1B,CAAC,cAEF9D,IAAA,CAAC0D,eAAe,EACdC,IAAI,cAAE3D,IAAA,CAACN,cAAc,EAACU,SAAS,CAAC,aAAa,CAAE,CAAE,CACjDwD,KAAK,CAAC,SAAS,CACfC,OAAO,CAAC,yCAAyC,CACjDC,IAAI,CAAC,4DAA4D,CAClE,CAAC,EACC,CAAC,cAEN5D,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAC3EL,IAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BL,IAAA,CAAC+D,UAAU,EAACJ,IAAI,cAAE3D,IAAA,CAACJ,WAAW,GAAE,CAAE,CAACoE,IAAI,CAAC,sDAAsD,CAAE,CAAC,CAE9F,CAAC,EACH,CAAC,cAEN9D,KAAA,QAAKE,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpEL,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAChFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,oCAAkC,CAAG,CAAC,cACxEH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBH,KAAA,QAAKE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCL,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvDL,IAAA,SAAMI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,mBAAiB,CAAM,CAAC,EACnD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCL,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,cAChDL,IAAA,SAAMI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,oBAAkB,CAAM,CAAC,EACpD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCL,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cAC9CL,IAAA,SAAMI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EACxC,CAAC,EACH,CAAC,EACH,CAAC,EACI,CAAC,EACH,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACER,IAAA,YAASI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC1CL,IAAA,QAAKI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAEtCL,IAAA,QAAKI,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAC3FH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,CAACN,cAAc,EAACU,SAAS,CAAC,yCAAyC,CAAE,CAAC,cACtEJ,IAAA,OAAII,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACrFL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yCAAuC,CAAG,CAAC,EACrE,CAAC,CACH,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAI,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAA+B,QAAQ,CAAGlD,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACmD,GAAG,CAAEC,MAAM,CAAC,CAAGnD,SAAS,CAAC,CAC9BoD,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFxD,SAAS,CAAC,IAAM,CACd,GAAIsD,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE1C,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCL,IAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BH,KAAA,CAACb,MAAM,CAACqB,GAAG,EACT+B,GAAG,CAAEA,GAAI,CACTrC,SAAS,CAAC,+BAA+B,CACzCO,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAE0B,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEnC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BmC,OAAO,CAAE,CAAEpC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAX,QAAA,eAEFL,IAAA,OAAII,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAClGL,IAAA,MAAGI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kJAG1C,CAAG,CAAC,cACJH,KAAA,QAAKE,SAAS,CAAC,8EAA8E,CAAAC,QAAA,eAC3FL,IAAA,MAAGgE,IAAI,CAAC,6BAA6B,CAAClC,MAAM,CAAC,QAAQ,CAACmC,GAAG,CAAC,qBAAqB,CAAC7D,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cACxHL,IAAA,CAACH,IAAI,EAACqE,EAAE,CAAC,QAAQ,CAAC9D,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EAC1D,CAAC,EACI,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAqD,eAAe,CAAGS,IAAA,EAAoC,IAAnC,CAAER,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAK,IAAA,CACrD,mBACEjE,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BL,IAAA,QAAKI,SAAS,CAAC,0GAA0G,CAAAC,QAAA,CACtHsD,IAAI,CACF,CAAC,cACNzD,KAAA,QAAAG,QAAA,eACEL,IAAA,OAAII,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAEuD,KAAK,CAAK,CAAC,cACpE5D,IAAA,MACEgE,IAAI,CAAEF,IAAK,CACX1D,SAAS,CAAC,gEAAgE,CAC1E0B,MAAM,CAAC,QAAQ,CACfmC,GAAG,CAAC,qBAAqB,CAAA5D,QAAA,CAExBwD,OAAO,CACP,CAAC,EACD,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAE,UAAU,CAAGK,KAAA,EAAoB,IAAnB,CAAET,IAAI,CAAEK,IAAK,CAAC,CAAAI,KAAA,CAChC,mBACEpE,IAAA,MACEgE,IAAI,CAAEA,IAAK,CACXlC,MAAM,CAAC,QAAQ,CACfmC,GAAG,CAAC,qBAAqB,CACzB7D,SAAS,CAAC,4KAA4K,CAAAC,QAAA,CAErLsD,IAAI,CACJ,CAAC,CAER,CAAC,CAED,cAAe,CAAAxD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}