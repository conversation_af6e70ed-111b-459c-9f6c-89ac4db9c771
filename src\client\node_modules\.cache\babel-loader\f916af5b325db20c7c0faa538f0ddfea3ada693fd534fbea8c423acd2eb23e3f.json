{"ast": null, "code": "import React,{useState}from'react';import{Link,useNavigate}from'react-router-dom';import{sendPasswordResetEmail}from'firebase/auth';import{auth}from'../firebase';import{FaArrowLeft,FaEnvelope}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ForgotPassword=()=>{const[email,setEmail]=useState('');const[message,setMessage]=useState('');const[error,setError]=useState('');const[isLoading,setIsLoading]=useState(false);const navigate=useNavigate();const handleSubmit=async e=>{e.preventDefault();setError('');setMessage('');if(!email){setError('Please enter your email address');return;}setIsLoading(true);try{await sendPasswordResetEmail(auth,email);setMessage('Password reset email sent! Please check your inbox.');}catch(error){console.error('Password reset error:',error);if(error.code==='auth/user-not-found'){setError('No account found with this email address.');}else{setError('Failed to send password reset email. Please try again.');}}finally{setIsLoading(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-900 text-white flex flex-col\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/login\",className:\"inline-flex items-center text-blue-400 hover:text-blue-300 mb-8\",children:[/*#__PURE__*/_jsx(FaArrowLeft,{className:\"mr-2\"}),\" Back to Login\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(FaEnvelope,{className:\"text-3xl text-blue-500\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold\",children:\"Reset Your Password\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 mt-2\",children:\"Enter your email and we'll send you a link to reset your password.\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 p-3 bg-red-900/30 border border-red-700 text-red-400 rounded-lg text-sm\",children:error}),message?/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 p-4 bg-green-900/30 border border-green-700 text-green-400 rounded-lg\",children:message}):/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",className:\"block text-sm font-medium text-gray-400 mb-1\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",value:email,onChange:e=>setEmail(e.target.value),className:\"w-full px-4 py-3 bg-blue-900/50 border border-blue-800 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"<EMAIL>\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isLoading,className:\"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center\",children:isLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]}),\"Sending...\"]}):'Send Reset Link'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 text-center text-sm text-gray-500\",children:[\"Remember your password?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"text-blue-400 hover:text-blue-300 font-medium\",children:\"Back to Login\"})]})]})]})})});};export default ForgotPassword;", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "sendPasswordResetEmail", "auth", "FaArrowLeft", "FaEnvelope", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ForgotPassword", "email", "setEmail", "message", "setMessage", "error", "setError", "isLoading", "setIsLoading", "navigate", "handleSubmit", "e", "preventDefault", "console", "code", "className", "children", "to", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "disabled", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/ForgotPassword.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { sendPasswordResetEmail } from 'firebase/auth';\nimport { auth } from '../firebase';\nimport { FaArrowLeft, FaEnvelope } from 'react-icons/fa';\n\nconst ForgotPassword = () => {\n  const [email, setEmail] = useState('');\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setMessage('');\n    \n    if (!email) {\n      setError('Please enter your email address');\n      return;\n    }\n\n    setIsLoading(true);\n    \n    try {\n      await sendPasswordResetEmail(auth, email);\n      setMessage('Password reset email sent! Please check your inbox.');\n    } catch (error) {\n      console.error('Password reset error:', error);\n      if (error.code === 'auth/user-not-found') {\n        setError('No account found with this email address.');\n      } else {\n        setError('Failed to send password reset email. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-md\">\n          <Link \n            to=\"/login\" \n            className=\"inline-flex items-center text-blue-400 hover:text-blue-300 mb-8\"\n          >\n            <FaArrowLeft className=\"mr-2\" /> Back to Login\n          </Link>\n          \n          <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl\">\n            <div className=\"text-center mb-8\">\n              <div className=\"w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <FaEnvelope className=\"text-3xl text-blue-500\" />\n              </div>\n              <h1 className=\"text-2xl font-bold\">Reset Your Password</h1>\n              <p className=\"text-gray-400 mt-2\">\n                Enter your email and we'll send you a link to reset your password.\n              </p>\n            </div>\n\n            {error && (\n              <div className=\"mb-6 p-3 bg-red-900/30 border border-red-700 text-red-400 rounded-lg text-sm\">\n                {error}\n              </div>\n            )}\n\n            {message ? (\n              <div className=\"mb-6 p-4 bg-green-900/30 border border-green-700 text-green-400 rounded-lg\">\n                {message}\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-400 mb-1\">\n                    Email Address\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    className=\"w-full px-4 py-3 bg-blue-900/50 border border-blue-800 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n\n                <button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center\"\n                >\n                  {isLoading ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Sending...\n                    </>\n                  ) : 'Send Reset Link'}\n                </button>\n              </form>\n            )}\n\n            <div className=\"mt-6 text-center text-sm text-gray-500\">\n              Remember your password?{' '}\n              <Link to=\"/login\" className=\"text-blue-400 hover:text-blue-300 font-medium\">\n                Back to Login\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ForgotPassword;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,sBAAsB,KAAQ,eAAe,CACtD,OAASC,IAAI,KAAQ,aAAa,CAClC,OAASC,WAAW,CAAEC,UAAU,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoB,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAAsB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAqB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBN,QAAQ,CAAC,EAAE,CAAC,CACZF,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CAACH,KAAK,CAAE,CACVK,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,OACF,CAEAE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF,KAAM,CAAAlB,sBAAsB,CAACC,IAAI,CAAEU,KAAK,CAAC,CACzCG,UAAU,CAAC,qDAAqD,CAAC,CACnE,CAAE,MAAOC,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,GAAIA,KAAK,CAACS,IAAI,GAAK,qBAAqB,CAAE,CACxCR,QAAQ,CAAC,2CAA2C,CAAC,CACvD,CAAC,IAAM,CACLA,QAAQ,CAAC,wDAAwD,CAAC,CACpE,CACF,CAAC,OAAS,CACRE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,mBACEb,IAAA,QAAKoB,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAChErB,IAAA,QAAKoB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DnB,KAAA,QAAKkB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnB,KAAA,CAACT,IAAI,EACH6B,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,eAE3ErB,IAAA,CAACH,WAAW,EAACuB,SAAS,CAAC,MAAM,CAAE,CAAC,iBAClC,EAAM,CAAC,cAEPlB,KAAA,QAAKkB,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDnB,KAAA,QAAKkB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BrB,IAAA,QAAKoB,SAAS,CAAC,qFAAqF,CAAAC,QAAA,cAClGrB,IAAA,CAACF,UAAU,EAACsB,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC9C,CAAC,cACNpB,IAAA,OAAIoB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC3DrB,IAAA,MAAGoB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,oEAElC,CAAG,CAAC,EACD,CAAC,CAELX,KAAK,eACJV,IAAA,QAAKoB,SAAS,CAAC,8EAA8E,CAAAC,QAAA,CAC1FX,KAAK,CACH,CACN,CAEAF,OAAO,cACNR,IAAA,QAAKoB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACxFb,OAAO,CACL,CAAC,cAENN,KAAA,SAAMqB,QAAQ,CAAER,YAAa,CAACK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDnB,KAAA,QAAAmB,QAAA,eACErB,IAAA,UAAOwB,OAAO,CAAC,OAAO,CAACJ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhF,CAAO,CAAC,cACRrB,IAAA,UACEyB,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACVC,KAAK,CAAErB,KAAM,CACbsB,QAAQ,CAAGZ,CAAC,EAAKT,QAAQ,CAACS,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE,CAC1CP,SAAS,CAAC,qIAAqI,CAC/IU,WAAW,CAAC,iBAAiB,CAC7BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN/B,IAAA,WACEyB,IAAI,CAAC,QAAQ,CACbO,QAAQ,CAAEpB,SAAU,CACpBQ,SAAS,CAAC,yIAAyI,CAAAC,QAAA,CAElJT,SAAS,cACRV,KAAA,CAAAE,SAAA,EAAAiB,QAAA,eACEnB,KAAA,QAAKkB,SAAS,CAAC,4CAA4C,CAACa,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAd,QAAA,eAC5HrB,IAAA,WAAQoB,SAAS,CAAC,YAAY,CAACgB,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAS,CAAC,cACrGxC,IAAA,SAAMoB,SAAS,CAAC,YAAY,CAACc,IAAI,CAAC,cAAc,CAACO,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CAAC,aAER,EAAE,CAAC,CACD,iBAAiB,CACf,CAAC,EACL,CACP,cAEDvC,KAAA,QAAKkB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,yBAC/B,CAAC,GAAG,cAC3BrB,IAAA,CAACP,IAAI,EAAC6B,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,eAE5E,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}