{"ast": null, "code": "'use strict';\n\n/**\n * Expose classes\n */\nconst Attachment = require('./attachment');\nconst EmailAddress = require('./email-address');\nconst Mail = require('./mail');\nconst Personalization = require('./personalization');\nconst Response = require('./response');\nconst ResponseError = require('./response-error');\nconst Statistics = require('./statistics');\n\n/**\n * Export\n */\nmodule.exports = {\n  Attachment,\n  EmailAddress,\n  Mail,\n  Personalization,\n  Response,\n  ResponseError,\n  Statistics\n};", "map": {"version": 3, "names": ["Attachment", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mail", "Personalization", "Response", "ResponseError", "Statistics", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Expose classes\n */\nconst Attachment = require('./attachment');\nconst EmailAddress = require('./email-address');\nconst Mail = require('./mail');\nconst Personalization = require('./personalization');\nconst Response = require('./response');\nconst ResponseError = require('./response-error');\nconst Statistics = require('./statistics');\n\n/**\n * Export\n */\nmodule.exports = {\n  Attachment,\n  EmailAddress,\n  Mail,\n  Personalization,\n  Response,\n  ResponseError,\n  Statistics,\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,MAAMC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAME,IAAI,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAC9B,MAAMG,eAAe,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AACpD,MAAMI,QAAQ,GAAGJ,OAAO,CAAC,YAAY,CAAC;AACtC,MAAMK,aAAa,GAAGL,OAAO,CAAC,kBAAkB,CAAC;AACjD,MAAMM,UAAU,GAAGN,OAAO,CAAC,cAAc,CAAC;;AAE1C;AACA;AACA;AACAO,MAAM,CAACC,OAAO,GAAG;EACfT,UAAU;EACVE,YAAY;EACZC,IAAI;EACJC,eAAe;EACfC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}