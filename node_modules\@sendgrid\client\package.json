{"name": "@sendgrid/client", "description": "Twilio SendGrid NodeJS API client", "version": "8.1.5", "author": "Twilio SendGrid <<EMAIL>> (sendgrid.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Swift <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://sendgrid.com", "repository": {"type": "git", "url": "git://github.com/sendgrid/sendgrid-nodejs.git"}, "publishConfig": {"access": "public"}, "main": "index.js", "engines": {"node": ">=12.*"}, "dependencies": {"@sendgrid/helpers": "^8.0.0", "axios": "^1.8.2"}, "devDependencies": {"chai": "4.2.0", "nock": "^10.0.6"}, "resolutions": {"chai": "4.2.0"}, "tags": ["http", "rest", "api", "mail", "sendgrid"], "gitHead": "2bac86884f71be3fb19f96a10c02a1fb616b81fc"}