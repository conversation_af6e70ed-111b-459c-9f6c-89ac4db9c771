{"ast": null, "code": "import sgMail from'@sendgrid/mail';// Initialize SendGrid with your API key\nsgMail.setApiKey(process.env.REACT_APP_SENDGRID_API_KEY);// Function to send verification email\nexport const sendVerificationEmail=async(email,displayName,verificationLink)=>{const msg={to:email,from:'<EMAIL>',templateId:'d-c3eb32a8c66b4d47beff2b9c9513af62',dynamicTemplateData:{name:displayName||'Trader',verificationLink:verificationLink,appName:'BlazeTrade'}};try{await sgMail.send(msg);console.log('Verification email sent to',email);return{success:true};}catch(error){console.error('Error sending verification email:',error);throw error;}};// Function to send welcome email\nexport const sendWelcomeEmail=async(email,displayName)=>{const msg={to:email,from:'<EMAIL>',templateId:'d-8c1e56e48c05424faf4a25dd9cf637b2',dynamicTemplateData:{name:displayName||'Trader',loginTime:new Date().toLocaleString(),appName:'BlazeTrade'}};try{await sgMail.send(msg);console.log('Welcome email sent to',email);return{success:true};}catch(error){console.error('Error sending welcome email:',error);throw error;}};", "map": {"version": 3, "names": ["sgMail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_SENDGRID_API_KEY", "sendVerificationEmail", "email", "displayName", "verificationLink", "msg", "to", "from", "templateId", "dynamicTemplateData", "name", "appName", "send", "console", "log", "success", "error", "sendWelcomeEmail", "loginTime", "Date", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/utils/emailService.js"], "sourcesContent": ["import sgMail from '@sendgrid/mail';\n\n// Initialize SendGrid with your API key\nsgMail.setApiKey(process.env.REACT_APP_SENDGRID_API_KEY);\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-c3eb32a8c66b4d47beff2b9c9513af62',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      verificationLink: verificationLink,\n      appName: 'BlazeTrade'\n    },\n  };\n\n  try {\n    await sgMail.send(msg);\n    console.log('Verification email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-8c1e56e48c05424faf4a25dd9cf637b2',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      loginTime: new Date().toLocaleString(),\n      appName: 'BlazeTrade'\n    },\n  };\n\n  try {\n    await sgMail.send(msg);\n    console.log('Welcome email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,MAAO,CAAAA,MAAM,KAAM,gBAAgB,CAEnC;AACAA,MAAM,CAACC,SAAS,CAACC,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC,CAExD;AACA,MAAO,MAAM,CAAAC,qBAAqB,CAAG,KAAAA,CAAOC,KAAK,CAAEC,WAAW,CAAEC,gBAAgB,GAAK,CACnF,KAAM,CAAAC,GAAG,CAAG,CACVC,EAAE,CAAEJ,KAAK,CACTK,IAAI,CAAE,8BAA8B,CACpCC,UAAU,CAAE,oCAAoC,CAChDC,mBAAmB,CAAE,CACnBC,IAAI,CAAEP,WAAW,EAAI,QAAQ,CAC7BC,gBAAgB,CAAEA,gBAAgB,CAClCO,OAAO,CAAE,YACX,CACF,CAAC,CAED,GAAI,CACF,KAAM,CAAAf,MAAM,CAACgB,IAAI,CAACP,GAAG,CAAC,CACtBQ,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEZ,KAAK,CAAC,CAChD,MAAO,CAAEa,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOC,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,gBAAgB,CAAG,KAAAA,CAAOf,KAAK,CAAEC,WAAW,GAAK,CAC5D,KAAM,CAAAE,GAAG,CAAG,CACVC,EAAE,CAAEJ,KAAK,CACTK,IAAI,CAAE,8BAA8B,CACpCC,UAAU,CAAE,oCAAoC,CAChDC,mBAAmB,CAAE,CACnBC,IAAI,CAAEP,WAAW,EAAI,QAAQ,CAC7Be,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CACtCT,OAAO,CAAE,YACX,CACF,CAAC,CAED,GAAI,CACF,KAAM,CAAAf,MAAM,CAACgB,IAAI,CAACP,GAAG,CAAC,CACtBQ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEZ,KAAK,CAAC,CAC3C,MAAO,CAAEa,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOC,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}