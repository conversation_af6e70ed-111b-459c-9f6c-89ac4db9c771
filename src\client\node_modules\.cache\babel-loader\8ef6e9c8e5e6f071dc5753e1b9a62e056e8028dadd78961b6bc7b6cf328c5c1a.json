{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst toCamelCase = require('../helpers/to-camel-case');\nconst deepClone = require('../helpers/deep-clone');\n\n/**\n * Options\n */\nconst AggregatedByOptions = ['day', 'week', 'month'];\nconst CountryOptions = ['us', 'ca'];\nconst SortByDirection = ['desc', 'asc'];\n\n/**\n * Statistics class\n */\nclass Statistics {\n  constructor(data) {\n    this.startDate = null;\n    this.endDate = null;\n    this.aggregatedBy = null;\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * Build from data\n   */\n  fromData(data) {\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Statistics data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'customArgs']);\n    const {\n      startDate,\n      endDate,\n      aggregatedBy\n    } = data;\n    this.setStartDate(startDate);\n    this.setEndDate(endDate);\n    this.setAggregatedBy(aggregatedBy);\n  }\n\n  /**\n   * Set startDate\n   */\n  setStartDate(startDate) {\n    if (typeof startDate === 'undefined') {\n      throw new Error('Date expected for `startDate`');\n    }\n    if (new Date(startDate) === 'Invalid Date' || isNaN(new Date(startDate))) {\n      throw new Error('Date expected for `startDate`');\n    }\n    console.log(startDate);\n    this.startDate = new Date(startDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set endDate\n   */\n  setEndDate(endDate) {\n    if (typeof endDate === 'undefined') {\n      this.endDate = new Date().toISOString().slice(0, 10);\n      return;\n    }\n    if (new Date(endDate) === 'Invalid Date' || isNaN(new Date(endDate))) {\n      throw new Error('Date expected for `endDate`');\n    }\n    this.endDate = new Date(endDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set aggregatedBy\n   */\n  setAggregatedBy(aggregatedBy) {\n    if (typeof aggregatedBy === 'undefined') {\n      return;\n    }\n    if (typeof aggregatedBy === 'string' && AggregatedByOptions.includes(aggregatedBy.toLowerCase())) {\n      this.aggregatedBy = aggregatedBy;\n    } else {\n      throw new Error('Incorrect value for `aggregatedBy`');\n    }\n  }\n\n  /**\n   * Get Global\n   */\n  getGlobal() {\n    const {\n      startDate,\n      endDate,\n      aggregatedBy\n    } = this;\n    return {\n      startDate,\n      endDate,\n      aggregatedBy\n    };\n  }\n\n  /**\n   * Get Advanced\n   */\n  getAdvanced(country) {\n    const json = this.getGlobal();\n    if (typeof country === 'undefined') {\n      return json;\n    }\n    if (typeof country === 'string' && CountryOptions.includes(country.toLowerCase())) {\n      json.country = country;\n    }\n    return json;\n  }\n\n  /**\n   * Get Advanced Mailbox Providers\n   */\n  getAdvancedMailboxProviders(mailBoxProviders) {\n    const json = this.getGlobal();\n    if (typeof mailBoxProviders === 'undefined') {\n      return json;\n    }\n    if (Array.isArray(mailBoxProviders) && mailBoxProviders.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `mailboxProviders`');\n    }\n    json.mailBoxProviders = mailBoxProviders;\n    return json;\n  }\n\n  /**\n   * Get Advanced Browsers\n   */\n  getAdvancedBrowsers(browsers) {\n    const json = this.getGlobal();\n    if (typeof browsers === 'undefined') {\n      return json;\n    }\n    if (Array.isArray(browsers) && browsers.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `browsers`');\n    }\n    json.browsers = browsers;\n    return json;\n  }\n\n  /**\n   * Get Categories\n   */\n  getCategories(categories) {\n    if (typeof categories === 'undefined') {\n      throw new Error('Array of strings expected for `categories`');\n    }\n    if (!this._isValidArrayOfStrings(categories)) {\n      throw new Error('Array of strings expected for `categories`');\n    }\n    const json = this.getGlobal();\n    json.categories = categories;\n    return json;\n  }\n\n  /**\n   * Get Subuser\n   */\n  getSubuser(subusers) {\n    if (typeof subusers === 'undefined') {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n    if (!this._isValidArrayOfStrings(subusers)) {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n    const json = this.getGlobal();\n    json.subusers = subusers;\n    return json;\n  }\n\n  /**\n   * Get Subuser Sum\n   */\n  getSubuserSum(sortByMetric = 'delivered', sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n    const json = this.getGlobal();\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n    return json;\n  }\n\n  /**\n   * Get Subuser Monthly\n   */\n  getSubuserMonthly(sortByMetric = 'delivered', sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n    const json = this.getGlobal();\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n    return json;\n  }\n  _isValidArrayOfStrings(arr) {\n    if (!Array.isArray(arr)) {\n      return false;\n    }\n    if (arr.length < 1 || arr.some(x => typeof x !== 'string')) {\n      return false;\n    }\n    return true;\n  }\n}\n\n//Export class\nmodule.exports = Statistics;", "map": {"version": 3, "names": ["toCamelCase", "require", "deepClone", "AggregatedByOptions", "CountryOptions", "SortByDirection", "Statistics", "constructor", "data", "startDate", "endDate", "aggregatedBy", "fromData", "Error", "setStartDate", "setEndDate", "setAggregatedBy", "Date", "isNaN", "console", "log", "toISOString", "slice", "includes", "toLowerCase", "getGlobal", "getAdvanced", "country", "json", "getAdvancedMailboxProviders", "mailBoxProviders", "Array", "isArray", "some", "x", "getAdvancedBrowsers", "browsers", "getCategories", "categories", "_isValidArrayOfStrings", "getSubuser", "subusers", "getSubuserSum", "sortByMetric", "sortByDirection", "limit", "offset", "getSubuserMonthly", "arr", "length", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/statistics.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst toCamelCase = require('../helpers/to-camel-case');\nconst deepClone = require('../helpers/deep-clone');\n\n/**\n * Options\n */\nconst AggregatedByOptions = ['day', 'week', 'month'];\nconst CountryOptions = ['us', 'ca'];\nconst SortByDirection = ['desc', 'asc'];\n\n/**\n * Statistics class\n */\nclass Statistics {\n  constructor(data) {\n    this.startDate = null;\n    this.endDate = null;\n    this.aggregatedBy = null;\n\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * Build from data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Statistics data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'customArgs']);\n\n    const { startDate,\n      endDate,\n      aggregatedBy,\n    } = data;\n\n    this.setStartDate(startDate);\n    this.setEndDate(endDate);\n    this.setAggregatedBy(aggregatedBy);\n  }\n\n  /**\n   * Set startDate\n   */\n  setStartDate(startDate) {\n    if (typeof startDate === 'undefined') {\n      throw new Error('Date expected for `startDate`');\n    }\n\n    if ((new Date(startDate) === 'Invalid Date') ||\n        isNaN(new Date(startDate))) {\n      throw new Error('Date expected for `startDate`');\n    }\n\n    console.log(startDate);\n\n    this.startDate = new Date(startDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set endDate\n   */\n  setEndDate(endDate) {\n    if (typeof endDate === 'undefined') {\n      this.endDate = new Date().toISOString().slice(0, 10);\n      return;\n    }\n\n    if (new Date(endDate) === 'Invalid Date' || isNaN(new Date(endDate))) {\n      throw new Error('Date expected for `endDate`');\n    }\n\n    this.endDate = new Date(endDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set aggregatedBy\n   */\n  setAggregatedBy(aggregatedBy) {\n    if (typeof aggregatedBy === 'undefined') {\n      return;\n    }\n\n    if (typeof aggregatedBy === 'string' &&\n        AggregatedByOptions.includes(aggregatedBy.toLowerCase())) {\n      this.aggregatedBy = aggregatedBy;\n    } else {\n      throw new Error('Incorrect value for `aggregatedBy`');\n    }\n  }\n\n  /**\n   * Get Global\n   */\n  getGlobal() {\n    const { startDate, endDate, aggregatedBy } = this;\n\n    return { startDate, endDate, aggregatedBy };\n  }\n\n  /**\n   * Get Advanced\n   */\n  getAdvanced(country) {\n    const json = this.getGlobal();\n\n    if (typeof country === 'undefined') {\n      return json;\n    }\n\n    if (typeof country === 'string' &&\n        CountryOptions.includes(country.toLowerCase())) {\n      json.country = country;\n    }\n\n    return json;\n  }\n\n  /**\n   * Get Advanced Mailbox Providers\n   */\n  getAdvancedMailboxProviders(mailBoxProviders) {\n    const json = this.getGlobal();\n\n    if (typeof mailBoxProviders === 'undefined') {\n      return json;\n    }\n\n    if (Array.isArray(mailBoxProviders) &&\n        mailBoxProviders.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `mailboxProviders`');\n    }\n\n    json.mailBoxProviders = mailBoxProviders;\n\n    return json;\n  }\n\n  /**\n   * Get Advanced Browsers\n   */\n  getAdvancedBrowsers(browsers) {\n    const json = this.getGlobal();\n\n    if (typeof browsers === 'undefined') {\n      return json;\n    }\n\n    if (Array.isArray(browsers) && browsers.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `browsers`');\n    }\n\n    json.browsers = browsers;\n\n    return json;\n  }\n\n  /**\n   * Get Categories\n   */\n  getCategories(categories) {\n    if (typeof categories === 'undefined') {\n      throw new Error('Array of strings expected for `categories`');\n    }\n\n    if (!this._isValidArrayOfStrings(categories)) {\n      throw new Error('Array of strings expected for `categories`');\n    }\n\n    const json = this.getGlobal();\n    json.categories = categories;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser\n   */\n  getSubuser(subusers) {\n    if (typeof subusers === 'undefined') {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n\n    if (!this._isValidArrayOfStrings(subusers)) {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n\n    const json = this.getGlobal();\n    json.subusers = subusers;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser Sum\n   */\n  getSubuserSum(sortByMetric = 'delivered',\n    sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n\n    const json = this.getGlobal();\n\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser Monthly\n   */\n  getSubuserMonthly(sortByMetric = 'delivered',\n    sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n\n    const json = this.getGlobal();\n\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n\n    return json;\n  }\n\n  _isValidArrayOfStrings(arr) {\n    if (!Array.isArray(arr)) {\n      return false;\n    }\n\n    if (arr.length < 1 || arr.some(x => typeof x !== 'string')) {\n      return false;\n    }\n\n    return true;\n  }\n}\n\n//Export class\nmodule.exports = Statistics;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACvD,MAAMC,SAAS,GAAGD,OAAO,CAAC,uBAAuB,CAAC;;AAElD;AACA;AACA;AACA,MAAME,mBAAmB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AACpD,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AACnC,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;;AAEvC;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB,IAAIH,IAAI,EAAE;MACR,IAAI,CAACI,QAAQ,CAACJ,IAAI,CAAC;IACrB;EACF;;EAEA;AACF;AACA;EACEI,QAAQA,CAACJ,IAAI,EAAE;IAEb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIK,KAAK,CAAC,sCAAsC,CAAC;IACzD;;IAEA;IACA;IACAL,IAAI,GAAGN,SAAS,CAACM,IAAI,CAAC;IACtBA,IAAI,GAAGR,WAAW,CAACQ,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;IAEzD,MAAM;MAAEC,SAAS;MACfC,OAAO;MACPC;IACF,CAAC,GAAGH,IAAI;IAER,IAAI,CAACM,YAAY,CAACL,SAAS,CAAC;IAC5B,IAAI,CAACM,UAAU,CAACL,OAAO,CAAC;IACxB,IAAI,CAACM,eAAe,CAACL,YAAY,CAAC;EACpC;;EAEA;AACF;AACA;EACEG,YAAYA,CAACL,SAAS,EAAE;IACtB,IAAI,OAAOA,SAAS,KAAK,WAAW,EAAE;MACpC,MAAM,IAAII,KAAK,CAAC,+BAA+B,CAAC;IAClD;IAEA,IAAK,IAAII,IAAI,CAACR,SAAS,CAAC,KAAK,cAAc,IACvCS,KAAK,CAAC,IAAID,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE;MAC9B,MAAM,IAAII,KAAK,CAAC,+BAA+B,CAAC;IAClD;IAEAM,OAAO,CAACC,GAAG,CAACX,SAAS,CAAC;IAEtB,IAAI,CAACA,SAAS,GAAG,IAAIQ,IAAI,CAACR,SAAS,CAAC,CAACY,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACjE;;EAEA;AACF;AACA;EACEP,UAAUA,CAACL,OAAO,EAAE;IAClB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClC,IAAI,CAACA,OAAO,GAAG,IAAIO,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACpD;IACF;IAEA,IAAI,IAAIL,IAAI,CAACP,OAAO,CAAC,KAAK,cAAc,IAAIQ,KAAK,CAAC,IAAID,IAAI,CAACP,OAAO,CAAC,CAAC,EAAE;MACpE,MAAM,IAAIG,KAAK,CAAC,6BAA6B,CAAC;IAChD;IAEA,IAAI,CAACH,OAAO,GAAG,IAAIO,IAAI,CAACP,OAAO,CAAC,CAACW,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D;;EAEA;AACF;AACA;EACEN,eAAeA,CAACL,YAAY,EAAE;IAC5B,IAAI,OAAOA,YAAY,KAAK,WAAW,EAAE;MACvC;IACF;IAEA,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAChCR,mBAAmB,CAACoB,QAAQ,CAACZ,YAAY,CAACa,WAAW,CAAC,CAAC,CAAC,EAAE;MAC5D,IAAI,CAACb,YAAY,GAAGA,YAAY;IAClC,CAAC,MAAM;MACL,MAAM,IAAIE,KAAK,CAAC,oCAAoC,CAAC;IACvD;EACF;;EAEA;AACF;AACA;EACEY,SAASA,CAAA,EAAG;IACV,MAAM;MAAEhB,SAAS;MAAEC,OAAO;MAAEC;IAAa,CAAC,GAAG,IAAI;IAEjD,OAAO;MAAEF,SAAS;MAAEC,OAAO;MAAEC;IAAa,CAAC;EAC7C;;EAEA;AACF;AACA;EACEe,WAAWA,CAACC,OAAO,EAAE;IACnB,MAAMC,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAE7B,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAClC,OAAOC,IAAI;IACb;IAEA,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAC3BvB,cAAc,CAACmB,QAAQ,CAACI,OAAO,CAACH,WAAW,CAAC,CAAC,CAAC,EAAE;MAClDI,IAAI,CAACD,OAAO,GAAGA,OAAO;IACxB;IAEA,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;EACEC,2BAA2BA,CAACC,gBAAgB,EAAE;IAC5C,MAAMF,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAE7B,IAAI,OAAOK,gBAAgB,KAAK,WAAW,EAAE;MAC3C,OAAOF,IAAI;IACb;IAEA,IAAIG,KAAK,CAACC,OAAO,CAACF,gBAAgB,CAAC,IAC/BA,gBAAgB,CAACG,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,CAAC,EAAE;MACrD,MAAM,IAAIrB,KAAK,CAAC,kDAAkD,CAAC;IACrE;IAEAe,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;IAExC,OAAOF,IAAI;EACb;;EAEA;AACF;AACA;EACEO,mBAAmBA,CAACC,QAAQ,EAAE;IAC5B,MAAMR,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAE7B,IAAI,OAAOW,QAAQ,KAAK,WAAW,EAAE;MACnC,OAAOR,IAAI;IACb;IAEA,IAAIG,KAAK,CAACC,OAAO,CAACI,QAAQ,CAAC,IAAIA,QAAQ,CAACH,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,CAAC,EAAE;MACxE,MAAM,IAAIrB,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEAe,IAAI,CAACQ,QAAQ,GAAGA,QAAQ;IAExB,OAAOR,IAAI;EACb;;EAEA;AACF;AACA;EACES,aAAaA,CAACC,UAAU,EAAE;IACxB,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;MACrC,MAAM,IAAIzB,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEA,IAAI,CAAC,IAAI,CAAC0B,sBAAsB,CAACD,UAAU,CAAC,EAAE;MAC5C,MAAM,IAAIzB,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEA,MAAMe,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAC7BG,IAAI,CAACU,UAAU,GAAGA,UAAU;IAE5B,OAAOV,IAAI;EACb;;EAEA;AACF;AACA;EACEY,UAAUA,CAACC,QAAQ,EAAE;IACnB,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE;MACnC,MAAM,IAAI5B,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAI,CAAC,IAAI,CAAC0B,sBAAsB,CAACE,QAAQ,CAAC,EAAE;MAC1C,MAAM,IAAI5B,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,MAAMe,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAC7BG,IAAI,CAACa,QAAQ,GAAGA,QAAQ;IAExB,OAAOb,IAAI;EACb;;EAEA;AACF;AACA;EACEc,aAAaA,CAACC,YAAY,GAAG,WAAW,EACtCC,eAAe,GAAGvC,eAAe,CAAC,CAAC,CAAC,EAAEwC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC7D,IAAI,OAAOH,YAAY,KAAK,QAAQ,EAAE;MACpC,MAAM,IAAI9B,KAAK,CAAC,oCAAoC,CAAC;IACvD;IAEA,IAAI,CAACR,eAAe,CAACkB,QAAQ,CAACqB,eAAe,CAACpB,WAAW,CAAC,CAAC,CAAC,EAAE;MAC5D,MAAM,IAAIX,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEA,IAAI,OAAOgC,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIhC,KAAK,CAAC,6BAA6B,CAAC;IAChD;IAEA,IAAI,OAAOiC,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAIjC,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,MAAMe,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAE7BG,IAAI,CAACe,YAAY,GAAGA,YAAY;IAChCf,IAAI,CAACgB,eAAe,GAAGA,eAAe;IACtChB,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClBjB,IAAI,CAACkB,MAAM,GAAGA,MAAM;IAEpB,OAAOlB,IAAI;EACb;;EAEA;AACF;AACA;EACEmB,iBAAiBA,CAACJ,YAAY,GAAG,WAAW,EAC1CC,eAAe,GAAGvC,eAAe,CAAC,CAAC,CAAC,EAAEwC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC7D,IAAI,OAAOH,YAAY,KAAK,QAAQ,EAAE;MACpC,MAAM,IAAI9B,KAAK,CAAC,oCAAoC,CAAC;IACvD;IAEA,IAAI,CAACR,eAAe,CAACkB,QAAQ,CAACqB,eAAe,CAACpB,WAAW,CAAC,CAAC,CAAC,EAAE;MAC5D,MAAM,IAAIX,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEA,IAAI,OAAOgC,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIhC,KAAK,CAAC,6BAA6B,CAAC;IAChD;IAEA,IAAI,OAAOiC,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAIjC,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,MAAMe,IAAI,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAE7BG,IAAI,CAACe,YAAY,GAAGA,YAAY;IAChCf,IAAI,CAACgB,eAAe,GAAGA,eAAe;IACtChB,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClBjB,IAAI,CAACkB,MAAM,GAAGA,MAAM;IAEpB,OAAOlB,IAAI;EACb;EAEAW,sBAAsBA,CAACS,GAAG,EAAE;IAC1B,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACgB,GAAG,CAAC,EAAE;MACvB,OAAO,KAAK;IACd;IAEA,IAAIA,GAAG,CAACC,MAAM,GAAG,CAAC,IAAID,GAAG,CAACf,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,CAAC,EAAE;MAC1D,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACAgB,MAAM,CAACC,OAAO,GAAG7C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}