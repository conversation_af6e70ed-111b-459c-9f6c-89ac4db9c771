{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst toCamelCase = require('../helpers/to-camel-case');\nconst toSnakeCase = require('../helpers/to-snake-case');\nconst deepClone = require('../helpers/deep-clone');\nconst fs = require('fs');\nconst path = require('path');\n\n/**\n * Attachment class\n */\nclass Attachment {\n  /**\n   * Constructor\n   */\n  constructor(data) {\n    //Create from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data);\n\n    //Extract properties from data\n    const {\n      content,\n      filename,\n      type,\n      disposition,\n      contentId,\n      filePath\n    } = data;\n    if (typeof content !== 'undefined' && typeof filePath !== 'undefined') {\n      throw new Error(\"The props 'content' and 'filePath' cannot be used together.\");\n    }\n\n    //Set data\n    this.setFilename(filename);\n    this.setType(type);\n    this.setDisposition(disposition);\n    this.setContentId(contentId);\n    this.setContent(filePath ? this.readFile(filePath) : content);\n  }\n\n  /**\n   * Read a file and return its content as base64\n   */\n  readFile(filePath) {\n    return fs.readFileSync(path.resolve(filePath));\n  }\n\n  /**\n   * Set content\n   */\n  setContent(content) {\n    //Duck type check toString on content if it's a Buffer as that's the method that will be called.\n    if (typeof content === 'string') {\n      this.content = content;\n      return;\n    } else if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString();\n      if (this.disposition === 'attachment') {\n        this.content = content.toString('base64');\n      }\n      return;\n    }\n    throw new Error('`content` expected to be either Buffer or string');\n  }\n\n  /**\n   * Set content\n   */\n  setFileContent(content) {\n    if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString('base64');\n      return;\n    }\n    throw new Error('`content` expected to be Buffer');\n  }\n\n  /**\n   * Set filename\n   */\n  setFilename(filename) {\n    if (typeof filename === 'undefined') {\n      return;\n    }\n    if (filename && typeof filename !== 'string') {\n      throw new Error('String expected for `filename`');\n    }\n    this.filename = filename;\n  }\n\n  /**\n   * Set type\n   */\n  setType(type) {\n    if (typeof type === 'undefined') {\n      return;\n    }\n    if (typeof type !== 'string') {\n      throw new Error('String expected for `type`');\n    }\n    this.type = type;\n  }\n\n  /**\n   * Set disposition\n   */\n  setDisposition(disposition) {\n    if (typeof disposition === 'undefined') {\n      return;\n    }\n    if (typeof disposition !== 'string') {\n      throw new Error('String expected for `disposition`');\n    }\n    this.disposition = disposition;\n  }\n\n  /**\n   * Set content ID\n   */\n  setContentId(contentId) {\n    if (typeof contentId === 'undefined') {\n      return;\n    }\n    if (typeof contentId !== 'string') {\n      throw new Error('String expected for `contentId`');\n    }\n    this.contentId = contentId;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n    //Extract properties from self\n    const {\n      content,\n      filename,\n      type,\n      disposition,\n      contentId\n    } = this;\n\n    //Initialize with mandatory properties\n    const json = {\n      content,\n      filename\n    };\n\n    //Add whatever else we have\n    if (typeof type !== 'undefined') {\n      json.type = type;\n    }\n    if (typeof disposition !== 'undefined') {\n      json.disposition = disposition;\n    }\n    if (typeof contentId !== 'undefined') {\n      json.contentId = contentId;\n    }\n\n    //Return\n    return toSnakeCase(json);\n  }\n}\n\n//Export class\nmodule.exports = Attachment;", "map": {"version": 3, "names": ["toCamelCase", "require", "toSnakeCase", "deepClone", "fs", "path", "Attachment", "constructor", "data", "fromData", "Error", "content", "filename", "type", "disposition", "contentId", "filePath", "setFilename", "setType", "setDisposition", "setContentId", "<PERSON><PERSON><PERSON><PERSON>", "readFile", "readFileSync", "resolve", "<PERSON><PERSON><PERSON>", "toString", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toJSON", "json", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/attachment.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst toCamelCase = require('../helpers/to-camel-case');\nconst toSnakeCase = require('../helpers/to-snake-case');\nconst deepClone = require('../helpers/deep-clone');\nconst fs = require('fs');\nconst path = require('path');\n\n/**\n * Attachment class\n */\nclass Attachment {\n\n  /**\n   * Constructor\n   */\n  constructor(data) {\n\n    //Create from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data);\n\n    //Extract properties from data\n    const {\n      content,\n      filename,\n      type,\n      disposition,\n      contentId,\n      filePath,\n    } = data;\n\n    if ((typeof content !== 'undefined') && (typeof filePath !== 'undefined')) {\n      throw new Error(\n        \"The props 'content' and 'filePath' cannot be used together.\"\n      );\n    }\n\n    //Set data\n    this.setFilename(filename);\n    this.setType(type);\n    this.setDisposition(disposition);\n    this.setContentId(contentId);\n    this.setContent(filePath ? this.readFile(filePath) : content);\n  }\n\n  /**\n   * Read a file and return its content as base64\n   */\n  readFile(filePath) {\n    return fs.readFileSync(path.resolve(filePath));\n  }\n\n  /**\n   * Set content\n   */\n  setContent(content) {\n    //Duck type check toString on content if it's a Buffer as that's the method that will be called.\n    if (typeof content === 'string') {\n      this.content = content;\n      return;\n    } else if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString();\n\n      if (this.disposition === 'attachment') {\n        this.content = content.toString('base64');\n      }\n\n      return;\n    }\n\n    throw new Error('`content` expected to be either Buffer or string');\n  }\n\n  /**\n   * Set content\n   */\n  setFileContent(content) {\n    if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString('base64');\n      return;\n    }\n\n    throw new Error('`content` expected to be Buffer');\n  }\n\n  /**\n   * Set filename\n   */\n  setFilename(filename) {\n    if (typeof filename === 'undefined') {\n      return;\n    }\n    if (filename && typeof filename !== 'string') {\n      throw new Error('String expected for `filename`');\n    }\n    this.filename = filename;\n  }\n\n  /**\n   * Set type\n   */\n  setType(type) {\n    if (typeof type === 'undefined') {\n      return;\n    }\n    if (typeof type !== 'string') {\n      throw new Error('String expected for `type`');\n    }\n    this.type = type;\n  }\n\n  /**\n   * Set disposition\n   */\n  setDisposition(disposition) {\n    if (typeof disposition === 'undefined') {\n      return;\n    }\n    if (typeof disposition !== 'string') {\n      throw new Error('String expected for `disposition`');\n    }\n    this.disposition = disposition;\n  }\n\n  /**\n   * Set content ID\n   */\n  setContentId(contentId) {\n    if (typeof contentId === 'undefined') {\n      return;\n    }\n    if (typeof contentId !== 'string') {\n      throw new Error('String expected for `contentId`');\n    }\n    this.contentId = contentId;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n\n    //Extract properties from self\n    const {content, filename, type, disposition, contentId} = this;\n\n    //Initialize with mandatory properties\n    const json = {content, filename};\n\n    //Add whatever else we have\n    if (typeof type !== 'undefined') {\n      json.type = type;\n    }\n    if (typeof disposition !== 'undefined') {\n      json.disposition = disposition;\n    }\n    if (typeof contentId !== 'undefined') {\n      json.contentId = contentId;\n    }\n\n    //Return\n    return toSnakeCase(json);\n  }\n}\n\n//Export class\nmodule.exports = Attachment;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACvD,MAAMC,WAAW,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACvD,MAAME,SAAS,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAClD,MAAMG,EAAE,GAAGH,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMI,IAAI,GAAGJ,OAAO,CAAC,MAAM,CAAC;;AAE5B;AACA;AACA;AACA,MAAMK,UAAU,CAAC;EAEf;AACF;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAEhB;IACA,IAAIA,IAAI,EAAE;MACR,IAAI,CAACC,QAAQ,CAACD,IAAI,CAAC;IACrB;EACF;;EAEA;AACF;AACA;EACEC,QAAQA,CAACD,IAAI,EAAE;IAEb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIE,KAAK,CAAC,gCAAgC,CAAC;IACnD;;IAEA;IACA;IACAF,IAAI,GAAGL,SAAS,CAACK,IAAI,CAAC;IACtBA,IAAI,GAAGR,WAAW,CAACQ,IAAI,CAAC;;IAExB;IACA,MAAM;MACJG,OAAO;MACPC,QAAQ;MACRC,IAAI;MACJC,WAAW;MACXC,SAAS;MACTC;IACF,CAAC,GAAGR,IAAI;IAER,IAAK,OAAOG,OAAO,KAAK,WAAW,IAAM,OAAOK,QAAQ,KAAK,WAAY,EAAE;MACzE,MAAM,IAAIN,KAAK,CACb,6DACF,CAAC;IACH;;IAEA;IACA,IAAI,CAACO,WAAW,CAACL,QAAQ,CAAC;IAC1B,IAAI,CAACM,OAAO,CAACL,IAAI,CAAC;IAClB,IAAI,CAACM,cAAc,CAACL,WAAW,CAAC;IAChC,IAAI,CAACM,YAAY,CAACL,SAAS,CAAC;IAC5B,IAAI,CAACM,UAAU,CAACL,QAAQ,GAAG,IAAI,CAACM,QAAQ,CAACN,QAAQ,CAAC,GAAGL,OAAO,CAAC;EAC/D;;EAEA;AACF;AACA;EACEW,QAAQA,CAACN,QAAQ,EAAE;IACjB,OAAOZ,EAAE,CAACmB,YAAY,CAAClB,IAAI,CAACmB,OAAO,CAACR,QAAQ,CAAC,CAAC;EAChD;;EAEA;AACF;AACA;EACEK,UAAUA,CAACV,OAAO,EAAE;IAClB;IACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB;IACF,CAAC,MAAM,IAAIA,OAAO,YAAYc,MAAM,IAAId,OAAO,CAACe,QAAQ,KAAKC,SAAS,EAAE;MACtE,IAAI,CAAChB,OAAO,GAAGA,OAAO,CAACe,QAAQ,CAAC,CAAC;MAEjC,IAAI,IAAI,CAACZ,WAAW,KAAK,YAAY,EAAE;QACrC,IAAI,CAACH,OAAO,GAAGA,OAAO,CAACe,QAAQ,CAAC,QAAQ,CAAC;MAC3C;MAEA;IACF;IAEA,MAAM,IAAIhB,KAAK,CAAC,kDAAkD,CAAC;EACrE;;EAEA;AACF;AACA;EACEkB,cAAcA,CAACjB,OAAO,EAAE;IACtB,IAAIA,OAAO,YAAYc,MAAM,IAAId,OAAO,CAACe,QAAQ,KAAKC,SAAS,EAAE;MAC/D,IAAI,CAAChB,OAAO,GAAGA,OAAO,CAACe,QAAQ,CAAC,QAAQ,CAAC;MACzC;IACF;IAEA,MAAM,IAAIhB,KAAK,CAAC,iCAAiC,CAAC;EACpD;;EAEA;AACF;AACA;EACEO,WAAWA,CAACL,QAAQ,EAAE;IACpB,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IACA,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5C,MAAM,IAAIF,KAAK,CAAC,gCAAgC,CAAC;IACnD;IACA,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EAC1B;;EAEA;AACF;AACA;EACEM,OAAOA,CAACL,IAAI,EAAE;IACZ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC/B;IACF;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIH,KAAK,CAAC,4BAA4B,CAAC;IAC/C;IACA,IAAI,CAACG,IAAI,GAAGA,IAAI;EAClB;;EAEA;AACF;AACA;EACEM,cAAcA,CAACL,WAAW,EAAE;IAC1B,IAAI,OAAOA,WAAW,KAAK,WAAW,EAAE;MACtC;IACF;IACA,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAIJ,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACA,IAAI,CAACI,WAAW,GAAGA,WAAW;EAChC;;EAEA;AACF;AACA;EACEM,YAAYA,CAACL,SAAS,EAAE;IACtB,IAAI,OAAOA,SAAS,KAAK,WAAW,EAAE;MACpC;IACF;IACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACjC,MAAM,IAAIL,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,IAAI,CAACK,SAAS,GAAGA,SAAS;EAC5B;;EAEA;AACF;AACA;EACEc,MAAMA,CAAA,EAAG;IAEP;IACA,MAAM;MAAClB,OAAO;MAAEC,QAAQ;MAAEC,IAAI;MAAEC,WAAW;MAAEC;IAAS,CAAC,GAAG,IAAI;;IAE9D;IACA,MAAMe,IAAI,GAAG;MAACnB,OAAO;MAAEC;IAAQ,CAAC;;IAEhC;IACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC/BiB,IAAI,CAACjB,IAAI,GAAGA,IAAI;IAClB;IACA,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;MACtCgB,IAAI,CAAChB,WAAW,GAAGA,WAAW;IAChC;IACA,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;MACpCe,IAAI,CAACf,SAAS,GAAGA,SAAS;IAC5B;;IAEA;IACA,OAAOb,WAAW,CAAC4B,IAAI,CAAC;EAC1B;AACF;;AAEA;AACAC,MAAM,CAACC,OAAO,GAAG1B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}