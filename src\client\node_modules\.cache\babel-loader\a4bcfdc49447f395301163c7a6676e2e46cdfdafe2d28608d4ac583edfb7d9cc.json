{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n  point -= translate;\n  point = scalePoint(point, 1 / scale, originPoint);\n  if (boxScale !== undefined) {\n    point = scalePoint(point, 1 / boxScale, originPoint);\n  }\n  return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis) {\n  let translate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let scale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let origin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.5;\n  let boxScale = arguments.length > 4 ? arguments[4] : undefined;\n  let originAxis = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : axis;\n  let sourceAxis = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : axis;\n  if (percent.test(translate)) {\n    translate = parseFloat(translate);\n    const relativeProgress = mix(sourceAxis.min, sourceAxis.max, translate / 100);\n    translate = relativeProgress - sourceAxis.min;\n  }\n  if (typeof translate !== \"number\") return;\n  let originPoint = mix(originAxis.min, originAxis.max, origin);\n  if (axis === originAxis) originPoint -= translate;\n  axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n  axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, _ref, origin, sourceAxis) {\n  let [key, scaleKey, originKey] = _ref;\n  removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n  removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n  removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };", "map": {"version": 3, "names": ["mix", "percent", "scalePoint", "removePoint<PERSON><PERSON><PERSON>", "point", "translate", "scale", "originPoint", "boxScale", "undefined", "remove<PERSON>xis<PERSON><PERSON><PERSON>", "axis", "arguments", "length", "origin", "originAxis", "sourceAxis", "test", "parseFloat", "relativeProgress", "min", "max", "removeAxisTransforms", "transforms", "_ref", "key", "scaleKey", "<PERSON><PERSON><PERSON>", "xKeys", "y<PERSON><PERSON><PERSON>", "removeBoxTransforms", "box", "originBox", "sourceBox", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = scalePoint(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = scalePoint(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = mix(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = mix(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACtEJ,KAAK,IAAIC,SAAS;EAClBD,KAAK,GAAGF,UAAU,CAACE,KAAK,EAAE,CAAC,GAAGE,KAAK,EAAEC,WAAW,CAAC;EACjD,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IACxBL,KAAK,GAAGF,UAAU,CAACE,KAAK,EAAE,CAAC,GAAGI,QAAQ,EAAED,WAAW,CAAC;EACxD;EACA,OAAOH,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASM,eAAeA,CAACC,IAAI,EAA0F;EAAA,IAAxFN,SAAS,GAAAO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,CAAC;EAAA,IAAEN,KAAK,GAAAM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,CAAC;EAAA,IAAEE,MAAM,GAAAF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,GAAG;EAAA,IAAEJ,QAAQ,GAAAI,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAH,SAAA;EAAA,IAAEM,UAAU,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAGD,IAAI;EAAA,IAAEK,UAAU,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAGD,IAAI;EACjH,IAAIV,OAAO,CAACgB,IAAI,CAACZ,SAAS,CAAC,EAAE;IACzBA,SAAS,GAAGa,UAAU,CAACb,SAAS,CAAC;IACjC,MAAMc,gBAAgB,GAAGnB,GAAG,CAACgB,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAACK,GAAG,EAAEhB,SAAS,GAAG,GAAG,CAAC;IAC7EA,SAAS,GAAGc,gBAAgB,GAAGH,UAAU,CAACI,GAAG;EACjD;EACA,IAAI,OAAOf,SAAS,KAAK,QAAQ,EAC7B;EACJ,IAAIE,WAAW,GAAGP,GAAG,CAACe,UAAU,CAACK,GAAG,EAAEL,UAAU,CAACM,GAAG,EAAEP,MAAM,CAAC;EAC7D,IAAIH,IAAI,KAAKI,UAAU,EACnBR,WAAW,IAAIF,SAAS;EAC5BM,IAAI,CAACS,GAAG,GAAGjB,gBAAgB,CAACQ,IAAI,CAACS,GAAG,EAAEf,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC;EAC9EG,IAAI,CAACU,GAAG,GAAGlB,gBAAgB,CAACQ,IAAI,CAACU,GAAG,EAAEhB,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA,SAASc,oBAAoBA,CAACX,IAAI,EAAEY,UAAU,EAAAC,IAAA,EAA8BV,MAAM,EAAEE,UAAU,EAAE;EAAA,IAAhD,CAACS,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAAC,GAAAH,IAAA;EACtEd,eAAe,CAACC,IAAI,EAAEY,UAAU,CAACE,GAAG,CAAC,EAAEF,UAAU,CAACG,QAAQ,CAAC,EAAEH,UAAU,CAACI,SAAS,CAAC,EAAEJ,UAAU,CAACjB,KAAK,EAAEQ,MAAM,EAAEE,UAAU,CAAC;AAC7H;AACA;AACA;AACA;AACA,MAAMY,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,GAAG,EAAER,UAAU,EAAES,SAAS,EAAEC,SAAS,EAAE;EAChEX,oBAAoB,CAACS,GAAG,CAACG,CAAC,EAAEX,UAAU,EAAEK,KAAK,EAAEI,SAAS,GAAGA,SAAS,CAACE,CAAC,GAAGzB,SAAS,EAAEwB,SAAS,GAAGA,SAAS,CAACC,CAAC,GAAGzB,SAAS,CAAC;EACxHa,oBAAoB,CAACS,GAAG,CAACI,CAAC,EAAEZ,UAAU,EAAEM,KAAK,EAAEG,SAAS,GAAGA,SAAS,CAACG,CAAC,GAAG1B,SAAS,EAAEwB,SAAS,GAAGA,SAAS,CAACE,CAAC,GAAG1B,SAAS,CAAC;AAC5H;AAEA,SAASC,eAAe,EAAEY,oBAAoB,EAAEQ,mBAAmB,EAAE3B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}