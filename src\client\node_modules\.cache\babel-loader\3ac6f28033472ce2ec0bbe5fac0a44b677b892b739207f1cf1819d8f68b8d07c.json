{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Signup = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const password = watch('password');\n  const onSubmit = async data => {\n    try {\n      await axios.post('/api/auth/signup', data);\n      navigate('/check-email');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Signup failed:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Create an Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), serverError && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",\n            children: serverError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit(onSubmit),\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-400\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                ...register('username', {\n                  required: 'Username is required'\n                }),\n                className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n                placeholder: \"Choose a username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-red-500\",\n                children: errors.username.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-400\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                ...register('email', {\n                  required: 'Email is required',\n                  pattern: {\n                    value: /\\S+@\\S+\\.\\S+/,\n                    message: 'Email is invalid'\n                  }\n                }),\n                className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-red-500\",\n                children: errors.email.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-400\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                ...register('password', {\n                  required: 'Password is required',\n                  minLength: {\n                    value: 6,\n                    message: 'Password must be at least 6 characters'\n                  }\n                }),\n                className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n                placeholder: \"Create a password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-red-500\",\n                children: errors.password.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-400\",\n                children: \"Confirm Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                ...register('confirmPassword', {\n                  required: 'Please confirm your password',\n                  validate: value => value === password || 'Passwords do not match'\n                }),\n                className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n                placeholder: \"Confirm your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-red-500\",\n                children: errors.confirmPassword.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"terms\",\n                type: \"checkbox\",\n                ...register('terms', {\n                  required: 'You must agree to the terms'\n                }),\n                className: \"h-4 w-4 mt-1 text-blue-600 bg-blue-900 border-blue-800 rounded focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"terms\",\n                  className: \"font-medium text-gray-400\",\n                  children: [\"I agree to the \", /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    className: \"text-blue-400 hover:text-blue-300\",\n                    children: \"Terms and Conditions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this), errors.terms && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-500\",\n                  children: errors.terms.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 36\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",\n                children: \"Sign Up\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-sm text-center text-gray-400\",\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"font-medium text-blue-400 hover:text-blue-300\",\n              children: \"Log in\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold mb-4\",\n            children: \"Join BlazeTrade Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-gray-400\",\n            children: \"Access premium trading tools, real-time data, and a vibrant community. Your journey to smarter trading starts here.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"GRmW5oxwZKHJpdaax1xw+MFD8L0=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Signup", "_s", "register", "handleSubmit", "watch", "formState", "errors", "navigate", "serverError", "setServerError", "password", "onSubmit", "data", "post", "error", "response", "msg", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "placeholder", "username", "message", "pattern", "value", "email", "<PERSON><PERSON><PERSON><PERSON>", "validate", "confirmPassword", "id", "htmlFor", "to", "terms", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Signup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst Signup = () => {\n  const { register, handleSubmit, watch, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const password = watch('password');\n\n  const onSubmit = async (data) => {\n    try {\n      await axios.post('/api/auth/signup', data);\n      navigate('/check-email');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Signup failed:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Create an Account</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            \n            <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Username</label>\n                <input\n                  type=\"text\"\n                  {...register('username', { required: 'Username is required' })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Choose a username\"\n                />\n                {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Email</label>\n                <input\n                  type=\"email\"\n                  {...register('email', { required: 'Email is required', pattern: { value: /\\S+@\\S+\\.\\S+/, message: 'Email is invalid' } })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Enter your email\"\n                />\n                {errors.email && <p className=\"mt-2 text-sm text-red-500\">{errors.email.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n                <input\n                  type=\"password\"\n                  {...register('password', { required: 'Password is required', minLength: { value: 6, message: 'Password must be at least 6 characters' } })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Create a password\"\n                />\n                {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Confirm Password</label>\n                <input\n                  type=\"password\"\n                  {...register('confirmPassword', { \n                    required: 'Please confirm your password',\n                    validate: value => value === password || 'Passwords do not match'\n                  })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Confirm your password\"\n                />\n                {errors.confirmPassword && <p className=\"mt-2 text-sm text-red-500\">{errors.confirmPassword.message}</p>}\n              </div>\n\n              <div className=\"flex items-start\">\n                <input\n                  id=\"terms\"\n                  type=\"checkbox\"\n                  {...register('terms', { required: 'You must agree to the terms' })}\n                  className=\"h-4 w-4 mt-1 text-blue-600 bg-blue-900 border-blue-800 rounded focus:ring-blue-500\"\n                />\n                <div className=\"ml-3 text-sm\">\n                  <label htmlFor=\"terms\" className=\"font-medium text-gray-400\">\n                    I agree to the <Link to=\"/terms\" className=\"text-blue-400 hover:text-blue-300\">Terms and Conditions</Link>\n                  </label>\n                  {errors.terms && <p className=\"mt-1 text-sm text-red-500\">{errors.terms.message}</p>}\n                </div>\n              </div>\n\n              <div>\n                <button\n                  type=\"submit\"\n                  className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n                >\n                  Sign Up\n                </button>\n              </div>\n            </form>\n            \n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Already have an account?{' '}\n              <Link to=\"/login\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Log in\n              </Link>\n            </p>\n          </div>\n\n          {/* Info Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\">\n              <h2 className=\"text-2xl font-bold mb-4\">Join BlazeTrade Today</h2>\n              <p className=\"text-center text-gray-400\">Access premium trading tools, real-time data, and a vibrant community. Your journey to smarter trading starts here.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC1E,MAAMa,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMkB,QAAQ,GAAGN,KAAK,CAAC,UAAU,CAAC;EAElC,MAAMO,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMjB,KAAK,CAACkB,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;MAC1CL,QAAQ,CAAC,cAAc,CAAC;IAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACH,IAAI,CAACI,GAAG,EAAE;QAC7CP,cAAc,CAACK,KAAK,CAACC,QAAQ,CAACH,IAAI,CAACI,GAAG,CAAC;MACzC,CAAC,MAAM;QACLP,cAAc,CAAC,iDAAiD,CAAC;MACnE;MACAQ,OAAO,CAACH,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,oBACEf,OAAA;IAAKmB,SAAS,EAAC,mDAAmD;IAAAC,QAAA,gBAChEpB,OAAA,CAACN,MAAM;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVxB,OAAA;MAAKmB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DpB,OAAA;QAAKmB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAG/EpB,OAAA;UAAKmB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpB,OAAA;YAAImB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7Df,WAAW,iBAAIT,OAAA;YAAGmB,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAAEX;UAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEnHxB,OAAA;YAAMY,QAAQ,EAAER,YAAY,CAACQ,QAAQ,CAAE;YAACO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC3DpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAOmB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ExB,OAAA;gBACEyB,IAAI,EAAC,MAAM;gBAAA,GACPtB,QAAQ,CAAC,UAAU,EAAE;kBAAEuB,QAAQ,EAAE;gBAAuB,CAAC,CAAC;gBAC9DP,SAAS,EAAC,oIAAoI;gBAC9IQ,WAAW,EAAC;cAAmB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDjB,MAAM,CAACqB,QAAQ,iBAAI5B,OAAA;gBAAGmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEb,MAAM,CAACqB,QAAQ,CAACC;cAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAENxB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAOmB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxExB,OAAA;gBACEyB,IAAI,EAAC,OAAO;gBAAA,GACRtB,QAAQ,CAAC,OAAO,EAAE;kBAAEuB,QAAQ,EAAE,mBAAmB;kBAAEI,OAAO,EAAE;oBAAEC,KAAK,EAAE,cAAc;oBAAEF,OAAO,EAAE;kBAAmB;gBAAE,CAAC,CAAC;gBACzHV,SAAS,EAAC,oIAAoI;gBAC9IQ,WAAW,EAAC;cAAkB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EACDjB,MAAM,CAACyB,KAAK,iBAAIhC,OAAA;gBAAGmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEb,MAAM,CAACyB,KAAK,CAACH;cAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eAENxB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAOmB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ExB,OAAA;gBACEyB,IAAI,EAAC,UAAU;gBAAA,GACXtB,QAAQ,CAAC,UAAU,EAAE;kBAAEuB,QAAQ,EAAE,sBAAsB;kBAAEO,SAAS,EAAE;oBAAEF,KAAK,EAAE,CAAC;oBAAEF,OAAO,EAAE;kBAAyC;gBAAE,CAAC,CAAC;gBAC1IV,SAAS,EAAC,oIAAoI;gBAC9IQ,WAAW,EAAC;cAAmB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDjB,MAAM,CAACI,QAAQ,iBAAIX,OAAA;gBAAGmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEb,MAAM,CAACI,QAAQ,CAACkB;cAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAENxB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAOmB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFxB,OAAA;gBACEyB,IAAI,EAAC,UAAU;gBAAA,GACXtB,QAAQ,CAAC,iBAAiB,EAAE;kBAC9BuB,QAAQ,EAAE,8BAA8B;kBACxCQ,QAAQ,EAAEH,KAAK,IAAIA,KAAK,KAAKpB,QAAQ,IAAI;gBAC3C,CAAC,CAAC;gBACFQ,SAAS,EAAC,oIAAoI;gBAC9IQ,WAAW,EAAC;cAAuB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,EACDjB,MAAM,CAAC4B,eAAe,iBAAInC,OAAA;gBAAGmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEb,MAAM,CAAC4B,eAAe,CAACN;cAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eAENxB,OAAA;cAAKmB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BpB,OAAA;gBACEoC,EAAE,EAAC,OAAO;gBACVX,IAAI,EAAC,UAAU;gBAAA,GACXtB,QAAQ,CAAC,OAAO,EAAE;kBAAEuB,QAAQ,EAAE;gBAA8B,CAAC,CAAC;gBAClEP,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACFxB,OAAA;gBAAKmB,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BpB,OAAA;kBAAOqC,OAAO,EAAC,OAAO;kBAAClB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAC,iBAC5C,eAAApB,OAAA,CAACH,IAAI;oBAACyC,EAAE,EAAC,QAAQ;oBAACnB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,EACPjB,MAAM,CAACgC,KAAK,iBAAIvC,OAAA;kBAAGmB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEb,MAAM,CAACgC,KAAK,CAACV;gBAAO;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxB,OAAA;cAAAoB,QAAA,eACEpB,OAAA;gBACEyB,IAAI,EAAC,QAAQ;gBACbN,SAAS,EAAC,2MAA2M;gBAAAC,QAAA,EACtN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPxB,OAAA;YAAGmB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,0BAC5B,EAAC,GAAG,eAC5BpB,OAAA,CAACH,IAAI;cAACyC,EAAE,EAAC,QAAQ;cAACnB,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNxB,OAAA;UAAKmB,SAAS,EAAC,4GAA4G;UAAAC,QAAA,gBACvHpB,OAAA;YAAImB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClExB,OAAA;YAAGmB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAmH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAzHID,MAAM;EAAA,QACuDN,OAAO,EACvDG,WAAW;AAAA;AAAA0C,EAAA,GAFxBvC,MAAM;AA2HZ,eAAeA,MAAM;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}