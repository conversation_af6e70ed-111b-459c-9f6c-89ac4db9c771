{"ast": null, "code": "const instanceOfAny = (object, constructors) => constructors.some(c => object instanceof c);\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n  return idbProxyableTypes || (idbProxyableTypes = [IDBDatabase, IDBObjectStore, IDBIndex, IDBCursor, IDBTransaction]);\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n  return cursorAdvanceMethods || (cursorAdvanceMethods = [IDBCursor.prototype.advance, IDBCursor.prototype.continue, IDBCursor.prototype.continuePrimaryKey]);\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n  const promise = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      request.removeEventListener('success', success);\n      request.removeEventListener('error', error);\n    };\n    const success = () => {\n      resolve(wrap(request.result));\n      unlisten();\n    };\n    const error = () => {\n      reject(request.error);\n      unlisten();\n    };\n    request.addEventListener('success', success);\n    request.addEventListener('error', error);\n  });\n  promise.then(value => {\n    // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n    // (see wrapFunction).\n    if (value instanceof IDBCursor) {\n      cursorRequestMap.set(value, request);\n    }\n    // Catching to avoid \"Uncaught Promise exceptions\"\n  }).catch(() => {});\n  // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n  // is because we create many promises from a single IDBRequest.\n  reverseTransformCache.set(promise, request);\n  return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n  // Early bail if we've already created a done promise for this transaction.\n  if (transactionDoneMap.has(tx)) return;\n  const done = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      tx.removeEventListener('complete', complete);\n      tx.removeEventListener('error', error);\n      tx.removeEventListener('abort', error);\n    };\n    const complete = () => {\n      resolve();\n      unlisten();\n    };\n    const error = () => {\n      reject(tx.error || new DOMException('AbortError', 'AbortError'));\n      unlisten();\n    };\n    tx.addEventListener('complete', complete);\n    tx.addEventListener('error', error);\n    tx.addEventListener('abort', error);\n  });\n  // Cache it for later retrieval.\n  transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n  get(target, prop, receiver) {\n    if (target instanceof IDBTransaction) {\n      // Special handling for transaction.done.\n      if (prop === 'done') return transactionDoneMap.get(target);\n      // Polyfill for objectStoreNames because of Edge.\n      if (prop === 'objectStoreNames') {\n        return target.objectStoreNames || transactionStoreNamesMap.get(target);\n      }\n      // Make tx.store return the only store in the transaction, or undefined if there are many.\n      if (prop === 'store') {\n        return receiver.objectStoreNames[1] ? undefined : receiver.objectStore(receiver.objectStoreNames[0]);\n      }\n    }\n    // Else transform whatever we get back.\n    return wrap(target[prop]);\n  },\n  set(target, prop, value) {\n    target[prop] = value;\n    return true;\n  },\n  has(target, prop) {\n    if (target instanceof IDBTransaction && (prop === 'done' || prop === 'store')) {\n      return true;\n    }\n    return prop in target;\n  }\n};\nfunction replaceTraps(callback) {\n  idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n  // Due to expected object equality (which is enforced by the caching in `wrap`), we\n  // only create one new func per func.\n  // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n  if (func === IDBDatabase.prototype.transaction && !('objectStoreNames' in IDBTransaction.prototype)) {\n    return function (storeNames) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      const tx = func.call(unwrap(this), storeNames, ...args);\n      transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n      return wrap(tx);\n    };\n  }\n  // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n  // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n  // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n  // with real promises, so each advance methods returns a new promise for the cursor object, or\n  // undefined if the end of the cursor has been reached.\n  if (getCursorAdvanceMethods().includes(func)) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n      // the original object.\n      func.apply(unwrap(this), args);\n      return wrap(cursorRequestMap.get(this));\n    };\n  }\n  return function () {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n    // the original object.\n    return wrap(func.apply(unwrap(this), args));\n  };\n}\nfunction transformCachableValue(value) {\n  if (typeof value === 'function') return wrapFunction(value);\n  // This doesn't return, it just creates a 'done' promise for the transaction,\n  // which is later returned for transaction.done (see idbObjectHandler).\n  if (value instanceof IDBTransaction) cacheDonePromiseForTransaction(value);\n  if (instanceOfAny(value, getIdbProxyableTypes())) return new Proxy(value, idbProxyTraps);\n  // Return the same value back if we're not going to transform it.\n  return value;\n}\nfunction wrap(value) {\n  // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n  // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n  if (value instanceof IDBRequest) return promisifyRequest(value);\n  // If we've already transformed this value before, reuse the transformed value.\n  // This is faster, but it also provides object equality.\n  if (transformCache.has(value)) return transformCache.get(value);\n  const newValue = transformCachableValue(value);\n  // Not all types are transformed.\n  // These may be primitive types, so they can't be WeakMap keys.\n  if (newValue !== value) {\n    transformCache.set(value, newValue);\n    reverseTransformCache.set(newValue, value);\n  }\n  return newValue;\n}\nconst unwrap = value => reverseTransformCache.get(value);\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };", "map": {"version": 3, "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "request", "promise", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "success", "error", "wrap", "result", "addEventListener", "then", "value", "set", "catch", "cacheDonePromiseForTransaction", "tx", "has", "done", "complete", "DOMException", "idbProxyTraps", "get", "target", "prop", "receiver", "objectStoreNames", "undefined", "objectStore", "replaceTraps", "callback", "wrapFunction", "func", "transaction", "storeNames", "_len", "arguments", "length", "args", "Array", "_key", "call", "unwrap", "sort", "includes", "_len2", "_key2", "apply", "_len3", "_key3", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "a", "i", "r", "u", "w"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/idb/build/wrap-idb-value.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGA,CAACC,MAAM,EAAEC,YAAY,KAAKA,YAAY,CAACC,IAAI,CAAEC,CAAC,IAAKH,MAAM,YAAYG,CAAC,CAAC;AAE7F,IAAIC,iBAAiB;AACrB,IAAIC,oBAAoB;AACxB;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,OAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,cAAc,CACjB,CAAC;AACV;AACA;AACA,SAASC,uBAAuBA,CAAA,EAAG;EAC/B,OAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAS,CAACC,OAAO,EAC3BJ,SAAS,CAACG,SAAS,CAACE,QAAQ,EAC5BL,SAAS,CAACG,SAAS,CAACG,kBAAkB,CACzC,CAAC;AACV;AACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACtC,MAAMC,kBAAkB,GAAG,IAAID,OAAO,CAAC,CAAC;AACxC,MAAME,wBAAwB,GAAG,IAAIF,OAAO,CAAC,CAAC;AAC9C,MAAMG,cAAc,GAAG,IAAIH,OAAO,CAAC,CAAC;AACpC,MAAMI,qBAAqB,GAAG,IAAIJ,OAAO,CAAC,CAAC;AAC3C,SAASK,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC7C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBL,OAAO,CAACM,mBAAmB,CAAC,SAAS,EAAEC,OAAO,CAAC;MAC/CP,OAAO,CAACM,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC;IAC/C,CAAC;IACD,MAAMD,OAAO,GAAGA,CAAA,KAAM;MAClBJ,OAAO,CAACM,IAAI,CAACT,OAAO,CAACU,MAAM,CAAC,CAAC;MAC7BL,QAAQ,CAAC,CAAC;IACd,CAAC;IACD,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAChBJ,MAAM,CAACJ,OAAO,CAACQ,KAAK,CAAC;MACrBH,QAAQ,CAAC,CAAC;IACd,CAAC;IACDL,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAEJ,OAAO,CAAC;IAC5CP,OAAO,CAACW,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC;EAC5C,CAAC,CAAC;EACFP,OAAO,CACFW,IAAI,CAAEC,KAAK,IAAK;IACjB;IACA;IACA,IAAIA,KAAK,YAAY3B,SAAS,EAAE;MAC5BO,gBAAgB,CAACqB,GAAG,CAACD,KAAK,EAAEb,OAAO,CAAC;IACxC;IACA;EACJ,CAAC,CAAC,CACGe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EACrB;EACA;EACAjB,qBAAqB,CAACgB,GAAG,CAACb,OAAO,EAAED,OAAO,CAAC;EAC3C,OAAOC,OAAO;AAClB;AACA,SAASe,8BAA8BA,CAACC,EAAE,EAAE;EACxC;EACA,IAAItB,kBAAkB,CAACuB,GAAG,CAACD,EAAE,CAAC,EAC1B;EACJ,MAAME,IAAI,GAAG,IAAIjB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC1C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBY,EAAE,CAACX,mBAAmB,CAAC,UAAU,EAAEc,QAAQ,CAAC;MAC5CH,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC;MACtCS,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC;IAC1C,CAAC;IACD,MAAMY,QAAQ,GAAGA,CAAA,KAAM;MACnBjB,OAAO,CAAC,CAAC;MACTE,QAAQ,CAAC,CAAC;IACd,CAAC;IACD,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAChBJ,MAAM,CAACa,EAAE,CAACT,KAAK,IAAI,IAAIa,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;MAChEhB,QAAQ,CAAC,CAAC;IACd,CAAC;IACDY,EAAE,CAACN,gBAAgB,CAAC,UAAU,EAAES,QAAQ,CAAC;IACzCH,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC;IACnCS,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC;EACvC,CAAC,CAAC;EACF;EACAb,kBAAkB,CAACmB,GAAG,CAACG,EAAE,EAAEE,IAAI,CAAC;AACpC;AACA,IAAIG,aAAa,GAAG;EAChBC,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;IACxB,IAAIF,MAAM,YAAYrC,cAAc,EAAE;MAClC;MACA,IAAIsC,IAAI,KAAK,MAAM,EACf,OAAO9B,kBAAkB,CAAC4B,GAAG,CAACC,MAAM,CAAC;MACzC;MACA,IAAIC,IAAI,KAAK,kBAAkB,EAAE;QAC7B,OAAOD,MAAM,CAACG,gBAAgB,IAAI/B,wBAAwB,CAAC2B,GAAG,CAACC,MAAM,CAAC;MAC1E;MACA;MACA,IAAIC,IAAI,KAAK,OAAO,EAAE;QAClB,OAAOC,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,GAC7BC,SAAS,GACTF,QAAQ,CAACG,WAAW,CAACH,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC5D;IACJ;IACA;IACA,OAAOlB,IAAI,CAACe,MAAM,CAACC,IAAI,CAAC,CAAC;EAC7B,CAAC;EACDX,GAAGA,CAACU,MAAM,EAAEC,IAAI,EAAEZ,KAAK,EAAE;IACrBW,MAAM,CAACC,IAAI,CAAC,GAAGZ,KAAK;IACpB,OAAO,IAAI;EACf,CAAC;EACDK,GAAGA,CAACM,MAAM,EAAEC,IAAI,EAAE;IACd,IAAID,MAAM,YAAYrC,cAAc,KAC/BsC,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACA,OAAOA,IAAI,IAAID,MAAM;EACzB;AACJ,CAAC;AACD,SAASM,YAAYA,CAACC,QAAQ,EAAE;EAC5BT,aAAa,GAAGS,QAAQ,CAACT,aAAa,CAAC;AAC3C;AACA,SAASU,YAAYA,CAACC,IAAI,EAAE;EACxB;EACA;EACA;EACA,IAAIA,IAAI,KAAKlD,WAAW,CAACM,SAAS,CAAC6C,WAAW,IAC1C,EAAE,kBAAkB,IAAI/C,cAAc,CAACE,SAAS,CAAC,EAAE;IACnD,OAAO,UAAU8C,UAAU,EAAW;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAChC,MAAMxB,EAAE,GAAGgB,IAAI,CAACS,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,EAAER,UAAU,EAAE,GAAGI,IAAI,CAAC;MACvD3C,wBAAwB,CAACkB,GAAG,CAACG,EAAE,EAAEkB,UAAU,CAACS,IAAI,GAAGT,UAAU,CAACS,IAAI,CAAC,CAAC,GAAG,CAACT,UAAU,CAAC,CAAC;MACpF,OAAO1B,IAAI,CAACQ,EAAE,CAAC;IACnB,CAAC;EACL;EACA;EACA;EACA;EACA;EACA;EACA,IAAI7B,uBAAuB,CAAC,CAAC,CAACyD,QAAQ,CAACZ,IAAI,CAAC,EAAE;IAC1C,OAAO,YAAmB;MAAA,SAAAa,KAAA,GAAAT,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAM,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJR,IAAI,CAAAQ,KAAA,IAAAV,SAAA,CAAAU,KAAA;MAAA;MACpB;MACA;MACAd,IAAI,CAACe,KAAK,CAACL,MAAM,CAAC,IAAI,CAAC,EAAEJ,IAAI,CAAC;MAC9B,OAAO9B,IAAI,CAAChB,gBAAgB,CAAC8B,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;EACL;EACA,OAAO,YAAmB;IAAA,SAAA0B,KAAA,GAAAZ,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJX,IAAI,CAAAW,KAAA,IAAAb,SAAA,CAAAa,KAAA;IAAA;IACpB;IACA;IACA,OAAOzC,IAAI,CAACwB,IAAI,CAACe,KAAK,CAACL,MAAM,CAAC,IAAI,CAAC,EAAEJ,IAAI,CAAC,CAAC;EAC/C,CAAC;AACL;AACA,SAASY,sBAAsBA,CAACtC,KAAK,EAAE;EACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAOmB,YAAY,CAACnB,KAAK,CAAC;EAC9B;EACA;EACA,IAAIA,KAAK,YAAY1B,cAAc,EAC/B6B,8BAA8B,CAACH,KAAK,CAAC;EACzC,IAAItC,aAAa,CAACsC,KAAK,EAAE/B,oBAAoB,CAAC,CAAC,CAAC,EAC5C,OAAO,IAAIsE,KAAK,CAACvC,KAAK,EAAES,aAAa,CAAC;EAC1C;EACA,OAAOT,KAAK;AAChB;AACA,SAASJ,IAAIA,CAACI,KAAK,EAAE;EACjB;EACA;EACA,IAAIA,KAAK,YAAYwC,UAAU,EAC3B,OAAOtD,gBAAgB,CAACc,KAAK,CAAC;EAClC;EACA;EACA,IAAIhB,cAAc,CAACqB,GAAG,CAACL,KAAK,CAAC,EACzB,OAAOhB,cAAc,CAAC0B,GAAG,CAACV,KAAK,CAAC;EACpC,MAAMyC,QAAQ,GAAGH,sBAAsB,CAACtC,KAAK,CAAC;EAC9C;EACA;EACA,IAAIyC,QAAQ,KAAKzC,KAAK,EAAE;IACpBhB,cAAc,CAACiB,GAAG,CAACD,KAAK,EAAEyC,QAAQ,CAAC;IACnCxD,qBAAqB,CAACgB,GAAG,CAACwC,QAAQ,EAAEzC,KAAK,CAAC;EAC9C;EACA,OAAOyC,QAAQ;AACnB;AACA,MAAMX,MAAM,GAAI9B,KAAK,IAAKf,qBAAqB,CAACyB,GAAG,CAACV,KAAK,CAAC;AAE1D,SAASf,qBAAqB,IAAIyD,CAAC,EAAEhF,aAAa,IAAIiF,CAAC,EAAE1B,YAAY,IAAI2B,CAAC,EAAEd,MAAM,IAAIe,CAAC,EAAEjD,IAAI,IAAIkD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}