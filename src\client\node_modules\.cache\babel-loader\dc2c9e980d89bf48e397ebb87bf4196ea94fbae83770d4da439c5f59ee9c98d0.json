{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst Client = require('./classes/client');\n\n//Export singleton instance\nmodule.exports = new Client();", "map": {"version": 3, "names": ["Client", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/client/src/client.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst Client = require('./classes/client');\n\n//Export singleton instance\nmodule.exports = new Client();\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,MAAM,GAAGC,OAAO,CAAC,kBAAkB,CAAC;;AAE1C;AACAC,MAAM,CAACC,OAAO,GAAG,IAAIH,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}