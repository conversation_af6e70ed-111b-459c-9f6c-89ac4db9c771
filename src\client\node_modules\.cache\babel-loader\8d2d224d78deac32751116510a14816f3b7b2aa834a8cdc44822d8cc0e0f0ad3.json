{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaInstagram, FaLinkedin, FaEnvelope, FaPhone } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto py-12 px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: blazeTradeLogo,\n              alt: \"BlazeTrade Logo\",\n              className: \"h-8 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold\",\n              children: \"BlazeTrade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm max-w-xs\",\n            children: \"Securely trade and manage your cryptocurrency assets with confidence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaTwitter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 33\n              }, this),\n              href: \"#\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaFacebook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 33\n              }, this),\n              href: \"#\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaInstagram, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 33\n              }, this),\n              href: \"#\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 33\n              }, this),\n              href: \"#\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/\",\n              label: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/about\",\n              label: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/services\",\n              label: \"Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/contact\",\n              label: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Legal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/terms-of-service\",\n              label: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/privacy-policy\",\n              label: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n              to: \"/disclaimer\",\n              label: \"Disclaimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                className: \"hover:text-blue-400\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+1234567890\",\n                className: \"hover:text-blue-400\",\n                children: \"+1 (234) 567-890\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"BlazeTrade App\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 p-2 bg-gray-800 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://i.imgur.com/O12wJvA.png\",\n              alt: \"QR Code\",\n              className: \"w-16 h-16 rounded-md bg-white p-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold\",\n                children: \"Scan to Download\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: \"iOS & Android\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-800 mt-10 pt-6 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"\\xA9 \", currentYear, \" BlazeTrade. All Rights Reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nconst SocialIcon = ({\n  icon,\n  href\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"a\", {\n    href: href,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: \"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\",\n    children: icon\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SocialIcon;\nconst FooterLink = ({\n  to,\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"li\", {\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: to,\n      className: \"text-gray-300 hover:text-white transition-colors duration-300\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_c3 = FooterLink;\nexport default Footer;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c2, \"SocialIcon\");\n$RefreshReg$(_c3, \"FooterLink\");", "map": {"version": 3, "names": ["React", "Link", "FaTwitter", "FaFacebook", "FaInstagram", "FaLinkedin", "FaEnvelope", "FaPhone", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SocialIcon", "icon", "href", "FooterLink", "to", "label", "_c", "target", "rel", "_c2", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaInstagram, FaLinkedin, FaEnvelope, FaPhone } from 'react-icons/fa';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          \n          {/* Company Info */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-8 object-contain\" />\n              <span className=\"text-xl font-bold\">BlazeTrade</span>\n            </div>\n            <p className=\"text-gray-400 text-sm max-w-xs\">\n              Securely trade and manage your cryptocurrency assets with confidence.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <SocialIcon icon={<FaTwitter />} href=\"#\" />\n              <SocialIcon icon={<FaFacebook />} href=\"#\" />\n              <SocialIcon icon={<FaInstagram />} href=\"#\" />\n              <SocialIcon icon={<FaLinkedin />} href=\"#\" />\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/\" label=\"Home\" />\n              <FooterLink to=\"/about\" label=\"About Us\" />\n              <FooterLink to=\"/services\" label=\"Services\" />\n              <FooterLink to=\"/contact\" label=\"Contact\" />\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <FooterLink to=\"/terms-of-service\" label=\"Terms of Service\" />\n              <FooterLink to=\"/privacy-policy\" label=\"Privacy Policy\" />\n              <FooterLink to=\"/disclaimer\" label=\"Disclaimer\" />\n            </ul>\n          </div>\n\n          {/* Contact Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Us</h3>\n            <ul className=\"space-y-2 text-gray-400\">\n              <li className=\"flex items-center\">\n                <FaEnvelope className=\"mr-2\" />\n                <a href=\"mailto:<EMAIL>\" className=\"hover:text-blue-400\"><EMAIL></a>\n              </li>\n              <li className=\"flex items-center\">\n                <FaPhone className=\"mr-2\" />\n                <a href=\"tel:+1234567890\" className=\"hover:text-blue-400\">+1 (234) 567-890</a>\n              </li>\n            </ul>\n          </div>\n\n          {/* App QR Code */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">BlazeTrade App</h3>\n            <div className=\"flex items-center space-x-4 p-2 bg-gray-800 rounded-lg\">\n              <img src=\"https://i.imgur.com/O12wJvA.png\" alt=\"QR Code\" className=\"w-16 h-16 rounded-md bg-white p-1\"/>\n              <div>\n                <p className=\"font-semibold\">Scan to Download</p>\n                <p className=\"text-xs text-gray-400\">iOS & Android</p>\n              </div>\n            </div>\n          </div>\n\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-10 pt-6 text-center\">\n          <p className=\"text-sm text-gray-500\">\n            &copy; {currentYear} BlazeTrade. All Rights Reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nconst SocialIcon = ({ icon, href }) => {\n  return (\n    <a \n      href={href} \n      target=\"_blank\" \n      rel=\"noopener noreferrer\"\n      className=\"w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center hover:bg-accent transition-colors duration-300\"\n    >\n      {icon}\n    </a>\n  );\n};\n\nconst FooterLink = ({ to, label }) => {\n  return (\n    <li>\n      <Link \n        to={to} \n        className=\"text-gray-300 hover:text-white transition-colors duration-300\"\n      >\n        {label}\n      </Link>\n    </li>\n  );\n};\n\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AACpG,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACxCN,OAAA;MAAKK,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DN,OAAA;QAAKK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBAGnEN,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BN,OAAA;YAAKK,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CN,OAAA;cAAKO,GAAG,EAAET,cAAe;cAACU,GAAG,EAAC,iBAAiB;cAACH,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFZ,OAAA;cAAMK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNZ,OAAA;YAAGK,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAE9C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAKK,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCN,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACR,SAAS;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CZ,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACP,UAAU;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CZ,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACN,WAAW;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CZ,OAAA,CAACa,UAAU;cAACC,IAAI,eAAEd,OAAA,CAACL,UAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,IAAI,EAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,GAAG;cAACC,KAAK,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,QAAQ;cAACC,KAAK,EAAC;YAAU;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,WAAW;cAACC,KAAK,EAAC;YAAU;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,UAAU;cAACC,KAAK,EAAC;YAAS;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDZ,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,mBAAmB;cAACC,KAAK,EAAC;YAAkB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,iBAAiB;cAACC,KAAK,EAAC;YAAgB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DZ,OAAA,CAACgB,UAAU;cAACC,EAAE,EAAC,aAAa;cAACC,KAAK,EAAC;YAAY;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DZ,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCN,OAAA;cAAIK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BN,OAAA,CAACJ,UAAU;gBAACS,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BZ,OAAA;gBAAGe,IAAI,EAAC,+BAA+B;gBAACV,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACLZ,OAAA;cAAIK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BN,OAAA,CAACH,OAAO;gBAACQ,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BZ,OAAA;gBAAGe,IAAI,EAAC,iBAAiB;gBAACV,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DZ,OAAA;YAAKK,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACrEN,OAAA;cAAKO,GAAG,EAAC,iCAAiC;cAACC,GAAG,EAAC,SAAS;cAACH,SAAS,EAAC;YAAmC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACxGZ,OAAA;cAAAM,QAAA,gBACEN,OAAA;gBAAGK,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjDZ,OAAA;gBAAGK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CAAC,eAENZ,OAAA;QAAKK,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DN,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OAC5B,EAACJ,WAAW,EAAC,mCACtB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACO,EAAA,GAnFIlB,MAAM;AAqFZ,MAAMY,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EACrC,oBACEf,OAAA;IACEe,IAAI,EAAEA,IAAK;IACXK,MAAM,EAAC,QAAQ;IACfC,GAAG,EAAC,qBAAqB;IACzBhB,SAAS,EAAC,kHAAkH;IAAAC,QAAA,EAE3HQ;EAAI;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAER,CAAC;AAACU,GAAA,GAXIT,UAAU;AAahB,MAAMG,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAM,CAAC,KAAK;EACpC,oBACElB,OAAA;IAAAM,QAAA,eACEN,OAAA,CAACT,IAAI;MACH0B,EAAE,EAAEA,EAAG;MACPZ,SAAS,EAAC,+DAA+D;MAAAC,QAAA,EAExEY;IAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAET,CAAC;AAACW,GAAA,GAXIP,UAAU;AAahB,eAAef,MAAM;AAAC,IAAAkB,EAAA,EAAAG,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}