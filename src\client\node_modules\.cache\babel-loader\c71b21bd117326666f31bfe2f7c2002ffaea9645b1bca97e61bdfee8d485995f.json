{"ast": null, "code": "import React,{useState}from'react';import Navbar from'../components/Navbar';import{useForm}from'react-hook-form';import axios from'axios';import{Link,useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const{register,handleSubmit,formState:{errors}}=useForm();const navigate=useNavigate();const[serverError,setServerError]=useState('');const onSubmit=async data=>{try{const response=await axios.post('/api/auth/login',data);localStorage.setItem('token',response.data.token);navigate('/dashboard');}catch(error){if(error.response&&error.response.data.msg){setServerError(error.response.data.msg);}else{setServerError('An unexpected error occurred. Please try again.');}console.error('<PERSON><PERSON> failed:',error);}};const renderForm=()=>{return/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit(onSubmit),className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",...register('username',{required:'Username is required'}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Enter your username\"}),errors.username&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.username.message})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Password\"}),/*#__PURE__*/_jsx(Link,{to:\"/forgot-password\",className:\"text-sm text-blue-400 hover:text-blue-300\",children:\"Forgot Password?\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",...register('password',{required:'Password is required'}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Enter password\"}),errors.password&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.password.message})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",children:\"Log In\"})})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-900 text-white flex flex-col\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold mb-4\",children:\"Log In\"}),serverError&&/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",children:serverError}),renderForm(),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-6 text-sm text-center text-gray-400\",children:[\"Don't have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/signup\",className:\"font-medium text-blue-400 hover:text-blue-300\",children:\"Sign up\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex flex-col items-center justify-center\",children:/*#__PURE__*/_jsx(\"img\",{src:\"https://i.ibb.co/wYyBf9g/blazetrade-logo.png\",alt:\"BlazeTrade Logo\",className:\"w-48 h-48\"})})]})})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "axios", "Link", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "register", "handleSubmit", "formState", "errors", "navigate", "serverError", "setServerError", "onSubmit", "data", "response", "post", "localStorage", "setItem", "token", "error", "msg", "console", "renderForm", "className", "children", "type", "required", "placeholder", "username", "message", "to", "password", "src", "alt"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n\n  const onSubmit = async (data) => {\n    try {\n      const response = await axios.post('/api/auth/login', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Login failed:', error);\n    }\n  };\n\n  const renderForm = () => {\n    return (\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Username</label>\n          <input\n            type=\"text\"\n            {...register('username', { required: 'Username is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter your username\"\n          />\n          {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n        </div>\n        <div>\n          <div className=\"flex justify-between items-center\">\n            <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n            <Link to=\"/forgot-password\" className=\"text-sm text-blue-400 hover:text-blue-300\">Forgot Password?</Link>\n          </div>\n          <input\n            type=\"password\"\n            {...register('password', { required: 'Password is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter password\"\n          />\n          {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n        </div>\n        <div>\n          <button\n            type=\"submit\"\n            className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n          >\n            Log In\n          </button>\n        </div>\n      </form>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Log In</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            {renderForm()}\n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Don't have an account?{' '}\n              <Link to=\"/signup\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Sign up\n              </Link>\n            </p>\n          </div>\n\n          {/* Logo Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center\">\n              <img src=\"https://i.ibb.co/wYyBf9g/blazetrade-logo.png\" alt=\"BlazeTrade Logo\" className=\"w-48 h-48\"/>\n          </div>\n        </div>\n\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,OAASC,OAAO,KAAQ,iBAAiB,CACzC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,SAAS,CAAE,CAAEC,MAAO,CAAE,CAAC,CAAGZ,OAAO,CAAC,CAAC,CACnE,KAAM,CAAAa,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACW,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAAkB,QAAQ,CAAG,KAAO,CAAAC,IAAI,EAAK,CAC/B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjB,KAAK,CAACkB,IAAI,CAAC,iBAAiB,CAAEF,IAAI,CAAC,CAC1DG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAEH,QAAQ,CAACD,IAAI,CAACK,KAAK,CAAC,CAClDT,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOU,KAAK,CAAE,CACd,GAAIA,KAAK,CAACL,QAAQ,EAAIK,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,CAAE,CAC7CT,cAAc,CAACQ,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,CAAC,CACzC,CAAC,IAAM,CACLT,cAAc,CAAC,iDAAiD,CAAC,CACnE,CACAU,OAAO,CAACF,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACEnB,KAAA,SAAMS,QAAQ,CAAEN,YAAY,CAACM,QAAQ,CAAE,CAACW,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC3DrB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,UAAOsB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EvB,IAAA,UACEwB,IAAI,CAAC,MAAM,IACPpB,QAAQ,CAAC,UAAU,CAAE,CAAEqB,QAAQ,CAAE,sBAAuB,CAAC,CAAC,CAC9DH,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,qBAAqB,CAClC,CAAC,CACDnB,MAAM,CAACoB,QAAQ,eAAI3B,IAAA,MAAGsB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEhB,MAAM,CAACoB,QAAQ,CAACC,OAAO,CAAI,CAAC,EACvF,CAAC,cACN1B,KAAA,QAAAqB,QAAA,eACErB,KAAA,QAAKoB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvB,IAAA,UAAOsB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EvB,IAAA,CAACH,IAAI,EAACgC,EAAE,CAAC,kBAAkB,CAACP,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACtG,CAAC,cACNvB,IAAA,UACEwB,IAAI,CAAC,UAAU,IACXpB,QAAQ,CAAC,UAAU,CAAE,CAAEqB,QAAQ,CAAE,sBAAuB,CAAC,CAAC,CAC9DH,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,gBAAgB,CAC7B,CAAC,CACDnB,MAAM,CAACuB,QAAQ,eAAI9B,IAAA,MAAGsB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEhB,MAAM,CAACuB,QAAQ,CAACF,OAAO,CAAI,CAAC,EACvF,CAAC,cACN5B,IAAA,QAAAuB,QAAA,cACEvB,IAAA,WACEwB,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,2MAA2M,CAAAC,QAAA,CACtN,QAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,CAEX,CAAC,CAED,mBACErB,KAAA,QAAKoB,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEvB,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,QAAKsB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DrB,KAAA,QAAKoB,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAG/ErB,KAAA,QAAKoB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BvB,IAAA,OAAIsB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,CAClDd,WAAW,eAAIT,IAAA,MAAGsB,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAEd,WAAW,CAAI,CAAC,CAClHY,UAAU,CAAC,CAAC,cACbnB,KAAA,MAAGoB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,wBAC9B,CAAC,GAAG,cAC1BvB,IAAA,CAACH,IAAI,EAACgC,EAAE,CAAC,SAAS,CAACP,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,SAE7E,CAAM,CAAC,EACN,CAAC,EACD,CAAC,cAGNvB,IAAA,QAAKsB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAChEvB,IAAA,QAAK+B,GAAG,CAAC,8CAA8C,CAACC,GAAG,CAAC,iBAAiB,CAACV,SAAS,CAAC,WAAW,CAAC,CAAC,CACpG,CAAC,EACH,CAAC,CAEH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}