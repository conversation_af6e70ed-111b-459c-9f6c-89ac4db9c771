{"ast": null, "code": "import React,{useState}from'react';import Navbar from'../components/Navbar';import{useForm}from'react-hook-form';import{auth}from'../firebase';import{createUserWithEmailAndPassword,updateProfile}from'firebase/auth';import{Link,useNavigate}from'react-router-dom';import{sendVerificationEmail}from'../utils/emailService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Signup=()=>{const{register,handleSubmit,watch,formState:{errors}}=useForm();const navigate=useNavigate();const[serverError,setServerError]=useState('');const password=watch('password');const onSubmit=async data=>{setServerError('');// Clear previous errors\ntry{// Create the user in Firebase Authentication\nconst userCredential=await createUserWithEmailAndPassword(auth,data.email,data.password);const user=userCredential.user;// Update the user's profile with their username\nawait updateProfile(user,{displayName:data.username});// Generate verification link manually\nconst actionCodeSettings={url:`${window.location.origin}/login`,handleCodeInApp:true};// Get the verification link\nconst verificationLink=await auth.currentUser.getIdToken().then(()=>auth.currentUser.getIdToken(true)).then(idToken=>{return`https://${auth.app.options.authDomain}/__/auth/action?mode=verifyEmail&oobCode=${idToken}&apiKey=${auth.app.options.apiKey}&continueUrl=${encodeURIComponent(actionCodeSettings.url)}`;});// Send verification email using SendGrid\nawait sendVerificationEmail(user.email,data.username,verificationLink);// Sign out the user until they verify their email\nawait auth.signOut();// Navigate to check-email page\nnavigate('/check-email',{state:{email:user.email}});}catch(error){let friendlyMessage='An unexpected error occurred. Please try again.';// Provide user-friendly error messages\nswitch(error.code){case'auth/email-already-in-use':friendlyMessage='This email address is already in use by another account.';break;case'auth/invalid-email':friendlyMessage='The email address you entered is not valid.';break;case'auth/weak-password':friendlyMessage='The password is too weak. Please use at least 6 characters.';break;default:console.error('Firebase Signup Error:',error);}setServerError(friendlyMessage);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-900 text-white flex flex-col\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold mb-4\",children:\"Create an Account\"}),serverError&&/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",children:serverError}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit(onSubmit),className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",...register('username',{required:'Username is required'}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Choose a username\"}),errors.username&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.username.message})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",...register('email',{required:'Email is required',pattern:{value:/\\S+@\\S+\\.\\S+/,message:'Email is invalid'}}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Enter your email\"}),errors.email&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.email.message})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",...register('password',{required:'Password is required',minLength:{value:6,message:'Password must be at least 6 characters'}}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Create a password\"}),errors.password&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.password.message})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",...register('confirmPassword',{required:'Please confirm your password',validate:value=>value===password||'Passwords do not match'}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Confirm your password\"}),errors.confirmPassword&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.confirmPassword.message})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"terms\",type:\"checkbox\",...register('terms',{required:'You must agree to the terms'}),className:\"h-4 w-4 mt-1 text-blue-600 bg-blue-900 border-blue-800 rounded focus:ring-blue-500\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3 text-sm\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"terms\",className:\"font-medium text-gray-400\",children:[\"I agree to the \",/*#__PURE__*/_jsx(Link,{to:\"/terms\",className:\"text-blue-400 hover:text-blue-300\",children:\"Terms and Conditions\"})]}),errors.terms&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-500\",children:errors.terms.message})]})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",children:\"Sign Up\"})})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-6 text-sm text-center text-gray-400\",children:[\"Already have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"font-medium text-blue-400 hover:text-blue-300\",children:\"Log in\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-4\",children:\"Join BlazeTrade Today\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-center text-gray-400\",children:\"Access premium trading tools, real-time data, and a vibrant community. Your journey to smarter trading starts here.\"})]})]})})]});};export default Signup;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "auth", "createUserWithEmailAndPassword", "updateProfile", "Link", "useNavigate", "sendVerificationEmail", "jsx", "_jsx", "jsxs", "_jsxs", "Signup", "register", "handleSubmit", "watch", "formState", "errors", "navigate", "serverError", "setServerError", "password", "onSubmit", "data", "userCredential", "email", "user", "displayName", "username", "actionCodeSettings", "url", "window", "location", "origin", "handleCodeInApp", "verificationLink", "currentUser", "getIdToken", "then", "idToken", "app", "options", "authDomain", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "signOut", "state", "error", "friendlyMessage", "code", "console", "className", "children", "type", "required", "placeholder", "message", "pattern", "value", "<PERSON><PERSON><PERSON><PERSON>", "validate", "confirmPassword", "id", "htmlFor", "to", "terms"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Signup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport { auth } from '../firebase';\nimport { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { sendVerificationEmail } from '../utils/emailService';\n\nconst Signup = () => {\n  const { register, handleSubmit, watch, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const password = watch('password');\n\n  const onSubmit = async (data) => {\n    setServerError(''); // Clear previous errors\n    try {\n      // Create the user in Firebase Authentication\n      const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);\n      const user = userCredential.user;\n\n      // Update the user's profile with their username\n      await update<PERSON>rofile(user, {\n        displayName: data.username\n      });\n\n      // Generate verification link manually\n      const actionCodeSettings = {\n        url: `${window.location.origin}/login`,\n        handleCodeInApp: true,\n      };\n      \n      // Get the verification link\n      const verificationLink = await auth.currentUser.getIdToken()\n        .then(() => auth.currentUser.getIdToken(true))\n        .then((idToken) => {\n          return `https://${auth.app.options.authDomain}/__/auth/action?mode=verifyEmail&oobCode=${idToken}&apiKey=${auth.app.options.apiKey}&continueUrl=${encodeURIComponent(actionCodeSettings.url)}`;\n        });\n      \n      // Send verification email using SendGrid\n      await sendVerificationEmail(user.email, data.username, verificationLink);\n      \n      // Sign out the user until they verify their email\n      await auth.signOut();\n      \n      // Navigate to check-email page\n      navigate('/check-email', { state: { email: user.email } });\n\n    } catch (error) {\n      let friendlyMessage = 'An unexpected error occurred. Please try again.';\n      // Provide user-friendly error messages\n      switch (error.code) {\n        case 'auth/email-already-in-use':\n          friendlyMessage = 'This email address is already in use by another account.';\n          break;\n        case 'auth/invalid-email':\n          friendlyMessage = 'The email address you entered is not valid.';\n          break;\n        case 'auth/weak-password':\n          friendlyMessage = 'The password is too weak. Please use at least 6 characters.';\n          break;\n        default:\n          console.error('Firebase Signup Error:', error);\n      }\n      setServerError(friendlyMessage);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Create an Account</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            \n            <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Username</label>\n                <input\n                  type=\"text\"\n                  {...register('username', { required: 'Username is required' })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Choose a username\"\n                />\n                {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Email</label>\n                <input\n                  type=\"email\"\n                  {...register('email', { required: 'Email is required', pattern: { value: /\\S+@\\S+\\.\\S+/, message: 'Email is invalid' } })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Enter your email\"\n                />\n                {errors.email && <p className=\"mt-2 text-sm text-red-500\">{errors.email.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n                <input\n                  type=\"password\"\n                  {...register('password', { required: 'Password is required', minLength: { value: 6, message: 'Password must be at least 6 characters' } })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Create a password\"\n                />\n                {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-400\">Confirm Password</label>\n                <input\n                  type=\"password\"\n                  {...register('confirmPassword', { \n                    required: 'Please confirm your password',\n                    validate: value => value === password || 'Passwords do not match'\n                  })}\n                  className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n                  placeholder=\"Confirm your password\"\n                />\n                {errors.confirmPassword && <p className=\"mt-2 text-sm text-red-500\">{errors.confirmPassword.message}</p>}\n              </div>\n\n              <div className=\"flex items-start\">\n                <input\n                  id=\"terms\"\n                  type=\"checkbox\"\n                  {...register('terms', { required: 'You must agree to the terms' })}\n                  className=\"h-4 w-4 mt-1 text-blue-600 bg-blue-900 border-blue-800 rounded focus:ring-blue-500\"\n                />\n                <div className=\"ml-3 text-sm\">\n                  <label htmlFor=\"terms\" className=\"font-medium text-gray-400\">\n                    I agree to the <Link to=\"/terms\" className=\"text-blue-400 hover:text-blue-300\">Terms and Conditions</Link>\n                  </label>\n                  {errors.terms && <p className=\"mt-1 text-sm text-red-500\">{errors.terms.message}</p>}\n                </div>\n              </div>\n\n              <div>\n                <button\n                  type=\"submit\"\n                  className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n                >\n                  Sign Up\n                </button>\n              </div>\n            </form>\n            \n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Already have an account?{' '}\n              <Link to=\"/login\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Log in\n              </Link>\n            </p>\n          </div>\n\n          {/* Info Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\">\n              <h2 className=\"text-2xl font-bold mb-4\">Join BlazeTrade Today</h2>\n              <p className=\"text-center text-gray-400\">Access premium trading tools, real-time data, and a vibrant community. Your journey to smarter trading starts here.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,IAAI,KAAQ,aAAa,CAClC,OAASC,8BAA8B,CAAEC,aAAa,KAAQ,eAAe,CAC7E,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,qBAAqB,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,KAAK,CAAEC,SAAS,CAAE,CAAEC,MAAO,CAAE,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAC1E,KAAM,CAAAiB,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACa,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAAsB,QAAQ,CAAGN,KAAK,CAAC,UAAU,CAAC,CAElC,KAAM,CAAAO,QAAQ,CAAG,KAAO,CAAAC,IAAI,EAAK,CAC/BH,cAAc,CAAC,EAAE,CAAC,CAAE;AACpB,GAAI,CACF;AACA,KAAM,CAAAI,cAAc,CAAG,KAAM,CAAArB,8BAA8B,CAACD,IAAI,CAAEqB,IAAI,CAACE,KAAK,CAAEF,IAAI,CAACF,QAAQ,CAAC,CAC5F,KAAM,CAAAK,IAAI,CAAGF,cAAc,CAACE,IAAI,CAEhC;AACA,KAAM,CAAAtB,aAAa,CAACsB,IAAI,CAAE,CACxBC,WAAW,CAAEJ,IAAI,CAACK,QACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,kBAAkB,CAAG,CACzBC,GAAG,CAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,QAAQ,CACtCC,eAAe,CAAE,IACnB,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAG,KAAM,CAAAjC,IAAI,CAACkC,WAAW,CAACC,UAAU,CAAC,CAAC,CACzDC,IAAI,CAAC,IAAMpC,IAAI,CAACkC,WAAW,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC,CAC7CC,IAAI,CAAEC,OAAO,EAAK,CACjB,MAAO,WAAWrC,IAAI,CAACsC,GAAG,CAACC,OAAO,CAACC,UAAU,4CAA4CH,OAAO,WAAWrC,IAAI,CAACsC,GAAG,CAACC,OAAO,CAACE,MAAM,gBAAgBC,kBAAkB,CAACf,kBAAkB,CAACC,GAAG,CAAC,EAAE,CAChM,CAAC,CAAC,CAEJ;AACA,KAAM,CAAAvB,qBAAqB,CAACmB,IAAI,CAACD,KAAK,CAAEF,IAAI,CAACK,QAAQ,CAAEO,gBAAgB,CAAC,CAExE;AACA,KAAM,CAAAjC,IAAI,CAAC2C,OAAO,CAAC,CAAC,CAEpB;AACA3B,QAAQ,CAAC,cAAc,CAAE,CAAE4B,KAAK,CAAE,CAAErB,KAAK,CAAEC,IAAI,CAACD,KAAM,CAAE,CAAC,CAAC,CAE5D,CAAE,MAAOsB,KAAK,CAAE,CACd,GAAI,CAAAC,eAAe,CAAG,iDAAiD,CACvE;AACA,OAAQD,KAAK,CAACE,IAAI,EAChB,IAAK,2BAA2B,CAC9BD,eAAe,CAAG,0DAA0D,CAC5E,MACF,IAAK,oBAAoB,CACvBA,eAAe,CAAG,6CAA6C,CAC/D,MACF,IAAK,oBAAoB,CACvBA,eAAe,CAAG,6DAA6D,CAC/E,MACF,QACEE,OAAO,CAACH,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CACA3B,cAAc,CAAC4B,eAAe,CAAC,CACjC,CACF,CAAC,CAED,mBACErC,KAAA,QAAKwC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChE3C,IAAA,CAACT,MAAM,GAAE,CAAC,cACVS,IAAA,QAAK0C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DzC,KAAA,QAAKwC,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAG/EzC,KAAA,QAAKwC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3C,IAAA,OAAI0C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,CAC7DjC,WAAW,eAAIV,IAAA,MAAG0C,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAEjC,WAAW,CAAI,CAAC,cAEnHR,KAAA,SAAMW,QAAQ,CAAER,YAAY,CAACQ,QAAQ,CAAE,CAAC6B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC3DzC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E3C,IAAA,UACE4C,IAAI,CAAC,MAAM,IACPxC,QAAQ,CAAC,UAAU,CAAE,CAAEyC,QAAQ,CAAE,sBAAuB,CAAC,CAAC,CAC9DH,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,mBAAmB,CAChC,CAAC,CACDtC,MAAM,CAACW,QAAQ,eAAInB,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEnC,MAAM,CAACW,QAAQ,CAAC4B,OAAO,CAAI,CAAC,EACvF,CAAC,cAEN7C,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxE3C,IAAA,UACE4C,IAAI,CAAC,OAAO,IACRxC,QAAQ,CAAC,OAAO,CAAE,CAAEyC,QAAQ,CAAE,mBAAmB,CAAEG,OAAO,CAAE,CAAEC,KAAK,CAAE,cAAc,CAAEF,OAAO,CAAE,kBAAmB,CAAE,CAAC,CAAC,CACzHL,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,kBAAkB,CAC/B,CAAC,CACDtC,MAAM,CAACQ,KAAK,eAAIhB,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEnC,MAAM,CAACQ,KAAK,CAAC+B,OAAO,CAAI,CAAC,EACjF,CAAC,cAEN7C,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E3C,IAAA,UACE4C,IAAI,CAAC,UAAU,IACXxC,QAAQ,CAAC,UAAU,CAAE,CAAEyC,QAAQ,CAAE,sBAAsB,CAAEK,SAAS,CAAE,CAAED,KAAK,CAAE,CAAC,CAAEF,OAAO,CAAE,wCAAyC,CAAE,CAAC,CAAC,CAC1IL,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,mBAAmB,CAChC,CAAC,CACDtC,MAAM,CAACI,QAAQ,eAAIZ,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEnC,MAAM,CAACI,QAAQ,CAACmC,OAAO,CAAI,CAAC,EACvF,CAAC,cAEN7C,KAAA,QAAAyC,QAAA,eACE3C,IAAA,UAAO0C,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACnF3C,IAAA,UACE4C,IAAI,CAAC,UAAU,IACXxC,QAAQ,CAAC,iBAAiB,CAAE,CAC9ByC,QAAQ,CAAE,8BAA8B,CACxCM,QAAQ,CAAEF,KAAK,EAAIA,KAAK,GAAKrC,QAAQ,EAAI,wBAC3C,CAAC,CAAC,CACF8B,SAAS,CAAC,oIAAoI,CAC9II,WAAW,CAAC,uBAAuB,CACpC,CAAC,CACDtC,MAAM,CAAC4C,eAAe,eAAIpD,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEnC,MAAM,CAAC4C,eAAe,CAACL,OAAO,CAAI,CAAC,EACrG,CAAC,cAEN7C,KAAA,QAAKwC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B3C,IAAA,UACEqD,EAAE,CAAC,OAAO,CACVT,IAAI,CAAC,UAAU,IACXxC,QAAQ,CAAC,OAAO,CAAE,CAAEyC,QAAQ,CAAE,6BAA8B,CAAC,CAAC,CAClEH,SAAS,CAAC,oFAAoF,CAC/F,CAAC,cACFxC,KAAA,QAAKwC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BzC,KAAA,UAAOoD,OAAO,CAAC,OAAO,CAACZ,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EAAC,iBAC5C,cAAA3C,IAAA,CAACJ,IAAI,EAAC2D,EAAE,CAAC,QAAQ,CAACb,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,EACrG,CAAC,CACPnC,MAAM,CAACgD,KAAK,eAAIxD,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEnC,MAAM,CAACgD,KAAK,CAACT,OAAO,CAAI,CAAC,EACjF,CAAC,EACH,CAAC,cAEN/C,IAAA,QAAA2C,QAAA,cACE3C,IAAA,WACE4C,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,2MAA2M,CAAAC,QAAA,CACtN,SAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,cAEPzC,KAAA,MAAGwC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,0BAC5B,CAAC,GAAG,cAC5B3C,IAAA,CAACJ,IAAI,EAAC2D,EAAE,CAAC,QAAQ,CAACb,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,QAE5E,CAAM,CAAC,EACN,CAAC,EACD,CAAC,cAGNzC,KAAA,QAAKwC,SAAS,CAAC,4GAA4G,CAAAC,QAAA,eACvH3C,IAAA,OAAI0C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAClE3C,IAAA,MAAG0C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,qHAAmH,CAAG,CAAC,EAC/J,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}