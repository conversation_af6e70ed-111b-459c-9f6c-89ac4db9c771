{"ast": null, "code": "/**\n * Pipe\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => v => b(a(v));\nconst pipe = function () {\n  for (var _len = arguments.length, transformers = new Array(_len), _key = 0; _key < _len; _key++) {\n    transformers[_key] = arguments[_key];\n  }\n  return transformers.reduce(combineFunctions);\n};\nexport { pipe };", "map": {"version": 3, "names": ["combineFunctions", "a", "b", "v", "pipe", "_len", "arguments", "length", "transformers", "Array", "_key", "reduce"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/utils/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAMC,CAAC,IAAKD,CAAC,CAACD,CAAC,CAACE,CAAC,CAAC,CAAC;AACjD,MAAMC,IAAI,GAAG,SAAAA,CAAA;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,YAAY,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAZF,YAAY,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAAA,OAAKF,YAAY,CAACG,MAAM,CAACX,gBAAgB,CAAC;AAAA;AAEvE,SAASI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}