{"ast": null, "code": "import * as React from 'react';\nimport React__default from 'react';\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb = typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined' && typeof document !== 'undefined';\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar isKey = value => /^\\w*$/.test(value);\nvar isUndefined = val => val === undefined;\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = React__default.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React__default.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n    children,\n    ...data\n  } = props;\n  return React__default.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = React__default.useState(control._formState);\n  const _localProxyFormState = React__default.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name,\n    formState: _localProxyFormState.current,\n    exact,\n    callback: formState => {\n      !disabled && updateFormState({\n        ...control._formState,\n        ...formState\n      });\n    }\n  }), [name, disabled, exact]);\n  React__default.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n  return React__default.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _defaultValue = React__default.useRef(defaultValue);\n  const [value, updateValue] = React__default.useState(control._getWatch(name, _defaultValue.current));\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name,\n    formState: {\n      values: true\n    },\n    exact,\n    callback: formState => !disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n  }), [name, control, disabled, exact]);\n  React__default.useEffect(() => control._removeUnmounted());\n  return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name,\n    exact: true\n  });\n  const _props = React__default.useRef(props);\n  const _registerProps = React__default.useRef(control.register(name, {\n    ...props.rules,\n    value,\n    ...(isBoolean(props.disabled) ? {\n      disabled: props.disabled\n    } : {})\n  }));\n  const fieldState = React__default.useMemo(() => Object.defineProperties({}, {\n    invalid: {\n      enumerable: true,\n      get: () => !!get(formState.errors, name)\n    },\n    isDirty: {\n      enumerable: true,\n      get: () => !!get(formState.dirtyFields, name)\n    },\n    isTouched: {\n      enumerable: true,\n      get: () => !!get(formState.touchedFields, name)\n    },\n    isValidating: {\n      enumerable: true,\n      get: () => !!get(formState.validatingFields, name)\n    },\n    error: {\n      enumerable: true,\n      get: () => get(formState.errors, name)\n    }\n  }), [formState, name]);\n  const onChange = React__default.useCallback(event => _registerProps.current.onChange({\n    target: {\n      value: getEventValue(event),\n      name: name\n    },\n    type: EVENTS.CHANGE\n  }), [name]);\n  const onBlur = React__default.useCallback(() => _registerProps.current.onBlur({\n    target: {\n      value: get(control._formValues, name),\n      name: name\n    },\n    type: EVENTS.BLUR\n  }), [name, control._formValues]);\n  const ref = React__default.useCallback(elm => {\n    const field = get(control._fields, name);\n    if (field && elm) {\n      field._f.ref = {\n        focus: () => elm.focus && elm.focus(),\n        select: () => elm.select && elm.select(),\n        setCustomValidity: message => elm.setCustomValidity(message),\n        reportValidity: () => elm.reportValidity()\n      };\n    }\n  }, [control._fields, name]);\n  const field = React__default.useMemo(() => ({\n    name,\n    value,\n    ...(isBoolean(disabled) || formState.disabled ? {\n      disabled: formState.disabled || disabled\n    } : {}),\n    onChange,\n    onBlur,\n    ref\n  }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n  React__default.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled) ? {\n        disabled: _props.current.disabled\n      } : {})\n    });\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    !isArrayField && control.register(name);\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  React__default.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name\n    });\n  }, [disabled, name, control]);\n  return React__default.useMemo(() => ({\n    field,\n    formState,\n    fieldState\n  }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst flatten = obj => {\n  const output = {};\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n  return output;\n};\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = React__default.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      const flattenFormValues = flatten(control._formValues);\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? {\n                'Content-Type': encType\n              } : {})\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  React__default.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? React__default.createElement(React__default.Fragment, null, render({\n    submit\n  })) : React__default.createElement(\"form\", {\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit,\n    ...rest\n  }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? {\n  ...errors[name],\n  types: {\n    ...(errors[name] && errors[name].types ? errors[name].types : {}),\n    [type]: message || true\n  }\n} : {};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2, _internal_visited) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioInput = element => element.type === 'radio';\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n            ...markFieldsDirty(data[key])\n          };\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nvar getFieldValueAs = (value, {\n  valueAsNumber,\n  valueAsDate,\n  setValueAs\n}) => isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({\n      value\n    }) => value);\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar isRegex = value => value instanceof RegExp;\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = fieldReference => !!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find(validateFunction => validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n        _f,\n        ...currentField\n      } = field;\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n    name,\n    ...formState\n  } = formStateData;\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isMessage = value => isString(value);\nfunction getValidateError(result, ref, type = 'validate') {\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n    };\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message)\n          };\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n  let _options = {\n    ...defaultOptions,\n    ...props\n  };\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  let _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState\n  };\n  const _subjects = {\n    array: createSubject(),\n    state: createSubject()\n  };\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _setValid = async shouldUpdateValid => {\n    if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (names, isValidating) => {\n    if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n      (names || Array.from(_names.mount)).forEach(name => {\n        if (name) {\n          isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n        }\n      });\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields)\n      });\n    }\n  };\n  const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _setValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n        const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n      }\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n        }\n      }\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? {\n          isValid\n        } : {}),\n        errors: _formState.errors,\n        name\n      };\n      _formState = {\n        ..._formState,\n        ...updatedFormState\n      };\n      _subjects.state.next(updatedFormState);\n    }\n  };\n  const _runSchema = async name => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    _updateIsValidating(name);\n    return result;\n  };\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _runSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n    valid: true\n  }) => {\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n          _f,\n          ...fieldValue\n        } = field;\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n          const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        !isEmptyObject(fieldValue) && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => !_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n    ...(_state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n      [names]: defaultValue\n    } : defaultValue)\n  }, isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach(checkboxRef => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(data => data === checkboxRef.value);\n                } else {\n                  checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues)\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues)\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next({\n      ..._formState\n    });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues)\n    });\n  };\n  const onChange = async event => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.state.next({\n        name,\n        type: event.type,\n        values: cloneObject(_formValues)\n      });\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n        return shouldRender && _subjects.state.next({\n          name,\n          ...(watched ? {} : fieldState)\n        });\n      }\n      !isBlurEvent && watched && _subjects.state.next({\n        ..._formState\n      });\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _runSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsValidating([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next({\n      ...(!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n        name\n      }),\n      ...(_options.resolver || !name ? {\n        isValid\n      } : {}),\n      errors: _formState.errors\n    });\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues)\n    };\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n    // Don't override existing error messages elsewhere in the object tree.\n    const {\n      ref: currentRef,\n      message,\n      type,\n      ...restOfErrorTree\n    } = currentError;\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref\n    });\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.state.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const _subscribe = props => _subjects.state.subscribe({\n    next: formState => {\n      if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n        props.callback({\n          values: {\n            ..._formValues\n          },\n          ..._formState,\n          ...formState\n        });\n      }\n    }\n  }).unsubscribe;\n  const subscribe = props => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState\n    });\n  };\n  const unregister = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.state.next({\n      values: cloneObject(_formValues)\n    });\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : {\n        isDirty: _getDirty()\n      })\n    });\n    !options.keepIsValid && _setValid();\n  };\n  const _setDisabledField = ({\n    disabled,\n    name\n  }) => {\n    if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n  const register = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : {\n          ref: {\n            name\n          }\n        }),\n        name,\n        mount: true,\n        ...options\n      }\n    });\n    _names.mount.add(name);\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n        name\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return {\n      ...(disabledIsDefined ? {\n        disabled: options.disabled || _options.disabled\n      } : {}),\n      ...(_options.progressive ? {\n        required: !!options.required,\n        min: getRuleValue(options.min),\n        max: getRuleValue(options.max),\n        minLength: getRuleValue(options.minLength),\n        maxLength: getRuleValue(options.maxLength),\n        pattern: getRuleValue(options.pattern)\n      } : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox ? {\n                refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n                ref: {\n                  type: fieldRef.type,\n                  name\n                }\n              } : {\n                ref: fieldRef\n              })\n            }\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    };\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        const currentField = get(_fields, name);\n        if (currentField) {\n          ref.disabled = currentField._f.disabled || disabled;\n          if (Array.isArray(currentField._f.refs)) {\n            currentField._f.refs.forEach(inputRef => {\n              inputRef.disabled = currentField._f.disabled || disabled;\n            });\n          }\n        }\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _runSchema();\n      _formState.errors = errors;\n      fieldValues = cloneObject(values);\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    if (_names.disabled.size) {\n      for (const name of _names.disabled) {\n        unset(fieldValues, name);\n      }\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid({\n          ..._formState.errors\n        }, e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n      _subjects.state.next({\n        ..._formState\n      });\n    }\n  };\n  const _reset = (formValues, keepStateOptions = {}) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([..._names.mount, ...Object.keys(getDirtyFields(_defaultValues, _formValues))]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(fieldName, get(values, fieldName));\n          }\n        } else {\n          _fields = {};\n        }\n      }\n      _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n      _subjects.array.next({\n        values: {\n          ...values\n        }\n      });\n      _subjects.state.next({\n        values: {\n          ...values\n        }\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!_options.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n      }\n    }\n  };\n  const _setFormState = updatedFormState => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState\n    };\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value\n        };\n      }\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n  return {\n    ...methods,\n    formControl: methods\n  };\n}\nvar generateId = () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules\n  } = props;\n  const [fields, setFields] = React__default.useState(control._getFieldArray(name));\n  const ids = React__default.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = React__default.useRef(fields);\n  const _name = React__default.useRef(name);\n  const _actioned = React__default.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  rules && control.register(name, rules);\n  useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n    next: ({\n      values,\n      name: fieldArrayName\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    }\n  }).unsubscribe, [control]);\n  const updateValues = React__default.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._setFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  React__default.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next({\n      ...control._formState\n    });\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues)\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  React__default.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n    return () => {\n      const updateMounted = (name, value) => {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n      control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: React__default.useCallback(swap, [updateValues, name, control]),\n    move: React__default.useCallback(move, [updateValues, name, control]),\n    prepend: React__default.useCallback(prepend, [updateValues, name, control]),\n    append: React__default.useCallback(append, [updateValues, name, control]),\n    remove: React__default.useCallback(remove, [updateValues, name, control]),\n    insert: React__default.useCallback(insert$1, [updateValues, name, control]),\n    update: React__default.useCallback(update, [updateValues, name, control]),\n    replace: React__default.useCallback(replace, [updateValues, name, control]),\n    fields: React__default.useMemo(() => fields.map((field, index) => ({\n      ...field,\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n  const _formControl = React__default.useRef(undefined);\n  const _values = React__default.useRef(undefined);\n  const [formState, updateFormState] = React__default.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState\n      };\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const {\n        formControl,\n        ...rest\n      } = createFormControl(props);\n      _formControl.current = {\n        ...rest,\n        formState\n      };\n    }\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({\n        ...control._formState\n      }),\n      reRenderRoot: true\n    });\n    updateFormState(data => ({\n      ...data,\n      isReady: true\n    }));\n    control._formState.isReady = true;\n    return sub;\n  }, [control]);\n  React__default.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  React__default.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n  React__default.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n  React__default.useEffect(() => {\n    props.shouldUnregister && control._subjects.state.next({\n      values: control._getWatch()\n    });\n  }, [control, props.shouldUnregister]);\n  React__default.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  React__default.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions\n      });\n      _values.current = props.values;\n      updateFormState(state => ({\n        ...state\n      }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n  React__default.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({\n        ...control._formState\n      });\n    }\n    control._removeUnmounted();\n  });\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };", "map": {"version": 3, "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "getNodeParentName", "name", "substring", "search", "isNameInFieldArray", "names", "has", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "max", "min", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "validate", "HookFormContext", "React__default", "createContext", "displayName", "useFormContext", "useContext", "FormProvider", "props", "children", "createElement", "Provider", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "React", "useLayoutEffect", "useEffect", "useFormState", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "useCallback", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "Controller", "render", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "Form", "mounted", "setMounted", "method", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "some", "includes", "response", "fetch", "String", "body", "status", "_subjects", "state", "next", "isSubmitSuccessful", "setError", "Fragment", "noValidate", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observer", "subscribe", "push", "unsubscribe", "o", "observers", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys2", "val1", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "live", "isConnected", "baseGet", "updatePath", "slice", "isEmptyArray", "unset", "paths", "childObject", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "getResolverOptions", "fieldsNames", "criteriaMode", "shouldUseNativeValidation", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "find", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "startsWith", "iterateFieldsByAction", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "shouldRenderFormState", "formStateData", "shouldSubscribeByName", "signalName", "currentName", "skipValidation", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "inputValue", "inputRef", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "shouldFocusError", "createFormControl", "submitCount", "isReady", "isSubmitting", "Set", "unMount", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "shouldDisplayAllAssociatedErrors", "debounce", "wait", "clearTimeout", "setTimeout", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "_setFieldArray", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "argA", "argB", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updatedFormState", "context", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "_getFieldArray", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "Promise", "shouldFocus", "getFieldState", "clearErrors", "inputName", "currentError", "currentRef", "restOfErrorTree", "payload", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "_disableForm", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "size", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "setFocus", "shouldSelect", "_resetDefaultValues", "then", "resetOptions", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeAtIndexes", "indexes", "i", "temp", "removeArrayAt", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "useFieldArray", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "appendValue", "prepend", "prependValue", "remove", "insert$1", "insertValue", "swap", "move", "update", "item", "existingError", "useForm", "_formControl", "_values", "sub"], "sources": ["C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isCheckBoxInput.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isDateObject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isNullOrUndefined.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isObject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getEventValue.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getNodeParentName.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\isNameInFieldArray.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isPlainObject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isWeb.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\cloneObject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isKey.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isUndefined.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\compact.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\stringToPath.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\get.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isBoolean.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\set.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useFormContext.tsx", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getProxyFormState.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useIsomorphicLayoutEffect.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useFormState.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isString.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\generateWatchOutput.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useWatch.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useController.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\controller.tsx", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\flatten.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\form.tsx", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\appendErrors.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\convertToArrayPayload.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\createSubject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isPrimitive.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\deepEqual.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isEmptyObject.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isFileInput.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isFunction.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isHTMLElement.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isMultipleSelect.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isRadioInput.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isRadioOrCheckbox.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\live.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\unset.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\objectHasFunction.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getDirtyFields.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getCheckboxValue.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getFieldValueAs.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getRadioValue.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getFieldValue.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getResolverOptions.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isRegex.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getRuleValue.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getValidationModes.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\hasPromiseValidation.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\hasValidation.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\isWatched.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\iterateFieldsByAction.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\schemaErrorLookup.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\shouldRenderFormState.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\shouldSubscribeByName.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\skipValidation.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\unsetEmptyArray.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\updateFieldArrayRootError.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\isMessage.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getValidateError.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getValueAndMessage.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\validateField.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\createFormControl.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\generateId.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\logic\\getFocusFieldName.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\append.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\fillEmptyArray.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\insert.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\move.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\prepend.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\remove.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\swap.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\utils\\update.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useFieldArray.ts", "C:\\Users\\<USER>\\Desktop\\blazetrad-app\\src\\client\\node_modules\\react-hook-form\\src\\useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";;AAEA,IAAAA,eAAA,GAAgBC,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,UAAU;ACH7B,IAAAC,YAAA,GAAgBC,KAAc,IAAoBA,KAAK,YAAYC,IAAI;ACAvE,IAAAC,iBAAA,GAAgBF,KAAc,IAAgCA,KAAK,IAAI,IAAI;ACGpE,MAAMG,YAAY,GAAIH,KAAc,IACzC,OAAOA,KAAK,KAAK,QAAQ;AAE3B,IAAAI,QAAA,GAAkCJ,KAAc,IAC9C,CAACE,iBAAiB,CAACF,KAAK,CAAC,IACzB,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IACrBG,YAAY,CAACH,KAAK,CAAC,IACnB,CAACD,YAAY,CAACC,KAAK,CAAC;ACLtB,IAAAO,aAAA,GAAgBC,KAAc,IAC5BJ,QAAQ,CAACI,KAAK,CAAC,IAAKA,KAAe,CAACC,MAAA,GAChCb,eAAe,CAAEY,KAAe,CAACC,MAAM,IACpCD,KAAe,CAACC,MAAM,CAACC,OAAA,GACvBF,KAAe,CAACC,MAAM,CAACT,KAAA,GAC1BQ,KAAK;ACVX,IAAAG,iBAAA,GAAgBC,IAAY,IAC1BA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAED,IAAI,CAACE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAIF,IAAI;ACGvD,IAAAG,kBAAA,GAAeA,CAACC,KAA6B,EAAEJ,IAAuB,KACpEI,KAAK,CAACC,GAAG,CAACN,iBAAiB,CAACC,IAAI,CAAC,CAAC;ACHpC,IAAAM,aAAA,GAAgBC,UAAkB,IAAI;EACpC,MAAMC,aAAa,GACjBD,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACC,SAAS;EAE5D,OACElB,QAAQ,CAACgB,aAAa,CAAC,IAAIA,aAAa,CAACG,cAAc,CAAC,eAAe,CAAC;AAE5E,CAAC;ACTD,IAAAC,KAAA,GAAe,OAAOC,MAAM,KAAK,WAAW,IAC1C,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,IACzC,OAAOC,QAAQ,KAAK,WAAW;ACEnB,SAAUC,WAAWA,CAAIC,IAAO;EAC5C,IAAIC,IAAS;EACb,MAAMxB,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EACnC,MAAME,kBAAkB,GACtB,OAAOC,QAAQ,KAAK,WAAW,GAAGH,IAAI,YAAYG,QAAQ,GAAG,KAAK;EAEpE,IAAIH,IAAI,YAAY5B,IAAI,EAAE;IACxB6B,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,IAAI,CAAC;SAChB,IACL,EAAEL,KAAK,KAAKK,IAAI,YAAYI,IAAI,IAAIF,kBAAkB,CAAC,CAAC,KACvDzB,OAAO,IAAIF,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAC3B;IACAC,IAAI,GAAGxB,OAAO,GAAG,EAAE,GAAG,EAAE;IAExB,IAAI,CAACA,OAAO,IAAI,CAACY,aAAa,CAACW,IAAI,CAAC,EAAE;MACpCC,IAAI,GAAGD,IAAI;WACN;MACL,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACN,cAAc,CAACW,GAAG,CAAC,EAAE;UAC5BJ,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACC,IAAI,CAACK,GAAG,CAAC,CAAC;;;;SAInC;IACL,OAAOL,IAAI;;EAGb,OAAOC,IAAI;AACb;AChCA,IAAAK,KAAA,GAAgBnC,KAAa,IAAK,OAAO,CAACoC,IAAI,CAACpC,KAAK,CAAC;ACArD,IAAAqC,WAAA,GAAgBC,GAAY,IAAuBA,GAAG,KAAKC,SAAS;ACApE,IAAAC,OAAA,GAAwBxC,KAAe,IACrCK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACyC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;ACCnD,IAAAC,YAAA,GAAgBC,KAAa,IAC3BJ,OAAO,CAACI,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC;ACGxD,IAAAC,GAAA,GAAeA,CACbC,MAAS,EACTC,IAAoB,EACpBC,YAAsB,KACf;EACP,IAAI,CAACD,IAAI,IAAI,CAAC7C,QAAQ,CAAC4C,MAAM,CAAC,EAAE;IAC9B,OAAOE,YAAY;;EAGrB,MAAMC,MAAM,GAAG,CAAChB,KAAK,CAACc,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGN,YAAY,CAACM,IAAI,CAAC,EAAEG,MAAM,CAC/D,CAACD,MAAM,EAAEjB,GAAG,KACVhC,iBAAiB,CAACiD,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACjB,GAAe,CAAC,EAC9Dc,MAAM,CACP;EAED,OAAOX,WAAW,CAACc,MAAM,CAAC,IAAIA,MAAM,KAAKH,MAAA,GACrCX,WAAW,CAACW,MAAM,CAACC,IAAe,CAAC,IACjCC,YAAA,GACAF,MAAM,CAACC,IAAe,IACxBE,MAAM;AACZ,CAAC;AC1BD,IAAAE,SAAA,GAAgBrD,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;ACM/E,IAAAsD,GAAA,GAAeA,CACbN,MAAmB,EACnBC,IAA4B,EAC5BjD,KAAe,KACb;EACF,IAAIuD,KAAK,GAAG,EAAE;EACd,MAAMC,QAAQ,GAAGrB,KAAK,CAACc,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGN,YAAY,CAACM,IAAI,CAAC;EAC1D,MAAMQ,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC9B,MAAMC,SAAS,GAAGD,MAAM,GAAG,CAAC;EAE5B,OAAO,EAAEF,KAAK,GAAGE,MAAM,EAAE;IACvB,MAAMvB,GAAG,GAAGsB,QAAQ,CAACD,KAAK,CAAC;IAC3B,IAAII,QAAQ,GAAG3D,KAAK;IAEpB,IAAIuD,KAAK,KAAKG,SAAS,EAAE;MACvB,MAAME,QAAQ,GAAGZ,MAAM,CAACd,GAAG,CAAC;MAC5ByB,QAAQ,GACNvD,QAAQ,CAACwD,QAAQ,CAAC,IAAIvD,KAAK,CAACC,OAAO,CAACsD,QAAQ,IACxCA,QAAA,GACA,CAACC,KAAK,CAAC,CAACL,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC,IACzB,KACA,EAAE;;IAGZ,IAAIrB,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvE;;IAGFc,MAAM,CAACd,GAAG,CAAC,GAAGyB,QAAQ;IACtBX,MAAM,GAAGA,MAAM,CAACd,GAAG,CAAC;;AAExB,CAAC;ACrCM,MAAM4B,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE;CACA;AAEH,MAAMC,eAAe,GAAG;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;CACG;AAEH,MAAMC,sBAAsB,GAAG;EACpCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACF;AClBV,MAAMC,eAAe,GAAGC,cAAK,CAACC,aAAa,CAAuB,IAAI,CAAC;AACvEF,eAAe,CAACG,WAAW,GAAG,iBAAiB;AAE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMC,cAAc,GAAGA,CAAA,KAK5BH,cAAK,CAACI,UAAU,CAACL,eAAe;AAMlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMM,YAAY,GAKvBC,KAAoE,IAClE;EACF,MAAM;IAAEC,QAAQ;IAAE,GAAG3D;EAAI,CAAE,GAAG0D,KAAK;EACnC,OACEN,cAAA,CAAAQ,aAAA,CAACT,eAAe,CAACU,QAAQ;IAAC1F,KAAK,EAAE6B;EAAgC,GAC9D2D,QAAQ,CACgB;AAE/B;ACxFA,IAAAG,iBAAA,GAAeA,CAKbC,SAAkC,EAClCC,OAA4D,EAC5DC,mBAAmC,EACnCC,MAAM,GAAG,IAAI,KACX;EACF,MAAM5C,MAAM,GAAG;IACb6C,aAAa,EAAEH,OAAO,CAACI;GACJ;EAErB,KAAK,MAAM/D,GAAG,IAAI0D,SAAS,EAAE;IAC3BM,MAAM,CAACC,cAAc,CAAChD,MAAM,EAAEjB,GAAG,EAAE;MACjCa,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMqD,IAAI,GAAGlE,GAA0D;QAEvE,IAAI2D,OAAO,CAACQ,eAAe,CAACD,IAAI,CAAC,KAAKlC,eAAe,CAACK,GAAG,EAAE;UACzDsB,OAAO,CAACQ,eAAe,CAACD,IAAI,CAAC,GAAG,CAACL,MAAM,IAAI7B,eAAe,CAACK,GAAG;;QAGhEuB,mBAAmB,KAAKA,mBAAmB,CAACM,IAAI,CAAC,GAAG,IAAI,CAAC;QACzD,OAAOR,SAAS,CAACQ,IAAI,CAAC;;IAEzB,EAAC;;EAGJ,OAAOjD,MAAM;AACf,CAAC;AC/BM,MAAMmD,yBAAyB,GACpC,OAAO7E,MAAM,KAAK,WAAW,GAAG8E,KAAK,CAACC,eAAe,GAAGD,KAAK,CAACE,SAAS;;ACQzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACG,SAAUC,YAAYA,CAI1BnB,KAA2D;EAE3D,MAAMoB,OAAO,GAAGvB,cAAc,EAAyC;EACvE,MAAM;IAAES,OAAO,GAAGc,OAAO,CAACd,OAAO;IAAEe,QAAQ;IAAEhG,IAAI;IAAEiG;EAAK,CAAE,GAAGtB,KAAK,IAAI,EAAE;EACxE,MAAM,CAACK,SAAS,EAAEkB,eAAe,CAAC,GAAG7B,cAAK,CAAC8B,QAAQ,CAAClB,OAAO,CAACmB,UAAU,CAAC;EACvE,MAAMC,oBAAoB,GAAGhC,cAAK,CAACiC,MAAM,CAAC;IACxCC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,KAAK;IACvBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;EACT,EAAC;EAEFpB,yBAAyB,CACvB,MACET,OAAO,CAAC8B,UAAU,CAAC;IACjB/G,IAAI;IACJgF,SAAS,EAAEqB,oBAAoB,CAACW,OAAO;IACvCf,KAAK;IACLgB,QAAQ,EAAGjC,SAAS,IAAI;MACtB,CAACgB,QAAQ,IACPE,eAAe,CAAC;QACd,GAAGjB,OAAO,CAACmB,UAAU;QACrB,GAAGpB;MACJ,EAAC;;GAEP,CAAC,EACJ,CAAChF,IAAI,EAAEgG,QAAQ,EAAEC,KAAK,CAAC,CACxB;EAED5B,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnBQ,oBAAoB,CAACW,OAAO,CAACH,OAAO,IAAI5B,OAAO,CAACiC,SAAS,CAAC,IAAI,CAAC;EACjE,CAAC,EAAE,CAACjC,OAAO,CAAC,CAAC;EAEb,OAAOZ,cAAK,CAAC8C,OAAO,CAClB,MACEpC,iBAAiB,CACfC,SAAS,EACTC,OAAO,EACPoB,oBAAoB,CAACW,OAAO,EAC5B,KAAK,CACN,EACH,CAAChC,SAAS,EAAEC,OAAO,CAAC,CACrB;AACH;AC5FA,IAAAmC,QAAA,GAAgBhI,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;ACI7E,IAAAiI,mBAAA,GAAeA,CACbjH,KAAoC,EACpCkH,MAAa,EACbC,UAAwB,EACxBC,QAAkB,EAClBlF,YAAuC,KACrC;EACF,IAAI8E,QAAQ,CAAChH,KAAK,CAAC,EAAE;IACnBoH,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACtH,KAAK,CAAC;IACnC,OAAO+B,GAAG,CAACoF,UAAU,EAAEnH,KAAK,EAAEkC,YAAY,CAAC;;EAG7C,IAAI7C,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACuH,GAAG,CACbC,SAAS,KACRJ,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACE,SAAS,CAAC,EACvCzF,GAAG,CAACoF,UAAU,EAAEK,SAAS,CAAC,CAC3B,CACF;;EAGHJ,QAAQ,KAAKF,MAAM,CAACO,QAAQ,GAAG,IAAI,CAAC;EAEpC,OAAON,UAAU;AACnB,CAAC;;ACoGD;;;;;;;;;;;;;;;AAeG;AACG,SAAUO,QAAQA,CACtBnD,KAAmC;EAEnC,MAAMoB,OAAO,GAAGvB,cAAc,EAAgB;EAC9C,MAAM;IACJS,OAAO,GAAGc,OAAO,CAACd,OAAO;IACzBjF,IAAI;IACJsC,YAAY;IACZ0D,QAAQ;IACRC;EAAK,CACN,GAAGtB,KAAK,IAAI,EAAE;EACf,MAAMoD,aAAa,GAAG1D,cAAK,CAACiC,MAAM,CAAChE,YAAY,CAAC;EAChD,MAAM,CAAClD,KAAK,EAAE4I,WAAW,CAAC,GAAG3D,cAAK,CAAC8B,QAAQ,CACzClB,OAAO,CAACgD,SAAS,CACfjI,IAAyB,EACzB+H,aAAa,CAACf,OAAgD,CAC/D,CACF;EAEDtB,yBAAyB,CACvB,MACET,OAAO,CAAC8B,UAAU,CAAC;IACjB/G,IAAI;IACJgF,SAAS,EAAE;MACTkD,MAAM,EAAE;IACT;IACDjC,KAAK;IACLgB,QAAQ,EAAGjC,SAAS,IAClB,CAACgB,QAAQ,IACTgC,WAAW,CACTX,mBAAmB,CACjBrH,IAA+C,EAC/CiF,OAAO,CAACqC,MAAM,EACdtC,SAAS,CAACkD,MAAM,IAAIjD,OAAO,CAACkD,WAAW,EACvC,KAAK,EACLJ,aAAa,CAACf,OAAO,CACtB;GAEN,CAAC,EACJ,CAAChH,IAAI,EAAEiF,OAAO,EAAEe,QAAQ,EAAEC,KAAK,CAAC,CACjC;EAED5B,cAAK,CAACwB,SAAS,CAAC,MAAMZ,OAAO,CAACmD,gBAAgB,EAAE,CAAC;EAEjD,OAAOhJ,KAAK;AACd;;ACrKA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAUiJ,aAAaA,CAK3B1D,KAAkE;EAElE,MAAMoB,OAAO,GAAGvB,cAAc,EAAyC;EACvE,MAAM;IAAExE,IAAI;IAAEgG,QAAQ;IAAEf,OAAO,GAAGc,OAAO,CAACd,OAAO;IAAEqD;EAAgB,CAAE,GAAG3D,KAAK;EAC7E,MAAM4D,YAAY,GAAGpI,kBAAkB,CAAC8E,OAAO,CAACqC,MAAM,CAACkB,KAAK,EAAExI,IAAI,CAAC;EACnE,MAAMZ,KAAK,GAAG0I,QAAQ,CAAC;IACrB7C,OAAO;IACPjF,IAAI;IACJsC,YAAY,EAAEH,GAAG,CACf8C,OAAO,CAACkD,WAAW,EACnBnI,IAAI,EACJmC,GAAG,CAAC8C,OAAO,CAACI,cAAc,EAAErF,IAAI,EAAE2E,KAAK,CAACrC,YAAY,CAAC,CACtD;IACD2D,KAAK,EAAE;EACR,EAAwC;EACzC,MAAMjB,SAAS,GAAGc,YAAY,CAAC;IAC7Bb,OAAO;IACPjF,IAAI;IACJiG,KAAK,EAAE;EACR,EAAC;EAEF,MAAMwC,MAAM,GAAGpE,cAAK,CAACiC,MAAM,CAAC3B,KAAK,CAAC;EAClC,MAAM+D,cAAc,GAAGrE,cAAK,CAACiC,MAAM,CACjCrB,OAAO,CAAC0D,QAAQ,CAAC3I,IAAI,EAAE;IACrB,GAAG2E,KAAK,CAACiE,KAAK;IACdxJ,KAAK;IACL,IAAIqD,SAAS,CAACkC,KAAK,CAACqB,QAAQ,CAAC,GAAG;MAAEA,QAAQ,EAAErB,KAAK,CAACqB;IAAQ,CAAE,GAAG,EAAE;EAClE,EAAC,CACH;EAED,MAAM6C,UAAU,GAAGxE,cAAK,CAAC8C,OAAO,CAC9B,MACE7B,MAAM,CAACwD,gBAAgB,CACrB,EAAE,EACF;IACEC,OAAO,EAAE;MACPC,UAAU,EAAE,IAAI;MAChB7G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC8B,MAAM,EAAE9G,IAAI;IACxC;IACDuG,OAAO,EAAE;MACPyC,UAAU,EAAE,IAAI;MAChB7G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAACyB,WAAW,EAAEzG,IAAI;IAC7C;IACDiJ,SAAS,EAAE;MACTD,UAAU,EAAE,IAAI;MAChB7G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC0B,aAAa,EAAE1G,IAAI;IAC/C;IACD4G,YAAY,EAAE;MACZoC,UAAU,EAAE,IAAI;MAChB7G,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAAC6C,SAAS,CAAC2B,gBAAgB,EAAE3G,IAAI;IAClD;IACDkJ,KAAK,EAAE;MACLF,UAAU,EAAE,IAAI;MAChB7G,GAAG,EAAEA,CAAA,KAAMA,GAAG,CAAC6C,SAAS,CAAC8B,MAAM,EAAE9G,IAAI;IACtC;EACF,EACsB,EAC3B,CAACgF,SAAS,EAAEhF,IAAI,CAAC,CAClB;EAED,MAAMwD,QAAQ,GAAGa,cAAK,CAAC8E,WAAW,CAC/BvJ,KAAU,IACT8I,cAAc,CAAC1B,OAAO,CAACxD,QAAQ,CAAC;IAC9B3D,MAAM,EAAE;MACNT,KAAK,EAAEO,aAAa,CAACC,KAAK,CAAC;MAC3BI,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEgE,MAAM,CAACG;EACd,EAAC,EACJ,CAACrD,IAAI,CAAC,CACP;EAED,MAAMuD,MAAM,GAAGc,cAAK,CAAC8E,WAAW,CAC9B,MACET,cAAc,CAAC1B,OAAO,CAACzD,MAAM,CAAC;IAC5B1D,MAAM,EAAE;MACNT,KAAK,EAAE+C,GAAG,CAAC8C,OAAO,CAACkD,WAAW,EAAEnI,IAAI,CAAC;MACrCA,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEgE,MAAM,CAACC;GACd,CAAC,EACJ,CAACnD,IAAI,EAAEiF,OAAO,CAACkD,WAAW,CAAC,CAC5B;EAED,MAAMiB,GAAG,GAAG/E,cAAK,CAAC8E,WAAW,CAC1BE,GAAQ,IAAI;IACX,MAAMC,KAAK,GAAGnH,GAAG,CAAC8C,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC;IAExC,IAAIsJ,KAAK,IAAID,GAAG,EAAE;MAChBC,KAAK,CAACE,EAAE,CAACJ,GAAG,GAAG;QACbK,KAAK,EAAEA,CAAA,KAAMJ,GAAG,CAACI,KAAK,IAAIJ,GAAG,CAACI,KAAK,EAAE;QACrCC,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,EAAE;QACxCC,iBAAiB,EAAGC,OAAe,IACjCP,GAAG,CAACM,iBAAiB,CAACC,OAAO,CAAC;QAChCC,cAAc,EAAEA,CAAA,KAAMR,GAAG,CAACQ,cAAc;OACzC;;GAEJ,EACD,CAAC5E,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC,CACxB;EAED,MAAMsJ,KAAK,GAAGjF,cAAK,CAAC8C,OAAO,CACzB,OAAO;IACLnH,IAAI;IACJZ,KAAK;IACL,IAAIqD,SAAS,CAACuD,QAAQ,CAAC,IAAIhB,SAAS,CAACgB,QAAA,GACjC;MAAEA,QAAQ,EAAEhB,SAAS,CAACgB,QAAQ,IAAIA;IAAQ,IAC1C,EAAE,CAAC;IACPxC,QAAQ;IACRD,MAAM;IACN6F;EACD,EAAC,EACF,CAACpJ,IAAI,EAAEgG,QAAQ,EAAEhB,SAAS,CAACgB,QAAQ,EAAExC,QAAQ,EAAED,MAAM,EAAE6F,GAAG,EAAEhK,KAAK,CAAC,CACnE;EAEDiF,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,MAAMiE,sBAAsB,GAC1B7E,OAAO,CAAC8E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAgB;IAEvDrD,OAAO,CAAC0D,QAAQ,CAAC3I,IAAI,EAAE;MACrB,GAAGyI,MAAM,CAACzB,OAAO,CAAC4B,KAAK;MACvB,IAAInG,SAAS,CAACgG,MAAM,CAACzB,OAAO,CAAChB,QAAQ,IACjC;QAAEA,QAAQ,EAAEyC,MAAM,CAACzB,OAAO,CAAChB;MAAQ,IACnC,EAAE;IACP,EAAC;IAEF,MAAMgE,aAAa,GAAGA,CAAChK,IAAuB,EAAEZ,KAAc,KAAI;MAChE,MAAMkK,KAAK,GAAUnH,GAAG,CAAC8C,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC;MAE/C,IAAIsJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;QACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG7K,KAAK;;IAE1B,CAAC;IAED4K,aAAa,CAAChK,IAAI,EAAE,IAAI,CAAC;IAEzB,IAAI8J,sBAAsB,EAAE;MAC1B,MAAM1K,KAAK,GAAG4B,WAAW,CAACmB,GAAG,CAAC8C,OAAO,CAAC8E,QAAQ,CAAC3E,aAAa,EAAEpF,IAAI,CAAC,CAAC;MACpE0C,GAAG,CAACuC,OAAO,CAACI,cAAc,EAAErF,IAAI,EAAEZ,KAAK,CAAC;MACxC,IAAIqC,WAAW,CAACU,GAAG,CAAC8C,OAAO,CAACkD,WAAW,EAAEnI,IAAI,CAAC,CAAC,EAAE;QAC/C0C,GAAG,CAACuC,OAAO,CAACkD,WAAW,EAAEnI,IAAI,EAAEZ,KAAK,CAAC;;;IAIzC,CAACmJ,YAAY,IAAItD,OAAO,CAAC0D,QAAQ,CAAC3I,IAAI,CAAC;IAEvC,OAAO,MAAK;MACV,CACEuI,YAAA,GACIuB,sBAAsB,IAAI,CAAC7E,OAAO,CAACiF,MAAM,CAACC,MAAA,GAC1CL,sBAAsB,IAExB7E,OAAO,CAACmF,UAAU,CAACpK,IAAI,IACvBgK,aAAa,CAAChK,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEsD,YAAY,EAAED,gBAAgB,CAAC,CAAC;EAEnDjE,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnBZ,OAAO,CAACoF,iBAAiB,CAAC;MACxBrE,QAAQ;MACRhG;IACD,EAAC;GACH,EAAE,CAACgG,QAAQ,EAAEhG,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE7B,OAAOZ,cAAK,CAAC8C,OAAO,CAClB,OAAO;IACLmC,KAAK;IACLtE,SAAS;IACT6D;GACD,CAAC,EACF,CAACS,KAAK,EAAEtE,SAAS,EAAE6D,UAAU,CAAC,CAC/B;AACH;;AC9NA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAMyB,UAAU,GAKd3F,KAA+D,IAE/DA,KAAK,CAAC4F,MAAM,CAAClC,aAAa,CAA0C1D,KAAK,CAAC;AChDrE,MAAM6F,OAAO,GAAIC,GAAgB,IAAI;EAC1C,MAAMC,MAAM,GAAgB,EAAE;EAE9B,KAAK,MAAMpJ,GAAG,IAAIgE,MAAM,CAACqF,IAAI,CAACF,GAAG,CAAC,EAAE;IAClC,IAAIlL,YAAY,CAACkL,GAAG,CAACnJ,GAAG,CAAC,CAAC,IAAImJ,GAAG,CAACnJ,GAAG,CAAC,KAAK,IAAI,EAAE;MAC/C,MAAMsJ,MAAM,GAAGJ,OAAO,CAACC,GAAG,CAACnJ,GAAG,CAAC,CAAC;MAEhC,KAAK,MAAMuJ,SAAS,IAAIvF,MAAM,CAACqF,IAAI,CAACC,MAAM,CAAC,EAAE;QAC3CF,MAAM,CAAC,GAAGpJ,GAAG,IAAIuJ,SAAS,EAAE,CAAC,GAAGD,MAAM,CAACC,SAAS,CAAC;;WAE9C;MACLH,MAAM,CAACpJ,GAAG,CAAC,GAAGmJ,GAAG,CAACnJ,GAAG,CAAC;;;EAI1B,OAAOoJ,MAAM;AACf,CAAC;ACdD,MAAMI,YAAY,GAAG,MAAM;AAE3B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,SAASC,IAAIA,CAGXpG,KAAkD;EAClD,MAAMoB,OAAO,GAAGvB,cAAc,EAAyC;EACvE,MAAM,CAACwG,OAAO,EAAEC,UAAU,CAAC,GAAG5G,cAAK,CAAC8B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IACJlB,OAAO,GAAGc,OAAO,CAACd,OAAO;IACzBxB,QAAQ;IACRmB,QAAQ;IACRuF,MAAM;IACNe,MAAM,GAAGJ,YAAY;IACrBK,OAAO;IACPC,OAAO;IACPC,OAAO;IACPd,MAAM;IACNe,SAAS;IACTC,cAAc;IACd,GAAGC;EAAI,CACR,GAAG7G,KAAK;EAET,MAAM8G,MAAM,GAAG,MAAO7L,KAAgC,IAAI;IACxD,IAAI8L,QAAQ,GAAG,KAAK;IACpB,IAAIxM,IAAI,GAAG,EAAE;IAEb,MAAM+F,OAAO,CAAC0G,YAAY,CAAC,MAAO1K,IAAI,IAAI;MACxC,MAAM2K,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAIC,YAAY,GAAG,EAAE;MAErB,IAAI;QACFA,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAC/K,IAAI,CAAC;QACnC,OAAAgL,EAAA,EAAM;MAER,MAAMC,iBAAiB,GAAG1B,OAAO,CAACvF,OAAO,CAACkD,WAAW,CAAC;MAEtD,KAAK,MAAM7G,GAAG,IAAI4K,iBAAiB,EAAE;QACnCN,QAAQ,CAACO,MAAM,CAAC7K,GAAG,EAAE4K,iBAAiB,CAAC5K,GAAG,CAAC,CAAC;;MAG9C,IAAImC,QAAQ,EAAE;QACZ,MAAMA,QAAQ,CAAC;UACbxC,IAAI;UACJrB,KAAK;UACLsL,MAAM;UACNU,QAAQ;UACRE;QACD,EAAC;;MAGJ,IAAI3B,MAAM,EAAE;QACV,IAAI;UACF,MAAMiC,6BAA6B,GAAG,CACpCjB,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,EAClCC,OAAO,CACR,CAACiB,IAAI,CAAEjN,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACkN,QAAQ,CAAC,MAAM,CAAC,CAAC;UAElD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,MAAM,CAACtC,MAAM,CAAC,EAAE;YAC3Ce,MAAM;YACNC,OAAO,EAAE;cACP,GAAGA,OAAO;cACV,IAAIC,OAAO,GAAG;gBAAE,cAAc,EAAEA;cAAO,CAAE,GAAG,EAAE;YAC/C;YACDsB,IAAI,EAAEN,6BAA6B,GAAGN,YAAY,GAAGF;UACtD,EAAC;UAEF,IACEW,QAAQ,KACPhB,cAAA,GACG,CAACA,cAAc,CAACgB,QAAQ,CAACI,MAAM,IAC/BJ,QAAQ,CAACI,MAAM,GAAG,GAAG,IAAIJ,QAAQ,CAACI,MAAM,IAAI,GAAG,CAAC,EACpD;YACAjB,QAAQ,GAAG,IAAI;YACfL,OAAO,IAAIA,OAAO,CAAC;cAAEkB;YAAQ,CAAE,CAAC;YAChCrN,IAAI,GAAGuN,MAAM,CAACF,QAAQ,CAACI,MAAM,CAAC;iBACzB;YACLrB,SAAS,IAAIA,SAAS,CAAC;cAAEiB;YAAQ,CAAE,CAAC;;UAEtC,OAAOrD,KAAc,EAAE;UACvBwC,QAAQ,GAAG,IAAI;UACfL,OAAO,IAAIA,OAAO,CAAC;YAAEnC;UAAK,CAAE,CAAC;;;IAGnC,CAAC,CAAC,CAACtJ,KAAK,CAAC;IAET,IAAI8L,QAAQ,IAAI/G,KAAK,CAACM,OAAO,EAAE;MAC7BN,KAAK,CAACM,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACjCC,kBAAkB,EAAE;MACrB,EAAC;MACFpI,KAAK,CAACM,OAAO,CAAC+H,QAAQ,CAAC,aAAa,EAAE;QACpC9N;MACD,EAAC;;EAEN,CAAC;EAEDmF,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnBoF,UAAU,CAAC,IAAI,CAAC;GACjB,EAAE,EAAE,CAAC;EAEN,OAAOV,MAAM,GACXlG,cAAA,CAAAQ,aAAA,CAAAR,cAAA,CAAA4I,QAAA,QACG1C,MAAM,CAAC;IACNkB;EACD,EAAC,CACD,GAEHpH,cAAA,CAAAQ,aAAA;IACEqI,UAAU,EAAElC,OAAO;IACnBb,MAAM,EAAEA,MAAM;IACde,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA,OAAO;IAChB3H,QAAQ,EAAEgI,MAAM;IAAA,GACZD;EAAI,GAEP5G,QAAQ,CAEZ;AACH;AC5IA,IAAAuI,YAAA,GAAeA,CACbnN,IAAuB,EACvBoN,wBAAiC,EACjCtG,MAA2B,EAC3B5H,IAAY,EACZ0K,OAAuB,KAEvBwD,wBAAA,GACI;EACE,GAAGtG,MAAM,CAAC9G,IAAI,CAAC;EACfqN,KAAK,EAAE;IACL,IAAIvG,MAAM,CAAC9G,IAAI,CAAC,IAAI8G,MAAM,CAAC9G,IAAI,CAAE,CAACqN,KAAK,GAAGvG,MAAM,CAAC9G,IAAI,CAAE,CAACqN,KAAK,GAAG,EAAE,CAAC;IACnE,CAACnO,IAAI,GAAG0K,OAAO,IAAI;EACpB;AACF,IACD,EAAE;ACrBR,IAAA0D,qBAAA,GAAmBlO,KAAQ,IAAMK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAE;ACgBxE,IAAAmO,aAAA,GAAeA,CAAA,KAAoB;EACjC,IAAIC,UAAU,GAAkB,EAAE;EAElC,MAAMV,IAAI,GAAI1N,KAAQ,IAAI;IACxB,KAAK,MAAMqO,QAAQ,IAAID,UAAU,EAAE;MACjCC,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAACX,IAAI,CAAC1N,KAAK,CAAC;;EAEzC,CAAC;EAED,MAAMsO,SAAS,GAAID,QAAqB,IAAkB;IACxDD,UAAU,CAACG,IAAI,CAACF,QAAQ,CAAC;IACzB,OAAO;MACLG,WAAW,EAAEA,CAAA,KAAK;QAChBJ,UAAU,GAAGA,UAAU,CAAC3L,MAAM,CAAEgM,CAAC,IAAKA,CAAC,KAAKJ,QAAQ,CAAC;;KAExD;EACH,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAK;IACvBJ,UAAU,GAAG,EAAE;EACjB,CAAC;EAED,OAAO;IACL,IAAIM,SAASA,CAAA;MACX,OAAON,UAAU;KAClB;IACDV,IAAI;IACJY,SAAS;IACTE;GACD;AACH,CAAC;ACzCD,IAAAG,WAAA,GAAgB3O,KAAc,IAC5BE,iBAAiB,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC;ACDpC,SAAU4O,SAASA,CAC/BC,OAAY,EACZC,OAAY,EACZC,iBAAiB,GAAG,IAAIC,OAAO,EAAE;EAEjC,IAAIL,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAACG,OAAO,CAAC,EAAE;IAChD,OAAOD,OAAO,KAAKC,OAAO;;EAG5B,IAAI/O,YAAY,CAAC8O,OAAO,CAAC,IAAI9O,YAAY,CAAC+O,OAAO,CAAC,EAAE;IAClD,OAAOD,OAAO,CAACI,OAAO,EAAE,KAAKH,OAAO,CAACG,OAAO,EAAE;;EAGhD,MAAMC,KAAK,GAAGhJ,MAAM,CAACqF,IAAI,CAACsD,OAAO,CAAC;EAClC,MAAMM,KAAK,GAAGjJ,MAAM,CAACqF,IAAI,CAACuD,OAAO,CAAC;EAElC,IAAII,KAAK,CAACzL,MAAM,KAAK0L,KAAK,CAAC1L,MAAM,EAAE;IACjC,OAAO,KAAK;;EAGd,IAAIsL,iBAAiB,CAAC9N,GAAG,CAAC4N,OAAO,CAAC,IAAIE,iBAAiB,CAAC9N,GAAG,CAAC6N,OAAO,CAAC,EAAE;IACpE,OAAO,IAAI;;EAEbC,iBAAiB,CAACzG,GAAG,CAACuG,OAAO,CAAC;EAC9BE,iBAAiB,CAACzG,GAAG,CAACwG,OAAO,CAAC;EAE9B,KAAK,MAAM5M,GAAG,IAAIgN,KAAK,EAAE;IACvB,MAAME,IAAI,GAAGP,OAAO,CAAC3M,GAAG,CAAC;IAEzB,IAAI,CAACiN,KAAK,CAACjC,QAAQ,CAAChL,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;;IAGd,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,MAAMmN,IAAI,GAAGP,OAAO,CAAC5M,GAAG,CAAC;MAEzB,IACGnC,YAAY,CAACqP,IAAI,CAAC,IAAIrP,YAAY,CAACsP,IAAI,CAAC,IACxCjP,QAAQ,CAACgP,IAAI,CAAC,IAAIhP,QAAQ,CAACiP,IAAI,CAAE,IACjChP,KAAK,CAACC,OAAO,CAAC8O,IAAI,CAAC,IAAI/O,KAAK,CAACC,OAAO,CAAC+O,IAAI,CAAC,GACvC,CAACT,SAAS,CAACQ,IAAI,EAAEC,IAAI,EAAEN,iBAAiB,IACxCK,IAAI,KAAKC,IAAI,EACjB;QACA,OAAO,KAAK;;;;EAKlB,OAAO,IAAI;AACb;AClDA,IAAAC,aAAA,GAAgBtP,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAI,CAACkG,MAAM,CAACqF,IAAI,CAACvL,KAAK,CAAC,CAACyD,MAAM;ACH/C,IAAA8L,WAAA,GAAgB1P,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,MAAM;ACHzB,IAAA0P,UAAA,GAAgBxP,KAAc,IAC5B,OAAOA,KAAK,KAAK,UAAU;ACC7B,IAAAyP,aAAA,GAAgBzP,KAAc,IAA0B;EACtD,IAAI,CAACwB,KAAK,EAAE;IACV,OAAO,KAAK;;EAGd,MAAMkO,KAAK,GAAG1P,KAAK,GAAKA,KAAqB,CAAC2P,aAA0B,GAAG,CAAC;EAC5E,OACE3P,KAAK,aACJ0P,KAAK,IAAIA,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAAClO,WAAW,GAAGA,WAAW,CAAC;AAE9E,CAAC;ACVD,IAAAmO,gBAAA,GAAgBhQ,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,iBAAiB;ACDpC,IAAAgQ,YAAA,GAAgBjQ,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,OAAO;ACE1B,IAAAiQ,iBAAA,GAAgB/F,GAAiB,IAC/B8F,YAAY,CAAC9F,GAAG,CAAC,IAAIpK,eAAe,CAACoK,GAAG,CAAC;ACF3C,IAAAgG,IAAA,GAAgBhG,GAAQ,IAAKyF,aAAa,CAACzF,GAAG,CAAC,IAAIA,GAAG,CAACiG,WAAW;ACElE,SAASC,OAAOA,CAAClN,MAAW,EAAEmN,UAA+B;EAC3D,MAAM1M,MAAM,GAAG0M,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC3M,MAAM;EAC7C,IAAIF,KAAK,GAAG,CAAC;EAEb,OAAOA,KAAK,GAAGE,MAAM,EAAE;IACrBT,MAAM,GAAGX,WAAW,CAACW,MAAM,CAAC,GAAGO,KAAK,EAAE,GAAGP,MAAM,CAACmN,UAAU,CAAC5M,KAAK,EAAE,CAAC,CAAC;;EAGtE,OAAOP,MAAM;AACf;AAEA,SAASqN,YAAYA,CAAChF,GAAc;EAClC,KAAK,MAAMnJ,GAAG,IAAImJ,GAAG,EAAE;IACrB,IAAIA,GAAG,CAAC9J,cAAc,CAACW,GAAG,CAAC,IAAI,CAACG,WAAW,CAACgJ,GAAG,CAACnJ,GAAG,CAAC,CAAC,EAAE;MACrD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEc,SAAUoO,KAAKA,CAACtN,MAAW,EAAEC,IAAkC;EAC3E,MAAMsN,KAAK,GAAGlQ,KAAK,CAACC,OAAO,CAAC2C,IAAI,IAC5BA,IAAA,GACAd,KAAK,CAACc,IAAI,IACR,CAACA,IAAI,IACLN,YAAY,CAACM,IAAI,CAAC;EAExB,MAAMuN,WAAW,GAAGD,KAAK,CAAC9M,MAAM,KAAK,CAAC,GAAGT,MAAM,GAAGkN,OAAO,CAAClN,MAAM,EAAEuN,KAAK,CAAC;EAExE,MAAMhN,KAAK,GAAGgN,KAAK,CAAC9M,MAAM,GAAG,CAAC;EAC9B,MAAMvB,GAAG,GAAGqO,KAAK,CAAChN,KAAK,CAAC;EAExB,IAAIiN,WAAW,EAAE;IACf,OAAOA,WAAW,CAACtO,GAAG,CAAC;;EAGzB,IACEqB,KAAK,KAAK,CAAC,KACTnD,QAAQ,CAACoQ,WAAW,CAAC,IAAIlB,aAAa,CAACkB,WAAW,CAAC,IAClDnQ,KAAK,CAACC,OAAO,CAACkQ,WAAW,CAAC,IAAIH,YAAY,CAACG,WAAW,CAAE,CAAC,EAC5D;IACAF,KAAK,CAACtN,MAAM,EAAEuN,KAAK,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;EAGnC,OAAOpN,MAAM;AACf;ACjDA,IAAAyN,iBAAA,GAAmB5O,IAAO,IAAa;EACrC,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;IACtB,IAAI2N,UAAU,CAAC3N,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;;;EAGf,OAAO,KAAK;AACd,CAAC;ACFD,SAASwO,eAAeA,CAAI7O,IAAO,EAAE8O,MAAA,GAA8B,EAAE;EACnE,MAAMC,iBAAiB,GAAGvQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAI+O,iBAAiB,EAAE;IACvC,KAAK,MAAM1O,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAACuO,iBAAiB,CAAC5O,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACAyO,MAAM,CAACzO,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAChDwO,eAAe,CAAC7O,IAAI,CAACK,GAAG,CAAC,EAAEyO,MAAM,CAACzO,GAAG,CAAC,CAAC;aAClC,IAAI,CAAChC,iBAAiB,CAAC2B,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;QACxCyO,MAAM,CAACzO,GAAG,CAAC,GAAG,IAAI;;;;EAKxB,OAAOyO,MAAM;AACf;AAEA,SAASE,+BAA+BA,CACtChP,IAAO,EACPsG,UAAa,EACb2I,qBAGC;EAED,MAAMF,iBAAiB,GAAGvQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAI+O,iBAAiB,EAAE;IACvC,KAAK,MAAM1O,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAACuO,iBAAiB,CAAC5O,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACA,IACEG,WAAW,CAAC8F,UAAU,CAAC,IACvBwG,WAAW,CAACmC,qBAAqB,CAAC5O,GAAG,CAAC,CAAC,EACvC;UACA4O,qBAAqB,CAAC5O,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,IAChDwO,eAAe,CAAC7O,IAAI,CAACK,GAAG,CAAC,EAAE,EAAE,IAC7B;YAAE,GAAGwO,eAAe,CAAC7O,IAAI,CAACK,GAAG,CAAC;UAAC,CAAE;eAChC;UACL2O,+BAA+B,CAC7BhP,IAAI,CAACK,GAAG,CAAC,EACThC,iBAAiB,CAACiI,UAAU,CAAC,GAAG,EAAE,GAAGA,UAAU,CAACjG,GAAG,CAAC,EACpD4O,qBAAqB,CAAC5O,GAAG,CAAC,CAC3B;;aAEE;QACL4O,qBAAqB,CAAC5O,GAAG,CAAC,GAAG,CAAC0M,SAAS,CAAC/M,IAAI,CAACK,GAAG,CAAC,EAAEiG,UAAU,CAACjG,GAAG,CAAC,CAAC;;;;EAKzE,OAAO4O,qBAAqB;AAC9B;AAEA,IAAAC,cAAA,GAAeA,CAAI/K,aAAgB,EAAEmC,UAAa,KAChD0I,+BAA+B,CAC7B7K,aAAa,EACbmC,UAAU,EACVuI,eAAe,CAACvI,UAAU,CAAC,CAC5B;AChEH,MAAM6I,aAAa,GAAwB;EACzChR,KAAK,EAAE,KAAK;EACZyH,OAAO,EAAE;CACV;AAED,MAAMwJ,WAAW,GAAG;EAAEjR,KAAK,EAAE,IAAI;EAAEyH,OAAO,EAAE;AAAI,CAAE;AAElD,IAAAyJ,gBAAA,GAAgBC,OAA4B,IAAyB;EACnE,IAAI9Q,KAAK,CAACC,OAAO,CAAC6Q,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAAC1N,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMqF,MAAM,GAAGqI,OAAA,CACZ1O,MAAM,CAAE2O,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAAC1Q,OAAO,IAAI,CAAC0Q,MAAM,CAACxK,QAAQ,EAC/D2B,GAAG,CAAE6I,MAAM,IAAKA,MAAM,CAACpR,KAAK,CAAC;MAChC,OAAO;QAAEA,KAAK,EAAE8I,MAAM;QAAErB,OAAO,EAAE,CAAC,CAACqB,MAAM,CAACrF;MAAM,CAAE;;IAGpD,OAAO0N,OAAO,CAAC,CAAC,CAAC,CAACzQ,OAAO,IAAI,CAACyQ,OAAO,CAAC,CAAC,CAAC,CAACvK,QAAA;IACvC;IACEuK,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,IAAI,CAAChP,WAAW,CAAC8O,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,CAACrR,KAAK,IAC/DqC,WAAW,CAAC8O,OAAO,CAAC,CAAC,CAAC,CAACnR,KAAK,CAAC,IAAImR,OAAO,CAAC,CAAC,CAAC,CAACnR,KAAK,KAAK,KACpDiR,WAAA,GACA;MAAEjR,KAAK,EAAEmR,OAAO,CAAC,CAAC,CAAC,CAACnR,KAAK;MAAEyH,OAAO,EAAE;IAAI,IAC1CwJ,WAAA,GACFD,aAAa;;EAGnB,OAAOA,aAAa;AACtB,CAAC;AC9BD,IAAAM,eAAA,GAAeA,CACbtR,KAAQ,EACR;EAAEuR,aAAa;EAAEC,WAAW;EAAEC;AAAU,CAAe,KAEvDpP,WAAW,CAACrC,KAAK,IACbA,KAAA,GACAuR,aAAA,GACEvR,KAAK,KAAK,KACR0R,GAAA,GACA1R,KAAA,GACE,CAACA,KAAA,GACDA,KAAA,GACJwR,WAAW,IAAIxJ,QAAQ,CAAChI,KAAK,IAC3B,IAAIC,IAAI,CAACD,KAAK,IACdyR,UAAA,GACEA,UAAU,CAACzR,KAAK,IAChBA,KAAK;ACfjB,MAAM2R,aAAa,GAAqB;EACtClK,OAAO,EAAE,KAAK;EACdzH,KAAK,EAAE;CACR;AAED,IAAA4R,aAAA,GAAgBT,OAA4B,IAC1C9Q,KAAK,CAACC,OAAO,CAAC6Q,OAAO,IACjBA,OAAO,CAAC/N,MAAM,CACZ,CAACyO,QAAQ,EAAET,MAAM,KACfA,MAAM,IAAIA,MAAM,CAAC1Q,OAAO,IAAI,CAAC0Q,MAAM,CAACxK,QAAA,GAChC;EACEa,OAAO,EAAE,IAAI;EACbzH,KAAK,EAAEoR,MAAM,CAACpR;AACf,IACD6R,QAAQ,EACdF,aAAa,IAEfA,aAAa;ACXL,SAAUG,aAAaA,CAAC1H,EAAe;EACnD,MAAMJ,GAAG,GAAGI,EAAE,CAACJ,GAAG;EAElB,IAAIuF,WAAW,CAACvF,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAAC+H,KAAK;;EAGlB,IAAIjC,YAAY,CAAC9F,GAAG,CAAC,EAAE;IACrB,OAAO4H,aAAa,CAACxH,EAAE,CAAC4H,IAAI,CAAC,CAAChS,KAAK;;EAGrC,IAAI6P,gBAAgB,CAAC7F,GAAG,CAAC,EAAE;IACzB,OAAO,CAAC,GAAGA,GAAG,CAACiI,eAAe,CAAC,CAAC1J,GAAG,CAAC,CAAC;MAAEvI;IAAK,CAAE,KAAKA,KAAK,CAAC;;EAG3D,IAAIJ,eAAU,CAACoK,GAAG,CAAC,EAAE;IACnB,OAAOkH,gBAAgB,CAAC9G,EAAE,CAAC4H,IAAI,CAAC,CAAChS,KAAK;;EAGxC,OAAOsR,eAAe,CAACjP,WAAW,CAAC2H,GAAG,CAAChK,KAAK,CAAC,GAAGoK,EAAE,CAACJ,GAAG,CAAChK,KAAK,GAAGgK,GAAG,CAAChK,KAAK,EAAEoK,EAAE,CAAC;AAC/E;ACpBA,IAAA8H,kBAAA,GAAeA,CACbC,WAAyD,EACzDhI,OAAkB,EAClBiI,YAA2B,EAC3BC,yBAA+C,KAC7C;EACF,MAAM1B,MAAM,GAA2C,EAAE;EAEzD,KAAK,MAAM/P,IAAI,IAAIuR,WAAW,EAAE;IAC9B,MAAMjI,KAAK,GAAUnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IAEvCsJ,KAAK,IAAI5G,GAAG,CAACqN,MAAM,EAAE/P,IAAI,EAAEsJ,KAAK,CAACE,EAAE,CAAC;;EAGtC,OAAO;IACLgI,YAAY;IACZpR,KAAK,EAAE,CAAC,GAAGmR,WAAW,CAA8B;IACpDxB,MAAM;IACN0B;GACD;AACH,CAAC;AC/BD,IAAAC,OAAA,GAAgBtS,KAAc,IAAsBA,KAAK,YAAYuS,MAAM;ACS3E,IAAAC,YAAA,GACEC,IAAoD,IAEpDpQ,WAAW,CAACoQ,IAAI,IACZA,IAAA,GACAH,OAAO,CAACG,IAAI,IACVA,IAAI,CAACC,MAAA,GACLtS,QAAQ,CAACqS,IAAI,IACXH,OAAO,CAACG,IAAI,CAACzS,KAAK,IAChByS,IAAI,CAACzS,KAAK,CAAC0S,MAAA,GACXD,IAAI,CAACzS,KAAA,GACPyS,IAAI;ACjBd,IAAAE,kBAAA,GAAgBC,IAAW,KAA2B;EACpDC,UAAU,EAAE,CAACD,IAAI,IAAIA,IAAI,KAAK1O,eAAe,CAACG,QAAQ;EACtDyO,QAAQ,EAAEF,IAAI,KAAK1O,eAAe,CAACC,MAAM;EACzC4O,UAAU,EAAEH,IAAI,KAAK1O,eAAe,CAACE,QAAQ;EAC7C4O,OAAO,EAAEJ,IAAI,KAAK1O,eAAe,CAACK,GAAG;EACrC0O,SAAS,EAAEL,IAAI,KAAK1O,eAAe,CAACI;AACrC,EAAC;ACLF,MAAM4O,cAAc,GAAG,eAAe;AAEtC,IAAAC,oBAAA,GAAgBC,cAA2B,IACzC,CAAC,CAACA,cAAc,IAChB,CAAC,CAACA,cAAc,CAACrO,QAAQ,IACzB,CAAC,EACEyK,UAAU,CAAC4D,cAAc,CAACrO,QAAQ,CAAC,IAClCqO,cAAc,CAACrO,QAAQ,CAAC1D,WAAW,CAACT,IAAI,KAAKsS,cAAc,IAC5D9S,QAAQ,CAACgT,cAAc,CAACrO,QAAQ,CAAC,IAChCmB,MAAM,CAAC4C,MAAM,CAACsK,cAAc,CAACrO,QAAQ,CAAC,CAACsO,IAAI,CACxCC,gBAA4C,IAC3CA,gBAAgB,CAACjS,WAAW,CAACT,IAAI,KAAKsS,cAAc,CACtD,CACL;ACfH,IAAAK,aAAA,GAAgBpC,OAAoB,IAClCA,OAAO,CAACtG,KAAK,KACZsG,OAAO,CAACrM,QAAQ,IACfqM,OAAO,CAACzM,GAAG,IACXyM,OAAO,CAAC1M,GAAG,IACX0M,OAAO,CAACxM,SAAS,IACjBwM,OAAO,CAACvM,SAAS,IACjBuM,OAAO,CAACtM,OAAO,IACfsM,OAAO,CAACpM,QAAQ,CAAC;ACRrB,IAAAyO,SAAA,GAAeA,CACb5S,IAAuB,EACvBsH,MAAa,EACbuL,WAAqB,KAErB,CAACA,WAAW,KACXvL,MAAM,CAACO,QAAQ,IACdP,MAAM,CAACG,KAAK,CAACpH,GAAG,CAACL,IAAI,CAAC,IACtB,CAAC,GAAGsH,MAAM,CAACG,KAAK,CAAC,CAAC4E,IAAI,CACnByG,SAAS,IACR9S,IAAI,CAAC+S,UAAU,CAACD,SAAS,CAAC,IAC1B,QAAQ,CAACtR,IAAI,CAACxB,IAAI,CAACwP,KAAK,CAACsD,SAAS,CAACjQ,MAAM,CAAC,CAAC,CAC9C,CAAC;ACVN,MAAMmQ,qBAAqB,GAAGA,CAC5BjD,MAAiB,EACjB5F,MAAwD,EACxDoH,WAA8D,EAC9D0B,UAAoB,KAClB;EACF,KAAK,MAAM3R,GAAG,IAAIiQ,WAAW,IAAIjM,MAAM,CAACqF,IAAI,CAACoF,MAAM,CAAC,EAAE;IACpD,MAAMzG,KAAK,GAAGnH,GAAG,CAAC4N,MAAM,EAAEzO,GAAG,CAAC;IAE9B,IAAIgI,KAAK,EAAE;MACT,MAAM;QAAEE,EAAE;QAAE,GAAG0J;MAAY,CAAE,GAAG5J,KAAK;MAErC,IAAIE,EAAE,EAAE;QACN,IAAIA,EAAE,CAAC4H,IAAI,IAAI5H,EAAE,CAAC4H,IAAI,CAAC,CAAC,CAAC,IAAIjH,MAAM,CAACX,EAAE,CAAC4H,IAAI,CAAC,CAAC,CAAC,EAAE9P,GAAG,CAAC,IAAI,CAAC2R,UAAU,EAAE;UACnE,OAAO,IAAI;eACN,IAAIzJ,EAAE,CAACJ,GAAG,IAAIe,MAAM,CAACX,EAAE,CAACJ,GAAG,EAAEI,EAAE,CAACxJ,IAAI,CAAC,IAAI,CAACiT,UAAU,EAAE;UAC3D,OAAO,IAAI;eACN;UACL,IAAID,qBAAqB,CAACE,YAAY,EAAE/I,MAAM,CAAC,EAAE;YAC/C;;;aAGC,IAAI3K,QAAQ,CAAC0T,YAAY,CAAC,EAAE;QACjC,IAAIF,qBAAqB,CAACE,YAAyB,EAAE/I,MAAM,CAAC,EAAE;UAC5D;;;;;EAKR;AACF,CAAC;AC9Ba,SAAUgJ,iBAAiBA,CACvCrM,MAAsB,EACtByC,OAAoB,EACpBvJ,IAAY;EAKZ,MAAMkJ,KAAK,GAAG/G,GAAG,CAAC2E,MAAM,EAAE9G,IAAI,CAAC;EAE/B,IAAIkJ,KAAK,IAAI3H,KAAK,CAACvB,IAAI,CAAC,EAAE;IACxB,OAAO;MACLkJ,KAAK;MACLlJ;KACD;;EAGH,MAAMI,KAAK,GAAGJ,IAAI,CAACkC,KAAK,CAAC,GAAG,CAAC;EAE7B,OAAO9B,KAAK,CAACyC,MAAM,EAAE;IACnB,MAAM+E,SAAS,GAAGxH,KAAK,CAACgT,IAAI,CAAC,GAAG,CAAC;IACjC,MAAM9J,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAE3B,SAAS,CAAC;IACrC,MAAMyL,UAAU,GAAGlR,GAAG,CAAC2E,MAAM,EAAEc,SAAS,CAAC;IAEzC,IAAI0B,KAAK,IAAI,CAAC7J,KAAK,CAACC,OAAO,CAAC4J,KAAK,CAAC,IAAItJ,IAAI,KAAK4H,SAAS,EAAE;MACxD,OAAO;QAAE5H;MAAI,CAAE;;IAGjB,IAAIqT,UAAU,IAAIA,UAAU,CAACnU,IAAI,EAAE;MACjC,OAAO;QACLc,IAAI,EAAE4H,SAAS;QACfsB,KAAK,EAAEmK;OACR;;IAGH,IAAIA,UAAU,IAAIA,UAAU,CAACC,IAAI,IAAID,UAAU,CAACC,IAAI,CAACpU,IAAI,EAAE;MACzD,OAAO;QACLc,IAAI,EAAE,GAAG4H,SAAS,OAAO;QACzBsB,KAAK,EAAEmK,UAAU,CAACC;OACnB;;IAGHlT,KAAK,CAACmT,GAAG,EAAE;;EAGb,OAAO;IACLvT;GACD;AACH;AC3CA,IAAAwT,qBAAA,GAAeA,CACbC,aAGC,EACDhO,eAAkB,EAClBS,eAA2D,EAC3Df,MAAgB,KACd;EACFe,eAAe,CAACuN,aAAa,CAAC;EAC9B,MAAM;IAAEzT,IAAI;IAAE,GAAGgF;EAAS,CAAE,GAAGyO,aAAa;EAE5C,OACE/E,aAAa,CAAC1J,SAAS,CAAC,IACxBM,MAAM,CAACqF,IAAI,CAAC3F,SAAS,CAAC,CAACnC,MAAM,IAAIyC,MAAM,CAACqF,IAAI,CAAClF,eAAe,CAAC,CAAC5C,MAAM,IACpEyC,MAAM,CAACqF,IAAI,CAAC3F,SAAS,CAAC,CAACyN,IAAI,CACxBnR,GAAG,IACFmE,eAAe,CAACnE,GAA0B,CAAC,MAC1C,CAAC6D,MAAM,IAAI7B,eAAe,CAACK,GAAG,CAAC,CACnC;AAEL,CAAC;AC5BD,IAAA+P,qBAAA,GAAeA,CACb1T,IAAQ,EACR2T,UAAmB,EACnB1N,KAAe,KAEf,CAACjG,IAAI,IACL,CAAC2T,UAAU,IACX3T,IAAI,KAAK2T,UAAU,IACnBrG,qBAAqB,CAACtN,IAAI,CAAC,CAACqM,IAAI,CAC7BuH,WAAW,IACVA,WAAW,KACV3N,KAAA,GACG2N,WAAW,KAAKD,UAAA,GAChBC,WAAW,CAACb,UAAU,CAACY,UAAU,CAAC,IAClCA,UAAU,CAACZ,UAAU,CAACa,WAAW,CAAC,CAAC,CAC1C;ACfH,IAAAC,cAAA,GAAeA,CACbhB,WAAoB,EACpB5J,SAAkB,EAClB6K,WAAoB,EACpBC,cAGC,EACD/B,IAAkC,KAChC;EACF,IAAIA,IAAI,CAACI,OAAO,EAAE;IAChB,OAAO,KAAK;SACP,IAAI,CAAC0B,WAAW,IAAI9B,IAAI,CAACK,SAAS,EAAE;IACzC,OAAO,EAAEpJ,SAAS,IAAI4J,WAAW,CAAC;SAC7B,IAAIiB,WAAW,GAAGC,cAAc,CAAC7B,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;IAChE,OAAO,CAACW,WAAW;SACd,IAAIiB,WAAW,GAAGC,cAAc,CAAC5B,UAAU,GAAGH,IAAI,CAACG,UAAU,EAAE;IACpE,OAAOU,WAAW;;EAEpB,OAAO,IAAI;AACb,CAAC;AClBD,IAAAmB,eAAA,GAAeA,CAAI5K,GAAM,EAAEpJ,IAAY,KACrC,CAAC4B,OAAO,CAACO,GAAG,CAACiH,GAAG,EAAEpJ,IAAI,CAAC,CAAC,CAAC6C,MAAM,IAAI6M,KAAK,CAACtG,GAAG,EAAEpJ,IAAI,CAAC;ACKrD,IAAAiU,yBAAA,GAAeA,CACbnN,MAAsB,EACtBoC,KAA0C,EAC1ClJ,IAAuB,KACL;EAClB,MAAMkU,gBAAgB,GAAG5G,qBAAqB,CAACnL,GAAG,CAAC2E,MAAM,EAAE9G,IAAI,CAAC,CAAC;EACjE0C,GAAG,CAACwR,gBAAgB,EAAE,MAAM,EAAEhL,KAAK,CAAClJ,IAAI,CAAC,CAAC;EAC1C0C,GAAG,CAACoE,MAAM,EAAE9G,IAAI,EAAEkU,gBAAgB,CAAC;EACnC,OAAOpN,MAAM;AACf,CAAC;AChBD,IAAAqN,SAAA,GAAgB/U,KAAc,IAAuBgI,QAAQ,CAAChI,KAAK,CAAC;ACCtD,SAAUgV,gBAAgBA,CACtC7R,MAAsB,EACtB6G,GAAQ,EACRlK,IAAI,GAAG,UAAU;EAEjB,IACEiV,SAAS,CAAC5R,MAAM,CAAC,IAChB9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,IAAIA,MAAM,CAAC8R,KAAK,CAACF,SAAS,CAAE,IACjD1R,SAAS,CAACF,MAAM,CAAC,IAAI,CAACA,MAAO,EAC9B;IACA,OAAO;MACLrD,IAAI;MACJ0K,OAAO,EAAEuK,SAAS,CAAC5R,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;MACxC6G;KACD;;AAEL;AChBA,IAAAkL,kBAAA,GAAgBC,cAA+B,IAC7C/U,QAAQ,CAAC+U,cAAc,CAAC,IAAI,CAAC7C,OAAO,CAAC6C,cAAc,IAC/CA,cAAA,GACA;EACEnV,KAAK,EAAEmV,cAAc;EACrB3K,OAAO,EAAE;CACV;ACuBP,IAAA4K,aAAA,GAAe,MAAAA,CACblL,KAAY,EACZmL,kBAAmC,EACnClN,UAAa,EACb6F,wBAAiC,EACjCqE,yBAAmC,EACnCiD,YAAsB,KACU;EAChC,MAAM;IACJtL,GAAG;IACHgI,IAAI;IACJlN,QAAQ;IACRH,SAAS;IACTC,SAAS;IACTF,GAAG;IACHD,GAAG;IACHI,OAAO;IACPE,QAAQ;IACRnE,IAAI;IACJ2Q,aAAa;IACb1G;EAAK,CACN,GAAGX,KAAK,CAACE,EAAE;EACZ,MAAMmL,UAAU,GAAqBxS,GAAG,CAACoF,UAAU,EAAEvH,IAAI,CAAC;EAC1D,IAAI,CAACiK,KAAK,IAAIwK,kBAAkB,CAACpU,GAAG,CAACL,IAAI,CAAC,EAAE;IAC1C,OAAO,EAAE;;EAEX,MAAM4U,QAAQ,GAAqBxD,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAIhI,GAAwB;EAC7E,MAAMO,iBAAiB,GAAIC,OAA0B,IAAI;IACvD,IAAI6H,yBAAyB,IAAImD,QAAQ,CAAC/K,cAAc,EAAE;MACxD+K,QAAQ,CAACjL,iBAAiB,CAAClH,SAAS,CAACmH,OAAO,CAAC,GAAG,EAAE,GAAGA,OAAO,IAAI,EAAE,CAAC;MACnEgL,QAAQ,CAAC/K,cAAc,EAAE;;EAE7B,CAAC;EACD,MAAMX,KAAK,GAAwB,EAAE;EACrC,MAAM2L,OAAO,GAAG3F,YAAY,CAAC9F,GAAG,CAAC;EACjC,MAAM0L,UAAU,GAAG9V,eAAe,CAACoK,GAAG,CAAC;EACvC,MAAM+F,iBAAiB,GAAG0F,OAAO,IAAIC,UAAU;EAC/C,MAAMC,OAAO,GACV,CAACpE,aAAa,IAAIhC,WAAW,CAACvF,GAAG,CAAC,KACjC3H,WAAW,CAAC2H,GAAG,CAAChK,KAAK,CAAC,IACtBqC,WAAW,CAACkT,UAAU,CAAC,IACxB9F,aAAa,CAACzF,GAAG,CAAC,IAAIA,GAAG,CAAChK,KAAK,KAAK,EAAG,IACxCuV,UAAU,KAAK,EAAE,IAChBlV,KAAK,CAACC,OAAO,CAACiV,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC9R,MAAO;EACnD,MAAMmS,iBAAiB,GAAG7H,YAAY,CAAC8H,IAAI,CACzC,IAAI,EACJjV,IAAI,EACJoN,wBAAwB,EACxBlE,KAAK,CACN;EACD,MAAMgM,gBAAgB,GAAGA,CACvBC,SAAkB,EAClBC,gBAAyB,EACzBC,gBAAyB,EACzBC,OAAA,GAAmB1R,sBAAsB,CAACG,SAAS,EACnDwR,OAAA,GAAmB3R,sBAAsB,CAACI,SAAS,KACjD;IACF,MAAM4F,OAAO,GAAGuL,SAAS,GAAGC,gBAAgB,GAAGC,gBAAgB;IAC/DnM,KAAK,CAAClJ,IAAI,CAAC,GAAG;MACZd,IAAI,EAAEiW,SAAS,GAAGG,OAAO,GAAGC,OAAO;MACnC3L,OAAO;MACPR,GAAG;MACH,GAAG4L,iBAAiB,CAACG,SAAS,GAAGG,OAAO,GAAGC,OAAO,EAAE3L,OAAO;KAC5D;EACH,CAAC;EAED,IACE8K,YAAA,GACI,CAACjV,KAAK,CAACC,OAAO,CAACiV,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC9R,MAAA,GAC1CqB,QAAQ,KACN,CAACiL,iBAAiB,KAAK4F,OAAO,IAAIzV,iBAAiB,CAACqV,UAAU,CAAC,CAAC,IAC/DlS,SAAS,CAACkS,UAAU,CAAC,IAAI,CAACA,UAAW,IACrCG,UAAU,IAAI,CAACxE,gBAAgB,CAACc,IAAI,CAAC,CAACvK,OAAQ,IAC9CgO,OAAO,IAAI,CAAC7D,aAAa,CAACI,IAAI,CAAC,CAACvK,OAAQ,CAAC,EAChD;IACA,MAAM;MAAEzH,KAAK;MAAEwK;IAAO,CAAE,GAAGuK,SAAS,CAACjQ,QAAQ,IACzC;MAAE9E,KAAK,EAAE,CAAC,CAAC8E,QAAQ;MAAE0F,OAAO,EAAE1F;IAAQ,IACtCoQ,kBAAkB,CAACpQ,QAAQ,CAAC;IAEhC,IAAI9E,KAAK,EAAE;MACT8J,KAAK,CAAClJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE0E,sBAAsB,CAACM,QAAQ;QACrC0F,OAAO;QACPR,GAAG,EAAEwL,QAAQ;QACb,GAAGI,iBAAiB,CAACpR,sBAAsB,CAACM,QAAQ,EAAE0F,OAAO;OAC9D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAI,CAAC6L,OAAO,KAAK,CAACzV,iBAAiB,CAACwE,GAAG,CAAC,IAAI,CAACxE,iBAAiB,CAACuE,GAAG,CAAC,CAAC,EAAE;IACpE,IAAIsR,SAAS;IACb,IAAIK,SAAS;IACb,MAAMC,SAAS,GAAGnB,kBAAkB,CAACzQ,GAAG,CAAC;IACzC,MAAM6R,SAAS,GAAGpB,kBAAkB,CAACxQ,GAAG,CAAC;IAEzC,IAAI,CAACxE,iBAAiB,CAACqV,UAAU,CAAC,IAAI,CAAC1R,KAAK,CAAC0R,UAAoB,CAAC,EAAE;MAClE,MAAMgB,WAAW,GACdvM,GAAwB,CAACuH,aAAa,KACtCgE,UAAU,GAAG,CAACA,UAAU,GAAGA,UAAU,CAAC;MACzC,IAAI,CAACrV,iBAAiB,CAACmW,SAAS,CAACrW,KAAK,CAAC,EAAE;QACvC+V,SAAS,GAAGQ,WAAW,GAAGF,SAAS,CAACrW,KAAK;;MAE3C,IAAI,CAACE,iBAAiB,CAACoW,SAAS,CAACtW,KAAK,CAAC,EAAE;QACvCoW,SAAS,GAAGG,WAAW,GAAGD,SAAS,CAACtW,KAAK;;WAEtC;MACL,MAAMwW,SAAS,GACZxM,GAAwB,CAACwH,WAAW,IAAI,IAAIvR,IAAI,CAACsV,UAAoB,CAAC;MACzE,MAAMkB,iBAAiB,GAAIC,IAAa,IACtC,IAAIzW,IAAI,CAAC,IAAIA,IAAI,EAAE,CAAC0W,YAAY,EAAE,GAAG,GAAG,GAAGD,IAAI,CAAC;MAClD,MAAME,MAAM,GAAG5M,GAAG,CAAClK,IAAI,IAAI,MAAM;MACjC,MAAM+W,MAAM,GAAG7M,GAAG,CAAClK,IAAI,IAAI,MAAM;MAEjC,IAAIkI,QAAQ,CAACqO,SAAS,CAACrW,KAAK,CAAC,IAAIuV,UAAU,EAAE;QAC3CQ,SAAS,GAAGa,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACJ,SAAS,CAACrW,KAAK,IACjE6W,MAAA,GACEtB,UAAU,GAAGc,SAAS,CAACrW,KAAA,GACvBwW,SAAS,GAAG,IAAIvW,IAAI,CAACoW,SAAS,CAACrW,KAAK,CAAC;;MAG7C,IAAIgI,QAAQ,CAACsO,SAAS,CAACtW,KAAK,CAAC,IAAIuV,UAAU,EAAE;QAC3Ca,SAAS,GAAGQ,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACH,SAAS,CAACtW,KAAK,IACjE6W,MAAA,GACEtB,UAAU,GAAGe,SAAS,CAACtW,KAAA,GACvBwW,SAAS,GAAG,IAAIvW,IAAI,CAACqW,SAAS,CAACtW,KAAK,CAAC;;;IAI/C,IAAI+V,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACd,CAAC,CAACC,SAAS,EACXM,SAAS,CAAC7L,OAAO,EACjB8L,SAAS,CAAC9L,OAAO,EACjBhG,sBAAsB,CAACC,GAAG,EAC1BD,sBAAsB,CAACE,GAAG,CAC3B;MACD,IAAI,CAACsJ,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAAClJ,IAAI,CAAE,CAAC4J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IACE,CAACnF,SAAS,IAAIC,SAAS,KACvB,CAAC+Q,OAAO,KACP3N,QAAQ,CAACuN,UAAU,CAAC,IAAKD,YAAY,IAAIjV,KAAK,CAACC,OAAO,CAACiV,UAAU,CAAE,CAAC,EACrE;IACA,MAAMuB,eAAe,GAAG5B,kBAAkB,CAACvQ,SAAS,CAAC;IACrD,MAAMoS,eAAe,GAAG7B,kBAAkB,CAACtQ,SAAS,CAAC;IACrD,MAAMmR,SAAS,GACb,CAAC7V,iBAAiB,CAAC4W,eAAe,CAAC9W,KAAK,CAAC,IACzCuV,UAAU,CAAC9R,MAAM,GAAG,CAACqT,eAAe,CAAC9W,KAAK;IAC5C,MAAMoW,SAAS,GACb,CAAClW,iBAAiB,CAAC6W,eAAe,CAAC/W,KAAK,CAAC,IACzCuV,UAAU,CAAC9R,MAAM,GAAG,CAACsT,eAAe,CAAC/W,KAAK;IAE5C,IAAI+V,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACdC,SAAS,EACTe,eAAe,CAACtM,OAAO,EACvBuM,eAAe,CAACvM,OAAO,CACxB;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAAClJ,IAAI,CAAE,CAAC4J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IAAIjF,OAAO,IAAI,CAAC8Q,OAAO,IAAI3N,QAAQ,CAACuN,UAAU,CAAC,EAAE;IAC/C,MAAM;MAAEvV,KAAK,EAAEgX,YAAY;MAAExM;IAAO,CAAE,GAAG0K,kBAAkB,CAACrQ,OAAO,CAAC;IAEpE,IAAIyN,OAAO,CAAC0E,YAAY,CAAC,IAAI,CAACzB,UAAU,CAAC0B,KAAK,CAACD,YAAY,CAAC,EAAE;MAC5DlN,KAAK,CAAClJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE0E,sBAAsB,CAACK,OAAO;QACpC2F,OAAO;QACPR,GAAG;QACH,GAAG4L,iBAAiB,CAACpR,sBAAsB,CAACK,OAAO,EAAE2F,OAAO;OAC7D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAI/E,QAAQ,EAAE;IACZ,IAAIyK,UAAU,CAACzK,QAAQ,CAAC,EAAE;MACxB,MAAM5B,MAAM,GAAG,MAAM4B,QAAQ,CAACwQ,UAAU,EAAEpN,UAAU,CAAC;MACrD,MAAM+O,aAAa,GAAGlC,gBAAgB,CAAC7R,MAAM,EAAEqS,QAAQ,CAAC;MAExD,IAAI0B,aAAa,EAAE;QACjBpN,KAAK,CAAClJ,IAAI,CAAC,GAAG;UACZ,GAAGsW,aAAa;UAChB,GAAGtB,iBAAiB,CAClBpR,sBAAsB,CAACO,QAAQ,EAC/BmS,aAAa,CAAC1M,OAAO;SAExB;QACD,IAAI,CAACwD,wBAAwB,EAAE;UAC7BzD,iBAAiB,CAAC2M,aAAa,CAAC1M,OAAO,CAAC;UACxC,OAAOV,KAAK;;;WAGX,IAAI1J,QAAQ,CAAC2E,QAAQ,CAAC,EAAE;MAC7B,IAAIoS,gBAAgB,GAAG,EAAgB;MAEvC,KAAK,MAAMjV,GAAG,IAAI6C,QAAQ,EAAE;QAC1B,IAAI,CAACuK,aAAa,CAAC6H,gBAAgB,CAAC,IAAI,CAACnJ,wBAAwB,EAAE;UACjE;;QAGF,MAAMkJ,aAAa,GAAGlC,gBAAgB,CACpC,MAAMjQ,QAAQ,CAAC7C,GAAG,CAAC,CAACqT,UAAU,EAAEpN,UAAU,CAAC,EAC3CqN,QAAQ,EACRtT,GAAG,CACJ;QAED,IAAIgV,aAAa,EAAE;UACjBC,gBAAgB,GAAG;YACjB,GAAGD,aAAa;YAChB,GAAGtB,iBAAiB,CAAC1T,GAAG,EAAEgV,aAAa,CAAC1M,OAAO;WAChD;UAEDD,iBAAiB,CAAC2M,aAAa,CAAC1M,OAAO,CAAC;UAExC,IAAIwD,wBAAwB,EAAE;YAC5BlE,KAAK,CAAClJ,IAAI,CAAC,GAAGuW,gBAAgB;;;;MAKpC,IAAI,CAAC7H,aAAa,CAAC6H,gBAAgB,CAAC,EAAE;QACpCrN,KAAK,CAAClJ,IAAI,CAAC,GAAG;UACZoJ,GAAG,EAAEwL,QAAQ;UACb,GAAG2B;SACJ;QACD,IAAI,CAACnJ,wBAAwB,EAAE;UAC7B,OAAOlE,KAAK;;;;;EAMpBS,iBAAiB,CAAC,IAAI,CAAC;EACvB,OAAOT,KAAK;AACd,CAAC;ACpMD,MAAMsN,cAAc,GAAG;EACrBxE,IAAI,EAAE1O,eAAe,CAACG,QAAQ;EAC9BsQ,cAAc,EAAEzQ,eAAe,CAACE,QAAQ;EACxCiT,gBAAgB,EAAE;CACV;AAEJ,SAAUC,iBAAiBA,CAK/B/R,KAAA,GAAkE,EAAE;EAUpE,IAAIoF,QAAQ,GAAG;IACb,GAAGyM,cAAc;IACjB,GAAG7R;GACJ;EACD,IAAIyB,UAAU,GAA4B;IACxCuQ,WAAW,EAAE,CAAC;IACdpQ,OAAO,EAAE,KAAK;IACdqQ,OAAO,EAAE,KAAK;IACdpQ,SAAS,EAAEoI,UAAU,CAAC7E,QAAQ,CAAC3E,aAAa,CAAC;IAC7CwB,YAAY,EAAE,KAAK;IACnBkN,WAAW,EAAE,KAAK;IAClB+C,YAAY,EAAE,KAAK;IACnB9J,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACdH,aAAa,EAAE,EAAE;IACjBD,WAAW,EAAE,EAAE;IACfE,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEiD,QAAQ,CAACjD,MAAM,IAAI,EAAE;IAC7Bd,QAAQ,EAAE+D,QAAQ,CAAC/D,QAAQ,IAAI;GAChC;EACD,IAAIuD,OAAO,GAAc,EAAE;EAC3B,IAAIlE,cAAc,GAChB7F,QAAQ,CAACuK,QAAQ,CAAC3E,aAAa,CAAC,IAAI5F,QAAQ,CAACuK,QAAQ,CAAC7B,MAAM,IACxDlH,WAAW,CAAC+I,QAAQ,CAAC3E,aAAa,IAAI2E,QAAQ,CAAC7B,MAAM,CAAC,IAAI,KAC1D,EAAE;EACR,IAAIC,WAAW,GAAG4B,QAAQ,CAACzB,gBAAA,GACtB,KACAtH,WAAW,CAACqE,cAAc,CAAkB;EACjD,IAAI6E,MAAM,GAAG;IACXC,MAAM,EAAE,KAAK;IACbF,KAAK,EAAE,KAAK;IACZxC,KAAK,EAAE;GACR;EACD,IAAIH,MAAM,GAAU;IAClB2C,KAAK,EAAE,IAAI6M,GAAG,EAAE;IAChB9Q,QAAQ,EAAE,IAAI8Q,GAAG,EAAE;IACnBC,OAAO,EAAE,IAAID,GAAG,EAAE;IAClBtO,KAAK,EAAE,IAAIsO,GAAG,EAAE;IAChBrP,KAAK,EAAE,IAAIqP,GAAG;GACf;EACD,IAAIE,kBAAwC;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMxR,eAAe,GAAkB;IACrCc,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBE,gBAAgB,EAAE,KAAK;IACvBD,aAAa,EAAE,KAAK;IACpBE,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;GACT;EACD,IAAIoQ,wBAAwB,GAAG;IAC7B,GAAGzR;GACJ;EACD,MAAMmH,SAAS,GAA2B;IACxCpE,KAAK,EAAE+E,aAAa,EAAE;IACtBV,KAAK,EAAEU,aAAa;GACrB;EAED,MAAM4J,gCAAgC,GACpCpN,QAAQ,CAACyH,YAAY,KAAKlO,eAAe,CAACK,GAAG;EAE/C,MAAMyT,QAAQ,GACSnQ,QAAW,IAC/BoQ,IAAY,IAAI;IACfC,YAAY,CAACL,KAAK,CAAC;IACnBA,KAAK,GAAGM,UAAU,CAACtQ,QAAQ,EAAEoQ,IAAI,CAAC;EACpC,CAAC;EAEH,MAAMnQ,SAAS,GAAG,MAAOsQ,iBAA2B,IAAI;IACtD,IACE,CAACzN,QAAQ,CAAC/D,QAAQ,KACjBP,eAAe,CAACoB,OAAO,IACtBqQ,wBAAwB,CAACrQ,OAAO,IAChC2Q,iBAAiB,CAAC,EACpB;MACA,MAAM3Q,OAAO,GAAGkD,QAAQ,CAAC0N,QAAA,GACrB/I,aAAa,CAAC,CAAC,MAAMgJ,UAAU,EAAE,EAAE5Q,MAAM,IACzC,MAAM6Q,wBAAwB,CAACpO,OAAO,EAAE,IAAI,CAAC;MAEjD,IAAI1C,OAAO,KAAKT,UAAU,CAACS,OAAO,EAAE;QAClC+F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnBjG;QACD,EAAC;;;EAGR,CAAC;EAED,MAAM+Q,mBAAmB,GAAGA,CAACxX,KAAgB,EAAEwG,YAAsB,KAAI;IACvE,IACE,CAACmD,QAAQ,CAAC/D,QAAQ,KACjBP,eAAe,CAACmB,YAAY,IAC3BnB,eAAe,CAACkB,gBAAgB,IAChCuQ,wBAAwB,CAACtQ,YAAY,IACrCsQ,wBAAwB,CAACvQ,gBAAgB,CAAC,EAC5C;MACA,CAACvG,KAAK,IAAIX,KAAK,CAACoY,IAAI,CAACvQ,MAAM,CAAC2C,KAAK,CAAC,EAAE6N,OAAO,CAAE9X,IAAI,IAAI;QACnD,IAAIA,IAAI,EAAE;UACR4G,YAAA,GACIlE,GAAG,CAAC0D,UAAU,CAACO,gBAAgB,EAAE3G,IAAI,EAAE4G,YAAY,IACnD8I,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAE3G,IAAI,CAAC;;MAEhD,CAAC,CAAC;MAEF4M,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBnG,gBAAgB,EAAEP,UAAU,CAACO,gBAAgB;QAC7CC,YAAY,EAAE,CAAC8H,aAAa,CAACtI,UAAU,CAACO,gBAAgB;MACzD,EAAC;;EAEN,CAAC;EAED,MAAMoR,cAAc,GAA0BA,CAC5C/X,IAAI,EACJkI,MAAM,GAAG,EAAE,EACXgD,MAAM,EACN8M,IAAI,EACJC,eAAe,GAAG,IAAI,EACtBC,0BAA0B,GAAG,IAAI,KAC/B;IACF,IAAIF,IAAI,IAAI9M,MAAM,IAAI,CAACnB,QAAQ,CAAC/D,QAAQ,EAAE;MACxCkE,MAAM,CAACC,MAAM,GAAG,IAAI;MACpB,IAAI+N,0BAA0B,IAAIzY,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC,CAAC,EAAE;QACnE,MAAMmY,WAAW,GAAGjN,MAAM,CAAC/I,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC,EAAEgY,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;QACpEJ,eAAe,IAAIvV,GAAG,CAAC6G,OAAO,EAAEvJ,IAAI,EAAEmY,WAAW,CAAC;;MAGpD,IACED,0BAA0B,IAC1BzY,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC,CAAC,EAC3C;QACA,MAAM8G,MAAM,GAAGoE,MAAM,CACnB/I,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC,EAC5BgY,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAIvV,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAE8G,MAAM,CAAC;QACvDkN,eAAe,CAAC5N,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;;MAG1C,IACE,CAACyF,eAAe,CAACiB,aAAa,IAC5BwQ,wBAAwB,CAACxQ,aAAa,KACxCwR,0BAA0B,IAC1BzY,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACiE,UAAU,CAACM,aAAa,EAAE1G,IAAI,CAAC,CAAC,EAClD;QACA,MAAM0G,aAAa,GAAGwE,MAAM,CAC1B/I,GAAG,CAACiE,UAAU,CAACM,aAAa,EAAE1G,IAAI,CAAC,EACnCgY,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAIvV,GAAG,CAAC0D,UAAU,CAACM,aAAa,EAAE1G,IAAI,EAAE0G,aAAa,CAAC;;MAGvE,IAAIjB,eAAe,CAACgB,WAAW,IAAIyQ,wBAAwB,CAACzQ,WAAW,EAAE;QACvEL,UAAU,CAACK,WAAW,GAAG0J,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC;;MAGtEyE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB9M,IAAI;QACJuG,OAAO,EAAE+R,SAAS,CAACtY,IAAI,EAAEkI,MAAM,CAAC;QAChCzB,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCK,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzBD,OAAO,EAAET,UAAU,CAACS;MACrB,EAAC;WACG;MACLnE,GAAG,CAACyF,WAAW,EAAEnI,IAAI,EAAEkI,MAAM,CAAC;;EAElC,CAAC;EAED,MAAMqQ,YAAY,GAAGA,CAACvY,IAAuB,EAAEkJ,KAAiB,KAAI;IAClExG,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAEkJ,KAAK,CAAC;IACnC0D,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;EACJ,CAAC;EAED,MAAM0R,UAAU,GAAI1R,MAAiC,IAAI;IACvDV,UAAU,CAACU,MAAM,GAAGA,MAAM;IAC1B8F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;EACJ,CAAC;EAED,MAAM4R,mBAAmB,GAAGA,CAC1BzY,IAAuB,EACvB0Y,oBAA6B,EAC7BtZ,KAAe,EACfgK,GAAS,KACP;IACF,MAAME,KAAK,GAAUnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IAEvC,IAAIsJ,KAAK,EAAE;MACT,MAAMhH,YAAY,GAAGH,GAAG,CACtBgG,WAAW,EACXnI,IAAI,EACJyB,WAAW,CAACrC,KAAK,CAAC,GAAG+C,GAAG,CAACkD,cAAc,EAAErF,IAAI,CAAC,GAAGZ,KAAK,CACvD;MAEDqC,WAAW,CAACa,YAAY,CAAC,IACxB8G,GAAG,IAAKA,GAAwB,CAACuP,cAAe,IACjDD,oBAAA,GACIhW,GAAG,CACDyF,WAAW,EACXnI,IAAI,EACJ0Y,oBAAoB,GAAGpW,YAAY,GAAG4O,aAAa,CAAC5H,KAAK,CAACE,EAAE,CAAC,IAE/DoP,aAAa,CAAC5Y,IAAI,EAAEsC,YAAY,CAAC;MAErC4H,MAAM,CAACD,KAAK,IAAI/C,SAAS,EAAE;;EAE/B,CAAC;EAED,MAAM2R,mBAAmB,GAAGA,CAC1B7Y,IAAuB,EACvB8Y,UAAmB,EACnBjG,WAAqB,EACrBkG,WAAqB,EACrBC,YAAsB,KAGpB;IACF,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,eAAe,GAAG,KAAK;IAC3B,MAAMxO,MAAM,GAAwD;MAClE1K;KACD;IAED,IAAI,CAAC+J,QAAQ,CAAC/D,QAAQ,EAAE;MACtB,IAAI,CAAC6M,WAAW,IAAIkG,WAAW,EAAE;QAC/B,IAAItT,eAAe,CAACc,OAAO,IAAI2Q,wBAAwB,CAAC3Q,OAAO,EAAE;UAC/D2S,eAAe,GAAG9S,UAAU,CAACG,OAAO;UACpCH,UAAU,CAACG,OAAO,GAAGmE,MAAM,CAACnE,OAAO,GAAG+R,SAAS,EAAE;UACjDW,iBAAiB,GAAGC,eAAe,KAAKxO,MAAM,CAACnE,OAAO;;QAGxD,MAAM4S,sBAAsB,GAAGnL,SAAS,CACtC7L,GAAG,CAACkD,cAAc,EAAErF,IAAI,CAAC,EACzB8Y,UAAU,CACX;QAEDI,eAAe,GAAG,CAAC,CAAC/W,GAAG,CAACiE,UAAU,CAACK,WAAW,EAAEzG,IAAI,CAAC;QACrDmZ,sBAAA,GACIzJ,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAEzG,IAAI,IAClC0C,GAAG,CAAC0D,UAAU,CAACK,WAAW,EAAEzG,IAAI,EAAE,IAAI,CAAC;QAC3C0K,MAAM,CAACjE,WAAW,GAAGL,UAAU,CAACK,WAAW;QAC3CwS,iBAAiB,GACfA,iBAAiB,IAChB,CAACxT,eAAe,CAACgB,WAAW,IAC3ByQ,wBAAwB,CAACzQ,WAAW,KACpCyS,eAAe,KAAK,CAACC,sBAAuB;;MAGlD,IAAItG,WAAW,EAAE;QACf,MAAMuG,sBAAsB,GAAGjX,GAAG,CAACiE,UAAU,CAACM,aAAa,EAAE1G,IAAI,CAAC;QAElE,IAAI,CAACoZ,sBAAsB,EAAE;UAC3B1W,GAAG,CAAC0D,UAAU,CAACM,aAAa,EAAE1G,IAAI,EAAE6S,WAAW,CAAC;UAChDnI,MAAM,CAAChE,aAAa,GAAGN,UAAU,CAACM,aAAa;UAC/CuS,iBAAiB,GACfA,iBAAiB,IAChB,CAACxT,eAAe,CAACiB,aAAa,IAC7BwQ,wBAAwB,CAACxQ,aAAa,KACtC0S,sBAAsB,KAAKvG,WAAY;;;MAI/CoG,iBAAiB,IAAID,YAAY,IAAIpM,SAAS,CAACC,KAAK,CAACC,IAAI,CAACpC,MAAM,CAAC;;IAGnE,OAAOuO,iBAAiB,GAAGvO,MAAM,GAAG,EAAE;EACxC,CAAC;EAED,MAAM2O,mBAAmB,GAAGA,CAC1BrZ,IAAuB,EACvB6G,OAAiB,EACjBqC,KAAkB,EAClBL,UAIC,KACC;IACF,MAAMyQ,kBAAkB,GAAGnX,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;IACvD,MAAMwX,iBAAiB,GACrB,CAAC/R,eAAe,CAACoB,OAAO,IAAIqQ,wBAAwB,CAACrQ,OAAO,KAC5DpE,SAAS,CAACoE,OAAO,CAAC,IAClBT,UAAU,CAACS,OAAO,KAAKA,OAAO;IAEhC,IAAIkD,QAAQ,CAACwP,UAAU,IAAIrQ,KAAK,EAAE;MAChC8N,kBAAkB,GAAGI,QAAQ,CAAC,MAAMmB,YAAY,CAACvY,IAAI,EAAEkJ,KAAK,CAAC,CAAC;MAC9D8N,kBAAkB,CAACjN,QAAQ,CAACwP,UAAU,CAAC;WAClC;MACLjC,YAAY,CAACL,KAAK,CAAC;MACnBD,kBAAkB,GAAG,IAAI;MACzB9N,KAAA,GACIxG,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAEkJ,KAAK,IAClCwG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;;IAGpC,IACE,CAACkJ,KAAK,GAAG,CAAC8E,SAAS,CAACsL,kBAAkB,EAAEpQ,KAAK,CAAC,GAAGoQ,kBAAkB,KACnE,CAAC5K,aAAa,CAAC7F,UAAU,CAAC,IAC1B2O,iBAAiB,EACjB;MACA,MAAMgC,gBAAgB,GAAG;QACvB,GAAG3Q,UAAU;QACb,IAAI2O,iBAAiB,IAAI/U,SAAS,CAACoE,OAAO,CAAC,GAAG;UAAEA;QAAO,CAAE,GAAG,EAAE,CAAC;QAC/DC,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzB9G;OACD;MAEDoG,UAAU,GAAG;QACX,GAAGA,UAAU;QACb,GAAGoT;OACJ;MAED5M,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC0M,gBAAgB,CAAC;;EAE1C,CAAC;EAED,MAAM9B,UAAU,GAAG,MAAO1X,IAA0B,IAAI;IACtD4X,mBAAmB,CAAC5X,IAAI,EAAE,IAAI,CAAC;IAC/B,MAAMuC,MAAM,GAAG,MAAMwH,QAAQ,CAAC0N,QAAS,CACrCtP,WAA2B,EAC3B4B,QAAQ,CAAC0P,OAAO,EAChBnI,kBAAkB,CAChBtR,IAAI,IAAIsH,MAAM,CAAC2C,KAAK,EACpBV,OAAO,EACPQ,QAAQ,CAACyH,YAAY,EACrBzH,QAAQ,CAAC0H,yBAAyB,CACnC,CACF;IACDmG,mBAAmB,CAAC5X,IAAI,CAAC;IACzB,OAAOuC,MAAM;EACf,CAAC;EAED,MAAMmX,2BAA2B,GAAG,MAAOtZ,KAA2B,IAAI;IACxE,MAAM;MAAE0G;IAAM,CAAE,GAAG,MAAM4Q,UAAU,CAACtX,KAAK,CAAC;IAE1C,IAAIA,KAAK,EAAE;MACT,KAAK,MAAMJ,IAAI,IAAII,KAAK,EAAE;QACxB,MAAM8I,KAAK,GAAG/G,GAAG,CAAC2E,MAAM,EAAE9G,IAAI,CAAC;QAC/BkJ,KAAA,GACIxG,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAEkJ,KAAK,IAClCwG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;;WAE/B;MACLoG,UAAU,CAACU,MAAM,GAAGA,MAAM;;IAG5B,OAAOA,MAAM;EACf,CAAC;EAED,MAAM6Q,wBAAwB,GAAG,MAAAA,CAC/B5H,MAAiB,EACjB4J,oBAA8B,EAC9BF,OAAA,GAEI;IACFG,KAAK,EAAE;EACR,MACC;IACF,KAAK,MAAM5Z,IAAI,IAAI+P,MAAM,EAAE;MACzB,MAAMzG,KAAK,GAAGyG,MAAM,CAAC/P,IAAI,CAAC;MAE1B,IAAIsJ,KAAK,EAAE;QACT,MAAM;UAAEE,EAAE;UAAE,GAAGsP;QAAU,CAAE,GAAGxP,KAAc;QAE5C,IAAIE,EAAE,EAAE;UACN,MAAMqQ,gBAAgB,GAAGvS,MAAM,CAACkB,KAAK,CAACnI,GAAG,CAACmJ,EAAE,CAACxJ,IAAI,CAAC;UAClD,MAAM8Z,iBAAiB,GACrBxQ,KAAK,CAACE,EAAE,IAAI+I,oBAAoB,CAAEjJ,KAAe,CAACE,EAAE,CAAC;UAEvD,IAAIsQ,iBAAiB,IAAIrU,eAAe,CAACkB,gBAAgB,EAAE;YACzDiR,mBAAmB,CAAC,CAAC5X,IAAI,CAAC,EAAE,IAAI,CAAC;;UAGnC,MAAM+Z,UAAU,GAAG,MAAMvF,aAAa,CACpClL,KAAc,EACdhC,MAAM,CAACtB,QAAQ,EACfmC,WAAW,EACXgP,gCAAgC,EAChCpN,QAAQ,CAAC0H,yBAAyB,IAAI,CAACkI,oBAAoB,EAC3DE,gBAAgB,CACjB;UAED,IAAIC,iBAAiB,IAAIrU,eAAe,CAACkB,gBAAgB,EAAE;YACzDiR,mBAAmB,CAAC,CAAC5X,IAAI,CAAC,CAAC;;UAG7B,IAAI+Z,UAAU,CAACvQ,EAAE,CAACxJ,IAAI,CAAC,EAAE;YACvByZ,OAAO,CAACG,KAAK,GAAG,KAAK;YACrB,IAAID,oBAAoB,EAAE;cACxB;;;UAIJ,CAACA,oBAAoB,KAClBxX,GAAG,CAAC4X,UAAU,EAAEvQ,EAAE,CAACxJ,IAAI,IACpB6Z,gBAAA,GACE5F,yBAAyB,CACvB7N,UAAU,CAACU,MAAM,EACjBiT,UAAU,EACVvQ,EAAE,CAACxJ,IAAI,IAET0C,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE0C,EAAE,CAACxJ,IAAI,EAAE+Z,UAAU,CAACvQ,EAAE,CAACxJ,IAAI,CAAC,IACrD0P,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE0C,EAAE,CAACxJ,IAAI,CAAC,CAAC;;QAG1C,CAAC0O,aAAa,CAACoK,UAAU,CAAC,KACvB,MAAMnB,wBAAwB,CAC7BmB,UAAU,EACVa,oBAAoB,EACpBF,OAAO,CACR,CAAC;;;IAIR,OAAOA,OAAO,CAACG,KAAK;EACtB,CAAC;EAED,MAAMxR,gBAAgB,GAAGA,CAAA,KAAK;IAC5B,KAAK,MAAMpI,IAAI,IAAIsH,MAAM,CAACyP,OAAO,EAAE;MACjC,MAAMzN,KAAK,GAAUnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;MAEvCsJ,KAAK,KACFA,KAAK,CAACE,EAAE,CAAC4H,IAAA,GACN9H,KAAK,CAACE,EAAE,CAAC4H,IAAI,CAACiD,KAAK,CAAEjL,GAAG,IAAK,CAACgG,IAAI,CAAChG,GAAG,CAAC,IACvC,CAACgG,IAAI,CAAC9F,KAAK,CAACE,EAAE,CAACJ,GAAG,CAAC,CAAC,IACxBgB,UAAU,CAACpK,IAA+B,CAAC;;IAG/CsH,MAAM,CAACyP,OAAO,GAAG,IAAID,GAAG,EAAE;EAC5B,CAAC;EAED,MAAMwB,SAAS,GAAeA,CAACtY,IAAI,EAAEiB,IAAI,KACvC,CAAC8I,QAAQ,CAAC/D,QAAQ,KACjBhG,IAAI,IAAIiB,IAAI,IAAIyB,GAAG,CAACyF,WAAW,EAAEnI,IAAI,EAAEiB,IAAI,CAAC,EAC7C,CAAC+M,SAAS,CAACgM,SAAS,EAAE,EAAE3U,cAAc,CAAC,CAAC;EAE1C,MAAM4C,SAAS,GAAgCA,CAC7C7H,KAAK,EACLkC,YAAY,EACZkF,QAAQ,KAERH,mBAAmB,CACjBjH,KAAK,EACLkH,MAAM,EACN;IACE,IAAI4C,MAAM,CAACD,KAAA,GACP9B,WAAA,GACA1G,WAAW,CAACa,YAAY,IACtB+C,cAAA,GACA+B,QAAQ,CAAChH,KAAK,IACZ;MAAE,CAACA,KAAK,GAAGkC;IAAY,IACvBA,YAAY;EACrB,GACDkF,QAAQ,EACRlF,YAAY,CACb;EAEH,MAAM2X,cAAc,GAClBja,IAAuB,IAEvB4B,OAAO,CACLO,GAAG,CACD+H,MAAM,CAACD,KAAK,GAAG9B,WAAW,GAAG9C,cAAc,EAC3CrF,IAAI,EACJ+J,QAAQ,CAACzB,gBAAgB,GAAGnG,GAAG,CAACkD,cAAc,EAAErF,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC/D,CACF;EAEH,MAAM4Y,aAAa,GAAGA,CACpB5Y,IAAuB,EACvBZ,KAAkC,EAClCmR,OAAA,GAA0B,EAAE,KAC1B;IACF,MAAMjH,KAAK,GAAUnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IACvC,IAAI8Y,UAAU,GAAY1Z,KAAK;IAE/B,IAAIkK,KAAK,EAAE;MACT,MAAMkJ,cAAc,GAAGlJ,KAAK,CAACE,EAAE;MAE/B,IAAIgJ,cAAc,EAAE;QAClB,CAACA,cAAc,CAACxM,QAAQ,IACtBtD,GAAG,CAACyF,WAAW,EAAEnI,IAAI,EAAE0Q,eAAe,CAACtR,KAAK,EAAEoT,cAAc,CAAC,CAAC;QAEhEsG,UAAU,GACRjK,aAAa,CAAC2D,cAAc,CAACpJ,GAAG,CAAC,IAAI9J,iBAAiB,CAACF,KAAK,IACxD,KACAA,KAAK;QAEX,IAAI6P,gBAAgB,CAACuD,cAAc,CAACpJ,GAAG,CAAC,EAAE;UACxC,CAAC,GAAGoJ,cAAc,CAACpJ,GAAG,CAACmH,OAAO,CAAC,CAACuH,OAAO,CACpCoC,SAAS,IACPA,SAAS,CAACC,QAAQ,GACjBrB,UACD,CAACxM,QAAQ,CAAC4N,SAAS,CAAC9a,KAAK,CAAE,CAC/B;eACI,IAAIoT,cAAc,CAACpB,IAAI,EAAE;UAC9B,IAAIpS,eAAe,CAACwT,cAAc,CAACpJ,GAAG,CAAC,EAAE;YACvCoJ,cAAc,CAACpB,IAAI,CAAC0G,OAAO,CAAEsC,WAAW,IAAI;cAC1C,IAAI,CAACA,WAAW,CAACzB,cAAc,IAAI,CAACyB,WAAW,CAACpU,QAAQ,EAAE;gBACxD,IAAIvG,KAAK,CAACC,OAAO,CAACoZ,UAAU,CAAC,EAAE;kBAC7BsB,WAAW,CAACta,OAAO,GAAG,CAAC,CAACgZ,UAAU,CAACrG,IAAI,CACpCxR,IAAY,IAAKA,IAAI,KAAKmZ,WAAW,CAAChb,KAAK,CAC7C;uBACI;kBACLgb,WAAW,CAACta,OAAO,GACjBgZ,UAAU,KAAKsB,WAAW,CAAChb,KAAK,IAAI,CAAC,CAAC0Z,UAAU;;;YAGxD,CAAC,CAAC;iBACG;YACLtG,cAAc,CAACpB,IAAI,CAAC0G,OAAO,CACxBuC,QAA0B,IACxBA,QAAQ,CAACva,OAAO,GAAGua,QAAQ,CAACjb,KAAK,KAAK0Z,UAAW,CACrD;;eAEE,IAAInK,WAAW,CAAC6D,cAAc,CAACpJ,GAAG,CAAC,EAAE;UAC1CoJ,cAAc,CAACpJ,GAAG,CAAChK,KAAK,GAAG,EAAE;eACxB;UACLoT,cAAc,CAACpJ,GAAG,CAAChK,KAAK,GAAG0Z,UAAU;UAErC,IAAI,CAACtG,cAAc,CAACpJ,GAAG,CAAClK,IAAI,EAAE;YAC5B0N,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cACnB9M,IAAI;cACJkI,MAAM,EAAElH,WAAW,CAACmH,WAAW;YAChC,EAAC;;;;;IAMV,CAACoI,OAAO,CAACwI,WAAW,IAAIxI,OAAO,CAAC+J,WAAW,KACzCzB,mBAAmB,CACjB7Y,IAAI,EACJ8Y,UAAU,EACVvI,OAAO,CAAC+J,WAAW,EACnB/J,OAAO,CAACwI,WAAW,EACnB,IAAI,CACL;IAEHxI,OAAO,CAACgK,cAAc,IAAIC,OAAO,CAACxa,IAA0B,CAAC;EAC/D,CAAC;EAED,MAAMya,SAAS,GAAGA,CAKhBza,IAAO,EACPZ,KAAQ,EACRmR,OAAU,KACR;IACF,KAAK,MAAMmK,QAAQ,IAAItb,KAAK,EAAE;MAC5B,IAAI,CAACA,KAAK,CAACuB,cAAc,CAAC+Z,QAAQ,CAAC,EAAE;QACnC;;MAEF,MAAM5B,UAAU,GAAG1Z,KAAK,CAACsb,QAAQ,CAAC;MAClC,MAAM9S,SAAS,GAAG5H,IAAI,GAAG,GAAG,GAAG0a,QAAQ;MACvC,MAAMpR,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAE3B,SAAS,CAAC;MAErC,CAACN,MAAM,CAACkB,KAAK,CAACnI,GAAG,CAACL,IAAI,CAAC,IACrBR,QAAQ,CAACsZ,UAAU,CAAC,IACnBxP,KAAK,IAAI,CAACA,KAAK,CAACE,EAAG,KACtB,CAACrK,YAAY,CAAC2Z,UAAU,IACpB2B,SAAS,CAAC7S,SAAS,EAAEkR,UAAU,EAAEvI,OAAO,IACxCqI,aAAa,CAAChR,SAAS,EAAEkR,UAAU,EAAEvI,OAAO,CAAC;;EAErD,CAAC;EAED,MAAMoK,QAAQ,GAAkCA,CAC9C3a,IAAI,EACJZ,KAAK,EACLmR,OAAO,GAAG,EAAE,KACV;IACF,MAAMjH,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IAChC,MAAM0U,YAAY,GAAGpN,MAAM,CAACkB,KAAK,CAACnI,GAAG,CAACL,IAAI,CAAC;IAC3C,MAAM4a,UAAU,GAAG5Z,WAAW,CAAC5B,KAAK,CAAC;IAErCsD,GAAG,CAACyF,WAAW,EAAEnI,IAAI,EAAE4a,UAAU,CAAC;IAElC,IAAIlG,YAAY,EAAE;MAChB9H,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB9M,IAAI;QACJkI,MAAM,EAAElH,WAAW,CAACmH,WAAW;MAChC,EAAC;MAEF,IACE,CAAC1C,eAAe,CAACc,OAAO,IACtBd,eAAe,CAACgB,WAAW,IAC3ByQ,wBAAwB,CAAC3Q,OAAO,IAChC2Q,wBAAwB,CAACzQ,WAAW,KACtC8J,OAAO,CAACwI,WAAW,EACnB;QACAnM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnB9M,IAAI;UACJyG,WAAW,EAAE0J,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC;UACxD5B,OAAO,EAAE+R,SAAS,CAACtY,IAAI,EAAE4a,UAAU;QACpC,EAAC;;WAEC;MACLtR,KAAK,IAAI,CAACA,KAAK,CAACE,EAAE,IAAI,CAAClK,iBAAiB,CAACsb,UAAU,IAC/CH,SAAS,CAACza,IAAI,EAAE4a,UAAU,EAAErK,OAAO,IACnCqI,aAAa,CAAC5Y,IAAI,EAAE4a,UAAU,EAAErK,OAAO,CAAC;;IAG9CqC,SAAS,CAAC5S,IAAI,EAAEsH,MAAM,CAAC,IAAIsF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAAE,GAAG1G;IAAU,CAAE,CAAC;IAClEwG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB9M,IAAI,EAAEkK,MAAM,CAACD,KAAK,GAAGjK,IAAI,GAAG2B,SAAS;MACrCuG,MAAM,EAAElH,WAAW,CAACmH,WAAW;IAChC,EAAC;EACJ,CAAC;EAED,MAAM3E,QAAQ,GAAkB,MAAO5D,KAAK,IAAI;IAC9CsK,MAAM,CAACD,KAAK,GAAG,IAAI;IACnB,MAAMpK,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIG,IAAI,GAAWH,MAAM,CAACG,IAAI;IAC9B,IAAI6a,mBAAmB,GAAG,IAAI;IAC9B,MAAMvR,KAAK,GAAUnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IACvC,MAAM8a,0BAA0B,GAAIhC,UAAmB,IAAI;MACzD+B,mBAAmB,GACjBE,MAAM,CAAC9X,KAAK,CAAC6V,UAAU,CAAC,IACvB3Z,YAAY,CAAC2Z,UAAU,CAAC,IAAI7V,KAAK,CAAC6V,UAAU,CAACzK,OAAO,EAAE,CAAE,IACzDL,SAAS,CAAC8K,UAAU,EAAE3W,GAAG,CAACgG,WAAW,EAAEnI,IAAI,EAAE8Y,UAAU,CAAC,CAAC;IAC7D,CAAC;IACD,MAAMkC,0BAA0B,GAAGjJ,kBAAkB,CAAChI,QAAQ,CAACiI,IAAI,CAAC;IACpE,MAAMiJ,yBAAyB,GAAGlJ,kBAAkB,CAClDhI,QAAQ,CAACgK,cAAc,CACxB;IAED,IAAIzK,KAAK,EAAE;MACT,IAAIJ,KAAK;MACT,IAAIrC,OAAO;MACX,MAAMiS,UAAU,GAAGjZ,MAAM,CAACX,IAAA,GACtBgS,aAAa,CAAC5H,KAAK,CAACE,EAAE,IACtB7J,aAAa,CAACC,KAAK,CAAC;MACxB,MAAMiT,WAAW,GACfjT,KAAK,CAACV,IAAI,KAAKgE,MAAM,CAACC,IAAI,IAAIvD,KAAK,CAACV,IAAI,KAAKgE,MAAM,CAACE,SAAS;MAC/D,MAAM8X,oBAAoB,GACvB,CAACvI,aAAa,CAACrJ,KAAK,CAACE,EAAE,CAAC,IACvB,CAACO,QAAQ,CAAC0N,QAAQ,IAClB,CAACtV,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC,IAC7B,CAACsJ,KAAK,CAACE,EAAE,CAAC2R,IAAI,IAChBtH,cAAc,CACZhB,WAAW,EACX1Q,GAAG,CAACiE,UAAU,CAACM,aAAa,EAAE1G,IAAI,CAAC,EACnCoG,UAAU,CAAC0N,WAAW,EACtBmH,yBAAyB,EACzBD,0BAA0B,CAC3B;MACH,MAAMI,OAAO,GAAGxI,SAAS,CAAC5S,IAAI,EAAEsH,MAAM,EAAEuL,WAAW,CAAC;MAEpDnQ,GAAG,CAACyF,WAAW,EAAEnI,IAAI,EAAE8Y,UAAU,CAAC;MAElC,IAAIjG,WAAW,EAAE;QACfvJ,KAAK,CAACE,EAAE,CAACjG,MAAM,IAAI+F,KAAK,CAACE,EAAE,CAACjG,MAAM,CAAC3D,KAAK,CAAC;QACzCoX,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;aACtC,IAAI1N,KAAK,CAACE,EAAE,CAAChG,QAAQ,EAAE;QAC5B8F,KAAK,CAACE,EAAE,CAAChG,QAAQ,CAAC5D,KAAK,CAAC;;MAG1B,MAAMiJ,UAAU,GAAGgQ,mBAAmB,CAAC7Y,IAAI,EAAE8Y,UAAU,EAAEjG,WAAW,CAAC;MAErE,MAAMmG,YAAY,GAAG,CAACtK,aAAa,CAAC7F,UAAU,CAAC,IAAIuS,OAAO;MAE1D,CAACvI,WAAW,IACVjG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB9M,IAAI;QACJd,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChBgJ,MAAM,EAAElH,WAAW,CAACmH,WAAW;MAChC,EAAC;MAEJ,IAAI+S,oBAAoB,EAAE;QACxB,IAAIzV,eAAe,CAACoB,OAAO,IAAIqQ,wBAAwB,CAACrQ,OAAO,EAAE;UAC/D,IAAIkD,QAAQ,CAACiI,IAAI,KAAK,QAAQ,EAAE;YAC9B,IAAIa,WAAW,EAAE;cACf3L,SAAS,EAAE;;iBAER,IAAI,CAAC2L,WAAW,EAAE;YACvB3L,SAAS,EAAE;;;QAIf,OACE8R,YAAY,IACZpM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAAE9M,IAAI;UAAE,IAAIob,OAAO,GAAG,EAAE,GAAGvS,UAAU;QAAC,CAAE,CAAC;;MAIlE,CAACgK,WAAW,IAAIuI,OAAO,IAAIxO,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG1G;MAAU,CAAE,CAAC;MAElE,IAAI2D,QAAQ,CAAC0N,QAAQ,EAAE;QACrB,MAAM;UAAE3Q;QAAM,CAAE,GAAG,MAAM4Q,UAAU,CAAC,CAAC1X,IAAI,CAAC,CAAC;QAE3C8a,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,MAAMQ,yBAAyB,GAAGlI,iBAAiB,CACjD/M,UAAU,CAACU,MAAM,EACjByC,OAAO,EACPvJ,IAAI,CACL;UACD,MAAMsb,iBAAiB,GAAGnI,iBAAiB,CACzCrM,MAAM,EACNyC,OAAO,EACP8R,yBAAyB,CAACrb,IAAI,IAAIA,IAAI,CACvC;UAEDkJ,KAAK,GAAGoS,iBAAiB,CAACpS,KAAK;UAC/BlJ,IAAI,GAAGsb,iBAAiB,CAACtb,IAAI;UAE7B6G,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;;aAE5B;QACL8Q,mBAAmB,CAAC,CAAC5X,IAAI,CAAC,EAAE,IAAI,CAAC;QACjCkJ,KAAK,GAAG,CACN,MAAMsL,aAAa,CACjBlL,KAAK,EACLhC,MAAM,CAACtB,QAAQ,EACfmC,WAAW,EACXgP,gCAAgC,EAChCpN,QAAQ,CAAC0H,yBAAyB,CACnC,EACDzR,IAAI,CAAC;QACP4X,mBAAmB,CAAC,CAAC5X,IAAI,CAAC,CAAC;QAE3B8a,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,IAAI3R,KAAK,EAAE;YACTrC,OAAO,GAAG,KAAK;iBACV,IACLpB,eAAe,CAACoB,OAAO,IACvBqQ,wBAAwB,CAACrQ,OAAO,EAChC;YACAA,OAAO,GAAG,MAAM8Q,wBAAwB,CAACpO,OAAO,EAAE,IAAI,CAAC;;;;MAK7D,IAAIsR,mBAAmB,EAAE;QACvBvR,KAAK,CAACE,EAAE,CAAC2R,IAAI,IACXX,OAAO,CACLlR,KAAK,CAACE,EAAE,CAAC2R,IAEoB,CAC9B;QACH9B,mBAAmB,CAACrZ,IAAI,EAAE6G,OAAO,EAAEqC,KAAK,EAAEL,UAAU,CAAC;;;EAG3D,CAAC;EAED,MAAM0S,WAAW,GAAGA,CAACnS,GAAQ,EAAE9H,GAAW,KAAI;IAC5C,IAAIa,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAExF,GAAG,CAAC,IAAI8H,GAAG,CAACK,KAAK,EAAE;MAC5CL,GAAG,CAACK,KAAK,EAAE;MACX,OAAO,CAAC;;IAEV;EACF,CAAC;EAED,MAAM+Q,OAAO,GAAiC,MAAAA,CAAOxa,IAAI,EAAEuQ,OAAO,GAAG,EAAE,KAAI;IACzE,IAAI1J,OAAO;IACX,IAAI0P,gBAAgB;IACpB,MAAMiF,UAAU,GAAGlO,qBAAqB,CAACtN,IAAI,CAAwB;IAErE,IAAI+J,QAAQ,CAAC0N,QAAQ,EAAE;MACrB,MAAM3Q,MAAM,GAAG,MAAM4S,2BAA2B,CAC9CjY,WAAW,CAACzB,IAAI,CAAC,GAAGA,IAAI,GAAGwb,UAAU,CACtC;MAED3U,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;MAC/ByP,gBAAgB,GAAGvW,IAAA,GACf,CAACwb,UAAU,CAACnP,IAAI,CAAErM,IAAI,IAAKmC,GAAG,CAAC2E,MAAM,EAAE9G,IAAI,CAAC,IAC5C6G,OAAO;WACN,IAAI7G,IAAI,EAAE;MACfuW,gBAAgB,GAAG,CACjB,MAAMkF,OAAO,CAAC9X,GAAG,CACf6X,UAAU,CAAC7T,GAAG,CAAC,MAAOC,SAAS,IAAI;QACjC,MAAM0B,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAE3B,SAAS,CAAC;QACrC,OAAO,MAAM+P,wBAAwB,CACnCrO,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAG;UAAE,CAAC5B,SAAS,GAAG0B;QAAK,CAAE,GAAGA,KAAK,CACnD;OACF,CAAC,CACH,EACD+K,KAAK,CAACvS,OAAO,CAAC;MAChB,EAAE,CAACyU,gBAAgB,IAAI,CAACnQ,UAAU,CAACS,OAAO,CAAC,IAAIK,SAAS,EAAE;WACrD;MACLqP,gBAAgB,GAAG1P,OAAO,GAAG,MAAM8Q,wBAAwB,CAACpO,OAAO,CAAC;;IAGtEqD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,IAAI,CAAC1F,QAAQ,CAACpH,IAAI,CAAC,IAClB,CAACyF,eAAe,CAACoB,OAAO,IAAIqQ,wBAAwB,CAACrQ,OAAO,KAC3DA,OAAO,KAAKT,UAAU,CAACS,OAAO,GAC5B,KACA;QAAE7G;MAAI,CAAE,CAAC;MACb,IAAI+J,QAAQ,CAAC0N,QAAQ,IAAI,CAACzX,IAAI,GAAG;QAAE6G;MAAO,CAAE,GAAG,EAAE,CAAC;MAClDC,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IAEFyJ,OAAO,CAACmL,WAAW,IACjB,CAACnF,gBAAgB,IACjBvD,qBAAqB,CACnBzJ,OAAO,EACPgS,WAAW,EACXvb,IAAI,GAAGwb,UAAU,GAAGlU,MAAM,CAAC2C,KAAK,CACjC;IAEH,OAAOsM,gBAAgB;EACzB,CAAC;EAED,MAAMyD,SAAS,GACbwB,UAE0C,IACxC;IACF,MAAMtT,MAAM,GAAG;MACb,IAAIgC,MAAM,CAACD,KAAK,GAAG9B,WAAW,GAAG9C,cAAc;KAChD;IAED,OAAO5D,WAAW,CAAC+Z,UAAU,IACzBtT,MAAA,GACAd,QAAQ,CAACoU,UAAU,IACjBrZ,GAAG,CAAC+F,MAAM,EAAEsT,UAAU,IACtBA,UAAU,CAAC7T,GAAG,CAAE3H,IAAI,IAAKmC,GAAG,CAAC+F,MAAM,EAAElI,IAAI,CAAC,CAAC;EACnD,CAAC;EAED,MAAM2b,aAAa,GAAuCA,CACxD3b,IAAI,EACJgF,SAAS,MACL;IACJ+D,OAAO,EAAE,CAAC,CAAC5G,GAAG,CAAC,CAAC6C,SAAS,IAAIoB,UAAU,EAAEU,MAAM,EAAE9G,IAAI,CAAC;IACtDuG,OAAO,EAAE,CAAC,CAACpE,GAAG,CAAC,CAAC6C,SAAS,IAAIoB,UAAU,EAAEK,WAAW,EAAEzG,IAAI,CAAC;IAC3DkJ,KAAK,EAAE/G,GAAG,CAAC,CAAC6C,SAAS,IAAIoB,UAAU,EAAEU,MAAM,EAAE9G,IAAI,CAAC;IAClD4G,YAAY,EAAE,CAAC,CAACzE,GAAG,CAACiE,UAAU,CAACO,gBAAgB,EAAE3G,IAAI,CAAC;IACtDiJ,SAAS,EAAE,CAAC,CAAC9G,GAAG,CAAC,CAAC6C,SAAS,IAAIoB,UAAU,EAAEM,aAAa,EAAE1G,IAAI;EAC/D,EAAC;EAEF,MAAM4b,WAAW,GAAsC5b,IAAI,IAAI;IAC7DA,IAAI,IACFsN,qBAAqB,CAACtN,IAAI,CAAC,CAAC8X,OAAO,CAAE+D,SAAS,IAC5CnM,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE+U,SAAS,CAAC,CACpC;IAEHjP,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBhG,MAAM,EAAE9G,IAAI,GAAGoG,UAAU,CAACU,MAAM,GAAG;IACpC,EAAC;EACJ,CAAC;EAED,MAAMkG,QAAQ,GAAkCA,CAAChN,IAAI,EAAEkJ,KAAK,EAAEqH,OAAO,KAAI;IACvE,MAAMnH,GAAG,GAAG,CAACjH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,EAAE;MAAEwJ,EAAE,EAAE;IAAE,CAAE,CAAC,CAACA,EAAE,IAAI,EAAE,EAAEJ,GAAG;IACzD,MAAM0S,YAAY,GAAG3Z,GAAG,CAACiE,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC,IAAI,EAAE;;IAGvD,MAAM;MAAEoJ,GAAG,EAAE2S,UAAU;MAAEnS,OAAO;MAAE1K,IAAI;MAAE,GAAG8c;IAAe,CAAE,GAAGF,YAAY;IAE3EpZ,GAAG,CAAC0D,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAE;MAC3B,GAAGgc,eAAe;MAClB,GAAG9S,KAAK;MACRE;IACD,EAAC;IAEFwD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB9M,IAAI;MACJ8G,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;IAEF0J,OAAO,IAAIA,OAAO,CAACmL,WAAW,IAAItS,GAAG,IAAIA,GAAG,CAACK,KAAK,IAAIL,GAAG,CAACK,KAAK,EAAE;EACnE,CAAC;EAED,MAAMhC,KAAK,GAA+BA,CACxCzH,IAG+B,EAC/BsC,YAAwC,KAExCsM,UAAU,CAAC5O,IAAI,IACX4M,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EAAGmP,OAAO,IACZjc,IAAI,CACFiI,SAAS,CAACtG,SAAS,EAAEW,YAAY,CAAC,EAClC2Z,OAIC;GAEN,IACDhU,SAAS,CACPjI,IAA+C,EAC/CsC,YAAY,EACZ,IAAI,CACL;EAEP,MAAMyE,UAAU,GAAiCpC,KAAK,IACpDiI,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EACF9H,SAIC,IACC;MACF,IACE0O,qBAAqB,CAAC/O,KAAK,CAAC3E,IAAI,EAAEgF,SAAS,CAAChF,IAAI,EAAE2E,KAAK,CAACsB,KAAK,CAAC,IAC9DuN,qBAAqB,CACnBxO,SAAS,EACRL,KAAK,CAACK,SAA2B,IAAIS,eAAe,EACrDyW,aAAa,EACbvX,KAAK,CAACwX,YAAY,CACnB,EACD;QACAxX,KAAK,CAACsC,QAAQ,CAAC;UACbiB,MAAM,EAAE;YAAE,GAAGC;UAAW,CAAkB;UAC1C,GAAG/B,UAAU;UACb,GAAGpB;QACJ,EAAC;;;GAGP,CAAC,CAAC4I,WAAW;EAEhB,MAAMF,SAAS,GAAoC/I,KAAK,IAAI;IAC1DuF,MAAM,CAACD,KAAK,GAAG,IAAI;IACnBiN,wBAAwB,GAAG;MACzB,GAAGA,wBAAwB;MAC3B,GAAGvS,KAAK,CAACK;KACV;IACD,OAAO+B,UAAU,CAAC;MAChB,GAAGpC,KAAK;MACRK,SAAS,EAAEkS;IACZ,EAAC;EACJ,CAAC;EAED,MAAM9M,UAAU,GAAoCA,CAACpK,IAAI,EAAEuQ,OAAO,GAAG,EAAE,KAAI;IACzE,KAAK,MAAM3I,SAAS,IAAI5H,IAAI,GAAGsN,qBAAqB,CAACtN,IAAI,CAAC,GAAGsH,MAAM,CAAC2C,KAAK,EAAE;MACzE3C,MAAM,CAAC2C,KAAK,CAACmS,MAAM,CAACxU,SAAS,CAAC;MAC9BN,MAAM,CAACkB,KAAK,CAAC4T,MAAM,CAACxU,SAAS,CAAC;MAE9B,IAAI,CAAC2I,OAAO,CAAC8L,SAAS,EAAE;QACtB3M,KAAK,CAACnG,OAAO,EAAE3B,SAAS,CAAC;QACzB8H,KAAK,CAACvH,WAAW,EAAEP,SAAS,CAAC;;MAG/B,CAAC2I,OAAO,CAAC+L,SAAS,IAAI5M,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAEc,SAAS,CAAC;MACzD,CAAC2I,OAAO,CAACgM,SAAS,IAAI7M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAEmB,SAAS,CAAC;MAC9D,CAAC2I,OAAO,CAACiM,WAAW,IAAI9M,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAEkB,SAAS,CAAC;MAClE,CAAC2I,OAAO,CAACkM,gBAAgB,IACvB/M,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAEiB,SAAS,CAAC;MAC/C,CAACmC,QAAQ,CAACzB,gBAAgB,IACxB,CAACiI,OAAO,CAACmM,gBAAgB,IACzBhN,KAAK,CAACrK,cAAc,EAAEuC,SAAS,CAAC;;IAGpCgF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB5E,MAAM,EAAElH,WAAW,CAACmH,WAAW;IAChC,EAAC;IAEFyE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,GAAG1G,UAAU;MACb,IAAI,CAACmK,OAAO,CAACgM,SAAS,GAAG,EAAE,GAAG;QAAEhW,OAAO,EAAE+R,SAAS;MAAE,CAAE;IACvD,EAAC;IAEF,CAAC/H,OAAO,CAACoM,WAAW,IAAIzV,SAAS,EAAE;EACrC,CAAC;EAED,MAAMmD,iBAAiB,GAA+CA,CAAC;IACrErE,QAAQ;IACRhG;EAAI,CACL,KAAI;IACH,IACGyC,SAAS,CAACuD,QAAQ,CAAC,IAAIkE,MAAM,CAACD,KAAK,IACpC,CAAC,CAACjE,QAAQ,IACVsB,MAAM,CAACtB,QAAQ,CAAC3F,GAAG,CAACL,IAAI,CAAC,EACzB;MACAgG,QAAQ,GAAGsB,MAAM,CAACtB,QAAQ,CAAC0B,GAAG,CAAC1H,IAAI,CAAC,GAAGsH,MAAM,CAACtB,QAAQ,CAACoW,MAAM,CAACpc,IAAI,CAAC;;EAEvE,CAAC;EAED,MAAM2I,QAAQ,GAAkCA,CAAC3I,IAAI,EAAEuQ,OAAO,GAAG,EAAE,KAAI;IACrE,IAAIjH,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IAC9B,MAAM4c,iBAAiB,GACrBna,SAAS,CAAC8N,OAAO,CAACvK,QAAQ,CAAC,IAAIvD,SAAS,CAACsH,QAAQ,CAAC/D,QAAQ,CAAC;IAE7DtD,GAAG,CAAC6G,OAAO,EAAEvJ,IAAI,EAAE;MACjB,IAAIsJ,KAAK,IAAI,EAAE,CAAC;MAChBE,EAAE,EAAE;QACF,IAAIF,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAGF,KAAK,CAACE,EAAE,GAAG;UAAEJ,GAAG,EAAE;YAAEpJ;UAAI;QAAE,CAAE,CAAC;QACrDA,IAAI;QACJiK,KAAK,EAAE,IAAI;QACX,GAAGsG;MACJ;IACF,EAAC;IACFjJ,MAAM,CAAC2C,KAAK,CAACvC,GAAG,CAAC1H,IAAI,CAAC;IAEtB,IAAIsJ,KAAK,EAAE;MACTe,iBAAiB,CAAC;QAChBrE,QAAQ,EAAEvD,SAAS,CAAC8N,OAAO,CAACvK,QAAQ,IAChCuK,OAAO,CAACvK,QAAA,GACR+D,QAAQ,CAAC/D,QAAQ;QACrBhG;MACD,EAAC;WACG;MACLyY,mBAAmB,CAACzY,IAAI,EAAE,IAAI,EAAEuQ,OAAO,CAACnR,KAAK,CAAC;;IAGhD,OAAO;MACL,IAAIwd,iBAAA,GACA;QAAE5W,QAAQ,EAAEuK,OAAO,CAACvK,QAAQ,IAAI+D,QAAQ,CAAC/D;MAAQ,IACjD,EAAE,CAAC;MACP,IAAI+D,QAAQ,CAAC8S,WAAA,GACT;QACE3Y,QAAQ,EAAE,CAAC,CAACqM,OAAO,CAACrM,QAAQ;QAC5BJ,GAAG,EAAE8N,YAAY,CAACrB,OAAO,CAACzM,GAAG,CAAC;QAC9BD,GAAG,EAAE+N,YAAY,CAACrB,OAAO,CAAC1M,GAAG,CAAC;QAC9BG,SAAS,EAAE4N,YAAY,CAASrB,OAAO,CAACvM,SAAS,CAAW;QAC5DD,SAAS,EAAE6N,YAAY,CAACrB,OAAO,CAACxM,SAAS,CAAW;QACpDE,OAAO,EAAE2N,YAAY,CAACrB,OAAO,CAACtM,OAAO;MACtC,IACD,EAAE,CAAC;MACPjE,IAAI;MACJwD,QAAQ;MACRD,MAAM,EAAEC,QAAQ;MAChB4F,GAAG,EAAGA,GAA4B,IAAU;QAC1C,IAAIA,GAAG,EAAE;UACPT,QAAQ,CAAC3I,IAAI,EAAEuQ,OAAO,CAAC;UACvBjH,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;UAE1B,MAAM8c,QAAQ,GAAGrb,WAAW,CAAC2H,GAAG,CAAChK,KAAK,IAClCgK,GAAG,CAAC2T,gBAAA,GACD3T,GAAG,CAAC2T,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAI3T,GAAA,GAC7DA,GAAA,GACFA,GAAG;UACP,MAAM4T,eAAe,GAAG7N,iBAAiB,CAAC2N,QAAQ,CAAC;UACnD,MAAM1L,IAAI,GAAG9H,KAAK,CAACE,EAAE,CAAC4H,IAAI,IAAI,EAAE;UAEhC,IACE4L,eAAA,GACI5L,IAAI,CAACqB,IAAI,CAAEjC,MAAW,IAAKA,MAAM,KAAKsM,QAAQ,IAC9CA,QAAQ,KAAKxT,KAAK,CAACE,EAAE,CAACJ,GAAG,EAC7B;YACA;;UAGF1G,GAAG,CAAC6G,OAAO,EAAEvJ,IAAI,EAAE;YACjBwJ,EAAE,EAAE;cACF,GAAGF,KAAK,CAACE,EAAE;cACX,IAAIwT,eAAA,GACA;gBACE5L,IAAI,EAAE,CACJ,GAAGA,IAAI,CAACvP,MAAM,CAACuN,IAAI,CAAC,EACpB0N,QAAQ,EACR,IAAIrd,KAAK,CAACC,OAAO,CAACyC,GAAG,CAACkD,cAAc,EAAErF,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAC1D;gBACDoJ,GAAG,EAAE;kBAAElK,IAAI,EAAE4d,QAAQ,CAAC5d,IAAI;kBAAEc;gBAAI;cACjC,IACD;gBAAEoJ,GAAG,EAAE0T;cAAQ,CAAE;YACtB;UACF,EAAC;UAEFrE,mBAAmB,CAACzY,IAAI,EAAE,KAAK,EAAE2B,SAAS,EAAEmb,QAAQ,CAAC;eAChD;UACLxT,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,EAAE,EAAE,CAAC;UAE9B,IAAIsJ,KAAK,CAACE,EAAE,EAAE;YACZF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG,KAAK;;UAGxB,CAACF,QAAQ,CAACzB,gBAAgB,IAAIiI,OAAO,CAACjI,gBAAgB,KACpD,EAAEnI,kBAAkB,CAACmH,MAAM,CAACkB,KAAK,EAAExI,IAAI,CAAC,IAAIkK,MAAM,CAACC,MAAM,CAAC,IAC1D7C,MAAM,CAACyP,OAAO,CAACrP,GAAG,CAAC1H,IAAI,CAAC;;;KAG/B;EACH,CAAC;EAED,MAAMid,WAAW,GAAGA,CAAA,KAClBlT,QAAQ,CAAC0M,gBAAgB,IACzBzD,qBAAqB,CAACzJ,OAAO,EAAEgS,WAAW,EAAEjU,MAAM,CAAC2C,KAAK,CAAC;EAE3D,MAAMiT,YAAY,GAAIlX,QAAkB,IAAI;IAC1C,IAAIvD,SAAS,CAACuD,QAAQ,CAAC,EAAE;MACvB4G,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE9G;MAAQ,CAAE,CAAC;MAClCgN,qBAAqB,CACnBzJ,OAAO,EACP,CAACH,GAAG,EAAEpJ,IAAI,KAAI;QACZ,MAAMkT,YAAY,GAAU/Q,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;QAC9C,IAAIkT,YAAY,EAAE;UAChB9J,GAAG,CAACpD,QAAQ,GAAGkN,YAAY,CAAC1J,EAAE,CAACxD,QAAQ,IAAIA,QAAQ;UAEnD,IAAIvG,KAAK,CAACC,OAAO,CAACwT,YAAY,CAAC1J,EAAE,CAAC4H,IAAI,CAAC,EAAE;YACvC8B,YAAY,CAAC1J,EAAE,CAAC4H,IAAI,CAAC0G,OAAO,CAAElD,QAAQ,IAAI;cACxCA,QAAQ,CAAC5O,QAAQ,GAAGkN,YAAY,CAAC1J,EAAE,CAACxD,QAAQ,IAAIA,QAAQ;YAC1D,CAAC,CAAC;;;MAGR,CAAC,EACD,CAAC,EACD,KAAK,CACN;;EAEL,CAAC;EAED,MAAM2F,YAAY,GAChBA,CAACwR,OAAO,EAAEC,SAAS,KAAK,MAAOC,CAAC,IAAI;IAClC,IAAIC,YAAY,GAAG3b,SAAS;IAC5B,IAAI0b,CAAC,EAAE;MACLA,CAAC,CAACE,cAAc,IAAIF,CAAC,CAACE,cAAc,EAAE;MACrCF,CAA8B,CAACG,OAAO,IACpCH,CAA8B,CAACG,OAAO,EAAE;;IAE7C,IAAIrF,WAAW,GACbnX,WAAW,CAACmH,WAAW,CAAC;IAE1ByE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB+J,YAAY,EAAE;IACf,EAAC;IAEF,IAAI9M,QAAQ,CAAC0N,QAAQ,EAAE;MACrB,MAAM;QAAE3Q,MAAM;QAAEoB;MAAM,CAAE,GAAG,MAAMwP,UAAU,EAAE;MAC7CtR,UAAU,CAACU,MAAM,GAAGA,MAAM;MAC1BqR,WAAW,GAAGnX,WAAW,CAACkH,MAAM,CAAiB;WAC5C;MACL,MAAMyP,wBAAwB,CAACpO,OAAO,CAAC;;IAGzC,IAAIjC,MAAM,CAACtB,QAAQ,CAACyX,IAAI,EAAE;MACxB,KAAK,MAAMzd,IAAI,IAAIsH,MAAM,CAACtB,QAAQ,EAAE;QAClC0J,KAAK,CAACyI,WAAW,EAAEnY,IAAI,CAAC;;;IAI5B0P,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE,MAAM,CAAC;IAEhC,IAAI4H,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,EAAE;MACpC8F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBhG,MAAM,EAAE;MACT,EAAC;MACF,IAAI;QACF,MAAMqW,OAAO,CAAChF,WAAiC,EAAEkF,CAAC,CAAC;QACnD,OAAOnU,KAAK,EAAE;QACdoU,YAAY,GAAGpU,KAAK;;WAEjB;MACL,IAAIkU,SAAS,EAAE;QACb,MAAMA,SAAS,CAAC;UAAE,GAAGhX,UAAU,CAACU;QAAM,CAAE,EAAEuW,CAAC,CAAC;;MAE9CJ,WAAW,EAAE;MACb1F,UAAU,CAAC0F,WAAW,CAAC;;IAGzBrQ,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBgH,WAAW,EAAE,IAAI;MACjB+C,YAAY,EAAE,KAAK;MACnB9J,kBAAkB,EAAE2B,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,IAAI,CAACwW,YAAY;MACrE3G,WAAW,EAAEvQ,UAAU,CAACuQ,WAAW,GAAG,CAAC;MACvC7P,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IACF,IAAIwW,YAAY,EAAE;MAChB,MAAMA,YAAY;;EAEtB,CAAC;EAEH,MAAMI,UAAU,GAAoCA,CAAC1d,IAAI,EAAEuQ,OAAO,GAAG,EAAE,KAAI;IACzE,IAAIpO,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC,EAAE;MACtB,IAAIyB,WAAW,CAAC8O,OAAO,CAACjO,YAAY,CAAC,EAAE;QACrCqY,QAAQ,CAAC3a,IAAI,EAAEgB,WAAW,CAACmB,GAAG,CAACkD,cAAc,EAAErF,IAAI,CAAC,CAAC,CAAC;aACjD;QACL2a,QAAQ,CACN3a,IAAI,EACJuQ,OAAO,CAACjO,YAA2D,CACpE;QACDI,GAAG,CAAC2C,cAAc,EAAErF,IAAI,EAAEgB,WAAW,CAACuP,OAAO,CAACjO,YAAY,CAAC,CAAC;;MAG9D,IAAI,CAACiO,OAAO,CAACiM,WAAW,EAAE;QACxB9M,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAE1G,IAAI,CAAC;;MAGvC,IAAI,CAACuQ,OAAO,CAACgM,SAAS,EAAE;QACtB7M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAEzG,IAAI,CAAC;QACnCoG,UAAU,CAACG,OAAO,GAAGgK,OAAO,CAACjO,YAAA,GACzBgW,SAAS,CAACtY,IAAI,EAAEgB,WAAW,CAACmB,GAAG,CAACkD,cAAc,EAAErF,IAAI,CAAC,CAAC,IACtDsY,SAAS,EAAE;;MAGjB,IAAI,CAAC/H,OAAO,CAAC+L,SAAS,EAAE;QACtB5M,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;QAC9ByF,eAAe,CAACoB,OAAO,IAAIK,SAAS,EAAE;;MAGxC0F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG1G;MAAU,CAAE,CAAC;;EAE3C,CAAC;EAED,MAAMuX,MAAM,GAA+BA,CACzCpW,UAAU,EACVqW,gBAAgB,GAAG,EAAE,KACnB;IACF,MAAMC,aAAa,GAAGtW,UAAU,GAAGvG,WAAW,CAACuG,UAAU,CAAC,GAAGlC,cAAc;IAC3E,MAAMyY,kBAAkB,GAAG9c,WAAW,CAAC6c,aAAa,CAAC;IACrD,MAAME,kBAAkB,GAAGrP,aAAa,CAACnH,UAAU,CAAC;IACpD,MAAMW,MAAM,GAAG6V,kBAAkB,GAAG1Y,cAAc,GAAGyY,kBAAkB;IAEvE,IAAI,CAACF,gBAAgB,CAACI,iBAAiB,EAAE;MACvC3Y,cAAc,GAAGwY,aAAa;;IAGhC,IAAI,CAACD,gBAAgB,CAACK,UAAU,EAAE;MAChC,IAAIL,gBAAgB,CAACM,eAAe,EAAE;QACpC,MAAMC,aAAa,GAAG,IAAIrH,GAAG,CAAC,CAC5B,GAAGxP,MAAM,CAAC2C,KAAK,EACf,GAAG3E,MAAM,CAACqF,IAAI,CAACwF,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,CAAC,CAAC,CAC5D,CAAC;QACF,KAAK,MAAMP,SAAS,IAAInI,KAAK,CAACoY,IAAI,CAACsG,aAAa,CAAC,EAAE;UACjDhc,GAAG,CAACiE,UAAU,CAACK,WAAW,EAAEmB,SAAS,IACjClF,GAAG,CAACwF,MAAM,EAAEN,SAAS,EAAEzF,GAAG,CAACgG,WAAW,EAAEP,SAAS,CAAC,IAClD+S,QAAQ,CACN/S,SAAoC,EACpCzF,GAAG,CAAC+F,MAAM,EAAEN,SAAS,CAAC,CACvB;;aAEF;QACL,IAAIhH,KAAK,IAAIa,WAAW,CAAC8F,UAAU,CAAC,EAAE;UACpC,KAAK,MAAMvH,IAAI,IAAIsH,MAAM,CAAC2C,KAAK,EAAE;YAC/B,MAAMX,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;YAChC,IAAIsJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;cACrB,MAAMgJ,cAAc,GAAG/S,KAAK,CAACC,OAAO,CAAC4J,KAAK,CAACE,EAAE,CAAC4H,IAAI,IAC9C9H,KAAK,CAACE,EAAE,CAAC4H,IAAI,CAAC,CAAC,IACf9H,KAAK,CAACE,EAAE,CAACJ,GAAG;cAEhB,IAAIyF,aAAa,CAAC2D,cAAc,CAAC,EAAE;gBACjC,MAAM4L,IAAI,GAAG5L,cAAc,CAAC6L,OAAO,CAAC,MAAM,CAAC;gBAC3C,IAAID,IAAI,EAAE;kBACRA,IAAI,CAACE,KAAK,EAAE;kBACZ;;;;;;QAOV,IAAIV,gBAAgB,CAACW,aAAa,EAAE;UAClC,KAAK,MAAM3W,SAAS,IAAIN,MAAM,CAAC2C,KAAK,EAAE;YACpC0Q,QAAQ,CACN/S,SAAoC,EACpCzF,GAAG,CAAC+F,MAAM,EAAEN,SAAS,CAAC,CACvB;;eAEE;UACL2B,OAAO,GAAG,EAAE;;;MAIhBpB,WAAW,GAAG4B,QAAQ,CAACzB,gBAAA,GACnBsV,gBAAgB,CAACI,iBAAA,GACdhd,WAAW,CAACqE,cAAc,IAC1B,KACFrE,WAAW,CAACkH,MAAM,CAAkB;MAEzC0E,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB5E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;MAEF0E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB5E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;;IAGJZ,MAAM,GAAG;MACP2C,KAAK,EAAE2T,gBAAgB,CAACM,eAAe,GAAG5W,MAAM,CAAC2C,KAAK,GAAG,IAAI6M,GAAG,EAAE;MAClEC,OAAO,EAAE,IAAID,GAAG,EAAE;MAClBtO,KAAK,EAAE,IAAIsO,GAAG,EAAE;MAChB9Q,QAAQ,EAAE,IAAI8Q,GAAG,EAAE;MACnBrP,KAAK,EAAE,IAAIqP,GAAG,EAAE;MAChBjP,QAAQ,EAAE,KAAK;MACf4B,KAAK,EAAE;KACR;IAEDS,MAAM,CAACD,KAAK,GACV,CAACxE,eAAe,CAACoB,OAAO,IACxB,CAAC,CAAC+W,gBAAgB,CAACjB,WAAW,IAC9B,CAAC,CAACiB,gBAAgB,CAACM,eAAe;IAEpChU,MAAM,CAACzC,KAAK,GAAG,CAAC,CAACsC,QAAQ,CAACzB,gBAAgB;IAE1CsE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB6J,WAAW,EAAEiH,gBAAgB,CAACY,eAAA,GAC1BpY,UAAU,CAACuQ,WAAA,GACX,CAAC;MACLpQ,OAAO,EAAEwX,kBAAA,GACL,QACAH,gBAAgB,CAACrB,SAAA,GACfnW,UAAU,CAACG,OAAA,GACX,CAAC,EACCqX,gBAAgB,CAACI,iBAAiB,IAClC,CAAChQ,SAAS,CAACzG,UAAU,EAAElC,cAAc,CAAC,CACvC;MACPyO,WAAW,EAAE8J,gBAAgB,CAACa,eAAA,GAC1BrY,UAAU,CAAC0N,WAAA,GACX,KAAK;MACTrN,WAAW,EAAEsX,kBAAA,GACT,KACAH,gBAAgB,CAACM,eAAA,GACfN,gBAAgB,CAACI,iBAAiB,IAAI7V,WAAA,GACpCgI,cAAc,CAAC9K,cAAc,EAAE8C,WAAW,IAC1C/B,UAAU,CAACK,WAAA,GACbmX,gBAAgB,CAACI,iBAAiB,IAAIzW,UAAA,GACpC4I,cAAc,CAAC9K,cAAc,EAAEkC,UAAU,IACzCqW,gBAAgB,CAACrB,SAAA,GACfnW,UAAU,CAACK,WAAA,GACX,EAAE;MACZC,aAAa,EAAEkX,gBAAgB,CAACpB,WAAA,GAC5BpW,UAAU,CAACM,aAAA,GACX,EAAE;MACNI,MAAM,EAAE8W,gBAAgB,CAACc,UAAU,GAAGtY,UAAU,CAACU,MAAM,GAAG,EAAE;MAC5DiG,kBAAkB,EAAE6Q,gBAAgB,CAACe,sBAAA,GACjCvY,UAAU,CAAC2G,kBAAA,GACX,KAAK;MACT8J,YAAY,EAAE;IACf,EAAC;EACJ,CAAC;EAED,MAAMyH,KAAK,GAA+BA,CAAC/W,UAAU,EAAEqW,gBAAgB,KACrED,MAAM,CACJ/O,UAAU,CAACrH,UAAU,IAChBA,UAAuB,CAACY,WAA2B,IACpDZ,UAAU,EACdqW,gBAAgB,CACjB;EAEH,MAAMgB,QAAQ,GAAkCA,CAAC5e,IAAI,EAAEuQ,OAAO,GAAG,EAAE,KAAI;IACrE,MAAMjH,KAAK,GAAGnH,GAAG,CAACoH,OAAO,EAAEvJ,IAAI,CAAC;IAChC,MAAMwS,cAAc,GAAGlJ,KAAK,IAAIA,KAAK,CAACE,EAAE;IAExC,IAAIgJ,cAAc,EAAE;MAClB,MAAMsK,QAAQ,GAAGtK,cAAc,CAACpB,IAAA,GAC5BoB,cAAc,CAACpB,IAAI,CAAC,CAAC,IACrBoB,cAAc,CAACpJ,GAAG;MAEtB,IAAI0T,QAAQ,CAACrT,KAAK,EAAE;QAClBqT,QAAQ,CAACrT,KAAK,EAAE;QAChB8G,OAAO,CAACsO,YAAY,IAClBjQ,UAAU,CAACkO,QAAQ,CAACpT,MAAM,CAAC,IAC3BoT,QAAQ,CAACpT,MAAM,EAAE;;;EAGzB,CAAC;EAED,MAAMwS,aAAa,GACjB1C,gBAAkD,IAChD;IACFpT,UAAU,GAAG;MACX,GAAGA,UAAU;MACb,GAAGoT;KACJ;EACH,CAAC;EAED,MAAMsF,mBAAmB,GAAGA,CAAA,KAC1BlQ,UAAU,CAAC7E,QAAQ,CAAC3E,aAAa,CAAC,IACjC2E,QAAQ,CAAC3E,aAA0B,EAAE,CAAC2Z,IAAI,CAAE7W,MAAoB,IAAI;IACnEoW,KAAK,CAACpW,MAAM,EAAE6B,QAAQ,CAACiV,YAAY,CAAC;IACpCpS,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBtG,SAAS,EAAE;IACZ,EAAC;EACJ,CAAC,CAAC;EAEJ,MAAMT,OAAO,GAAG;IACdd,OAAO,EAAE;MACP0D,QAAQ;MACRyB,UAAU;MACVuR,aAAa;MACbhQ,YAAY;MACZqB,QAAQ;MACRjG,UAAU;MACV2Q,UAAU;MACVuF,WAAW;MACXhV,SAAS;MACTqQ,SAAS;MACTpR,SAAS;MACT6Q,cAAc;MACd1N,iBAAiB;MACjBmO,UAAU;MACVyB,cAAc;MACd0D,MAAM;MACNmB,mBAAmB;MACnB1W,gBAAgB;MAChB8U,YAAY;MACZtQ,SAAS;MACTnH,eAAe;MACf,IAAI8D,OAAOA,CAAA;QACT,OAAOA,OAAO;OACf;MACD,IAAIpB,WAAWA,CAAA;QACb,OAAOA,WAAW;OACnB;MACD,IAAI+B,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAC9K,KAAK;QACd8K,MAAM,GAAG9K,KAAK;OACf;MACD,IAAIiG,cAAcA,CAAA;QAChB,OAAOA,cAAc;OACtB;MACD,IAAIiC,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAClI,KAAK;QACdkI,MAAM,GAAGlI,KAAK;OACf;MACD,IAAIgH,UAAUA,CAAA;QACZ,OAAOA,UAAU;OAClB;MACD,IAAI2D,QAAQA,CAAA;QACV,OAAOA,QAAQ;OAChB;MACD,IAAIA,QAAQA,CAAC3K,KAAK;QAChB2K,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX,GAAG3K;SACJ;;IAEJ;IACDsO,SAAS;IACT8M,OAAO;IACP7R,QAAQ;IACRgD,YAAY;IACZlE,KAAK;IACLkT,QAAQ;IACRX,SAAS;IACTsE,KAAK;IACLZ,UAAU;IACV9B,WAAW;IACXxR,UAAU;IACV4C,QAAQ;IACR4R,QAAQ;IACRjD;GACD;EAED,OAAO;IACL,GAAG5V,OAAO;IACVkZ,WAAW,EAAElZ;GACd;AACH;ACvhDA,IAAAmZ,UAAA,GAAeA,CAAA,KAAK;EAClB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,UAAU,EAAE;IACtD,OAAOD,MAAM,CAACC,UAAU,EAAE;;EAG5B,MAAMC,CAAC,GACL,OAAOC,WAAW,KAAK,WAAW,GAAGjgB,IAAI,CAACkgB,GAAG,EAAE,GAAGD,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI;EAE5E,OAAO,sCAAsC,CAACtd,OAAO,CAAC,OAAO,EAAGud,CAAC,IAAI;IACnE,MAAMC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAGN,CAAC,IAAI,EAAE,GAAG,CAAC;IAE3C,OAAO,CAACG,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG,EAAEG,QAAQ,CAAC,EAAE,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;ACVD,IAAAC,iBAAA,GAAeA,CACb7f,IAAuB,EACvB2C,KAAa,EACb4N,OAAA,GAAiC,EAAE,KAEnCA,OAAO,CAACmL,WAAW,IAAIja,WAAW,CAAC8O,OAAO,CAACmL,WAAW,IAClDnL,OAAO,CAACuP,SAAS,IACjB,GAAG9f,IAAI,IAAIyB,WAAW,CAAC8O,OAAO,CAACwP,UAAU,CAAC,GAAGpd,KAAK,GAAG4N,OAAO,CAACwP,UAAU,MACvE,EAAE;ACTR,IAAAC,QAAA,GAAeA,CAAI/e,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAG6B,IAAI,EACP,GAAGqM,qBAAqB,CAAClO,KAAK,CAAC,CAChC;ACLD,IAAA6gB,cAAA,GAAmB7gB,KAAc,IAC/BK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACuI,GAAG,CAAC,MAAMhG,SAAS,CAAC,GAAGA,SAAS;ACOjD,SAAUue,MAAMA,CAC5Bjf,IAAS,EACT0B,KAAa,EACbvD,KAAe;EAEf,OAAO,CACL,GAAG6B,IAAI,CAACuO,KAAK,CAAC,CAAC,EAAE7M,KAAK,CAAC,EACvB,GAAG2K,qBAAqB,CAAClO,KAAK,CAAC,EAC/B,GAAG6B,IAAI,CAACuO,KAAK,CAAC7M,KAAK,CAAC,CACrB;AACH;AChBA,IAAAwd,WAAA,GAAeA,CACblf,IAAuB,EACvB4W,IAAY,EACZuI,EAAU,KACW;EACrB,IAAI,CAAC3gB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB,OAAO,EAAE;;EAGX,IAAIQ,WAAW,CAACR,IAAI,CAACmf,EAAE,CAAC,CAAC,EAAE;IACzBnf,IAAI,CAACmf,EAAE,CAAC,GAAGze,SAAS;;EAEtBV,IAAI,CAACof,MAAM,CAACD,EAAE,EAAE,CAAC,EAAEnf,IAAI,CAACof,MAAM,CAACxI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAO5W,IAAI;AACb,CAAC;ACfD,IAAAqf,SAAA,GAAeA,CAAIrf,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAGkO,qBAAqB,CAAClO,KAAK,CAAC,EAC/B,GAAGkO,qBAAqB,CAACrM,IAAI,CAAC,CAC/B;ACDD,SAASsf,eAAeA,CAAItf,IAAS,EAAEuf,OAAiB;EACtD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,IAAI,GAAG,CAAC,GAAGzf,IAAI,CAAC;EAEtB,KAAK,MAAM0B,KAAK,IAAI6d,OAAO,EAAE;IAC3BE,IAAI,CAACL,MAAM,CAAC1d,KAAK,GAAG8d,CAAC,EAAE,CAAC,CAAC;IACzBA,CAAC,EAAE;;EAGL,OAAO7e,OAAO,CAAC8e,IAAI,CAAC,CAAC7d,MAAM,GAAG6d,IAAI,GAAG,EAAE;AACzC;AAEA,IAAAC,aAAA,GAAeA,CAAI1f,IAAS,EAAE0B,KAAyB,KACrDlB,WAAW,CAACkB,KAAK,IACb,KACA4d,eAAe,CACbtf,IAAI,EACHqM,qBAAqB,CAAC3K,KAAK,CAAc,CAACie,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACjE;ACtBP,IAAAC,WAAA,GAAeA,CAAI9f,IAAS,EAAE+f,MAAc,EAAEC,MAAc,KAAU;EACpE,CAAChgB,IAAI,CAAC+f,MAAM,CAAC,EAAE/f,IAAI,CAACggB,MAAM,CAAC,CAAC,GAAG,CAAChgB,IAAI,CAACggB,MAAM,CAAC,EAAEhgB,IAAI,CAAC+f,MAAM,CAAC,CAAC;AAC7D,CAAC;ACFD,IAAAE,QAAA,GAAeA,CAAI/I,WAAgB,EAAExV,KAAa,EAAEvD,KAAQ,KAAI;EAC9D+Y,WAAW,CAACxV,KAAK,CAAC,GAAGvD,KAAK;EAC1B,OAAO+Y,WAAW;AACpB,CAAC;;ACwCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAUgJ,aAAaA,CAO3Bxc,KAKC;EAED,MAAMoB,OAAO,GAAGvB,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGc,OAAO,CAACd,OAAO;IACzBjF,IAAI;IACJohB,OAAO,GAAG,IAAI;IACd9Y,gBAAgB;IAChBM;EAAK,CACN,GAAGjE,KAAK;EACT,MAAM,CAACoL,MAAM,EAAEsR,SAAS,CAAC,GAAGhd,cAAK,CAAC8B,QAAQ,CAAClB,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,CAAC;EACxE,MAAMshB,GAAG,GAAGjd,cAAK,CAACiC,MAAM,CACtBrB,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,CAAC2H,GAAG,CAACuX,UAAU,CAAC,CAC7C;EACD,MAAMqC,SAAS,GAAGld,cAAK,CAACiC,MAAM,CAACyJ,MAAM,CAAC;EACtC,MAAMyR,KAAK,GAAGnd,cAAK,CAACiC,MAAM,CAACtG,IAAI,CAAC;EAChC,MAAMyhB,SAAS,GAAGpd,cAAK,CAACiC,MAAM,CAAC,KAAK,CAAC;EAErCkb,KAAK,CAACxa,OAAO,GAAGhH,IAAI;EACpBuhB,SAAS,CAACva,OAAO,GAAG+I,MAAM;EAC1B9K,OAAO,CAACqC,MAAM,CAACkB,KAAK,CAACd,GAAG,CAAC1H,IAAI,CAAC;EAE9B4I,KAAK,IACF3D,OAA0D,CAAC0D,QAAQ,CAClE3I,IAA+B,EAC/B4I,KAAsC,CACvC;EAEHlD,yBAAyB,CACvB,MACET,OAAO,CAAC2H,SAAS,CAACpE,KAAK,CAACkF,SAAS,CAAC;IAChCZ,IAAI,EAAEA,CAAC;MACL5E,MAAM;MACNlI,IAAI,EAAE0hB;IAAc,CAIrB,KAAI;MACH,IAAIA,cAAc,KAAKF,KAAK,CAACxa,OAAO,IAAI,CAAC0a,cAAc,EAAE;QACvD,MAAMvJ,WAAW,GAAGhW,GAAG,CAAC+F,MAAM,EAAEsZ,KAAK,CAACxa,OAAO,CAAC;QAC9C,IAAIvH,KAAK,CAACC,OAAO,CAACyY,WAAW,CAAC,EAAE;UAC9BkJ,SAAS,CAAClJ,WAAW,CAAC;UACtBmJ,GAAG,CAACta,OAAO,GAAGmR,WAAW,CAACxQ,GAAG,CAACuX,UAAU,CAAC;;;;EAIhD,EAAC,CAACtR,WAAW,EAChB,CAAC3I,OAAO,CAAC,CACV;EAED,MAAM0c,YAAY,GAAGtd,cAAK,CAAC8E,WAAW,CAMlCyY,uBAA0B,IACxB;IACFH,SAAS,CAACza,OAAO,GAAG,IAAI;IACxB/B,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,EAAE4hB,uBAAuB,CAAC;EACvD,CAAC,EACD,CAAC3c,OAAO,EAAEjF,IAAI,CAAC,CAChB;EAED,MAAMmM,MAAM,GAAGA,CACb/M,KAEwD,EACxDmR,OAA+B,KAC7B;IACF,MAAMsR,WAAW,GAAGvU,qBAAqB,CAACtM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMwiB,uBAAuB,GAAG5B,QAAQ,CACtC/a,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,EAC5B6hB,WAAW,CACZ;IACD5c,OAAO,CAACqC,MAAM,CAACmC,KAAK,GAAGoW,iBAAiB,CACtC7f,IAAI,EACJ4hB,uBAAuB,CAAC/e,MAAM,GAAG,CAAC,EAClC0N,OAAO,CACR;IACD+Q,GAAG,CAACta,OAAO,GAAGgZ,QAAQ,CAACsB,GAAG,CAACta,OAAO,EAAE6a,WAAW,CAACla,GAAG,CAACuX,UAAU,CAAC,CAAC;IAChEyC,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC3c,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,EAAE4hB,uBAAuB,EAAE5B,QAAQ,EAAE;MAC9D5H,IAAI,EAAE6H,cAAc,CAAC7gB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM0iB,OAAO,GAAGA,CACd1iB,KAEwD,EACxDmR,OAA+B,KAC7B;IACF,MAAMwR,YAAY,GAAGzU,qBAAqB,CAACtM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC9D,MAAMwiB,uBAAuB,GAAGtB,SAAS,CACvCrb,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,EAC5B+hB,YAAY,CACb;IACD9c,OAAO,CAACqC,MAAM,CAACmC,KAAK,GAAGoW,iBAAiB,CAAC7f,IAAI,EAAE,CAAC,EAAEuQ,OAAO,CAAC;IAC1D+Q,GAAG,CAACta,OAAO,GAAGsZ,SAAS,CAACgB,GAAG,CAACta,OAAO,EAAE+a,YAAY,CAACpa,GAAG,CAACuX,UAAU,CAAC,CAAC;IAClEyC,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC3c,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,EAAE4hB,uBAAuB,EAAEtB,SAAS,EAAE;MAC/DlI,IAAI,EAAE6H,cAAc,CAAC7gB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM4iB,MAAM,GAAIrf,KAAyB,IAAI;IAC3C,MAAMif,uBAAuB,GAEvBjB,aAAa,CAAC1b,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,EAAE2C,KAAK,CAAC;IACxD2e,GAAG,CAACta,OAAO,GAAG2Z,aAAa,CAACW,GAAG,CAACta,OAAO,EAAErE,KAAK,CAAC;IAC/Cgf,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC,CAACniB,KAAK,CAACC,OAAO,CAACyC,GAAG,CAAC8C,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC,CAAC,IACxC0C,GAAG,CAACuC,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,EAAE2B,SAAS,CAAC;IACvCsD,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,EAAE4hB,uBAAuB,EAAEjB,aAAa,EAAE;MACnEvI,IAAI,EAAEzV;IACP,EAAC;EACJ,CAAC;EAED,MAAMsf,QAAM,GAAG/B,CACbvd,KAAa,EACbvD,KAEwD,EACxDmR,OAA+B,KAC7B;IACF,MAAM2R,WAAW,GAAG5U,qBAAqB,CAACtM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMwiB,uBAAuB,GAAG1B,MAAQ,CACtCjb,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC,EAC5B2C,KAAK,EACLuf,WAAW,CACZ;IACDjd,OAAO,CAACqC,MAAM,CAACmC,KAAK,GAAGoW,iBAAiB,CAAC7f,IAAI,EAAE2C,KAAK,EAAE4N,OAAO,CAAC;IAC9D+Q,GAAG,CAACta,OAAO,GAAGkZ,MAAQ,CAACoB,GAAG,CAACta,OAAO,EAAErE,KAAK,EAAEuf,WAAW,CAACva,GAAG,CAACuX,UAAU,CAAC,CAAC;IACvEyC,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC3c,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,EAAE4hB,uBAAuB,EAAE1B,MAAQ,EAAE;MAC9D9H,IAAI,EAAEzV,KAAK;MACX0V,IAAI,EAAE4H,cAAc,CAAC7gB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM+iB,IAAI,GAAGA,CAACnB,MAAc,EAAEC,MAAc,KAAI;IAC9C,MAAMW,uBAAuB,GAAG3c,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC;IAC5D+gB,WAAW,CAACa,uBAAuB,EAAEZ,MAAM,EAAEC,MAAM,CAAC;IACpDF,WAAW,CAACO,GAAG,CAACta,OAAO,EAAEga,MAAM,EAAEC,MAAM,CAAC;IACxCU,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC3c,OAAO,CAAC8S,cAAc,CACpB/X,IAAI,EACJ4hB,uBAAuB,EACvBb,WAAW,EACX;MACE3I,IAAI,EAAE4I,MAAM;MACZ3I,IAAI,EAAE4I;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMmB,IAAI,GAAGA,CAACvK,IAAY,EAAEuI,EAAU,KAAI;IACxC,MAAMwB,uBAAuB,GAAG3c,OAAO,CAACgV,cAAc,CAACja,IAAI,CAAC;IAC5DmgB,WAAW,CAACyB,uBAAuB,EAAE/J,IAAI,EAAEuI,EAAE,CAAC;IAC9CD,WAAW,CAACmB,GAAG,CAACta,OAAO,EAAE6Q,IAAI,EAAEuI,EAAE,CAAC;IAClCuB,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAACO,uBAAuB,CAAC;IAClC3c,OAAO,CAAC8S,cAAc,CACpB/X,IAAI,EACJ4hB,uBAAuB,EACvBzB,WAAW,EACX;MACE/H,IAAI,EAAEP,IAAI;MACVQ,IAAI,EAAE+H;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMiC,MAAM,GAAGA,CACb1f,KAAa,EACbvD,KAAgD,KAC9C;IACF,MAAM4I,WAAW,GAAGhH,WAAW,CAAC5B,KAAK,CAAC;IACtC,MAAMwiB,uBAAuB,GAAGV,QAAQ,CACtCjc,OAAO,CAACgV,cAAc,CAEpBja,IAAI,CAAC,EACP2C,KAAK,EACLqF,WAAwE,CACzE;IACDsZ,GAAG,CAACta,OAAO,GAAG,CAAC,GAAG4a,uBAAuB,CAAC,CAACja,GAAG,CAAC,CAAC2a,IAAI,EAAE7B,CAAC,KACrD,CAAC6B,IAAI,IAAI7B,CAAC,KAAK9d,KAAK,GAAGuc,UAAU,EAAE,GAAGoC,GAAG,CAACta,OAAO,CAACyZ,CAAC,CAAC,CACrD;IACDkB,YAAY,CAACC,uBAAuB,CAAC;IACrCP,SAAS,CAAC,CAAC,GAAGO,uBAAuB,CAAC,CAAC;IACvC3c,OAAO,CAAC8S,cAAc,CACpB/X,IAAI,EACJ4hB,uBAAuB,EACvBV,QAAQ,EACR;MACE9I,IAAI,EAAEzV,KAAK;MACX0V,IAAI,EAAErQ;IACP,GACD,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAED,MAAM/F,OAAO,GACX7C,KAEwD,IACtD;IACF,MAAMwiB,uBAAuB,GAAGtU,qBAAqB,CAACtM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IACzEkiB,GAAG,CAACta,OAAO,GAAG4a,uBAAuB,CAACja,GAAG,CAACuX,UAAU,CAAC;IACrDyC,YAAY,CAAC,CAAC,GAAGC,uBAAuB,CAAC,CAAC;IAC1CP,SAAS,CAAC,CAAC,GAAGO,uBAAuB,CAAC,CAAC;IACvC3c,OAAO,CAAC8S,cAAc,CACpB/X,IAAI,EACJ,CAAC,GAAG4hB,uBAAuB,CAAC,EACxB3gB,IAAO,IAAQA,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAEDoD,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnBZ,OAAO,CAACiF,MAAM,CAACC,MAAM,GAAG,KAAK;IAE7ByI,SAAS,CAAC5S,IAAI,EAAEiF,OAAO,CAACqC,MAAM,CAAC,IAC7BrC,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B,GAAG7H,OAAO,CAACmB;IACe,EAAC;IAE/B,IACEqb,SAAS,CAACza,OAAO,KAChB,CAAC+K,kBAAkB,CAAC9M,OAAO,CAAC8E,QAAQ,CAACiI,IAAI,CAAC,CAACC,UAAU,IACpDhN,OAAO,CAACmB,UAAU,CAAC0N,WAAW,CAAC,IACjC,CAAC/B,kBAAkB,CAAC9M,OAAO,CAAC8E,QAAQ,CAACgK,cAAc,CAAC,CAAC9B,UAAU,EAC/D;MACA,IAAIhN,OAAO,CAAC8E,QAAQ,CAAC0N,QAAQ,EAAE;QAC7BxS,OAAO,CAACyS,UAAU,CAAC,CAAC1X,IAAI,CAAC,CAAC,CAAC+e,IAAI,CAAExc,MAAM,IAAI;UACzC,MAAM2G,KAAK,GAAG/G,GAAG,CAACI,MAAM,CAACuE,MAAM,EAAE9G,IAAI,CAAC;UACtC,MAAMuiB,aAAa,GAAGpgB,GAAG,CAAC8C,OAAO,CAACmB,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;UAE1D,IACEuiB,aAAA,GACK,CAACrZ,KAAK,IAAIqZ,aAAa,CAACrjB,IAAI,IAC5BgK,KAAK,KACHqZ,aAAa,CAACrjB,IAAI,KAAKgK,KAAK,CAAChK,IAAI,IAChCqjB,aAAa,CAAC3Y,OAAO,KAAKV,KAAK,CAACU,OAAO,CAAC,GAC5CV,KAAK,IAAIA,KAAK,CAAChK,IAAI,EACvB;YACAgK,KAAA,GACIxG,GAAG,CAACuC,OAAO,CAACmB,UAAU,CAACU,MAAM,EAAE9G,IAAI,EAAEkJ,KAAK,IAC1CwG,KAAK,CAACzK,OAAO,CAACmB,UAAU,CAACU,MAAM,EAAE9G,IAAI,CAAC;YAC1CiF,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cAC3BhG,MAAM,EAAE7B,OAAO,CAACmB,UAAU,CAACU;YAC5B,EAAC;;QAEN,CAAC,CAAC;aACG;QACL,MAAMwC,KAAK,GAAUnH,GAAG,CAAC8C,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC;QAC/C,IACEsJ,KAAK,IACLA,KAAK,CAACE,EAAE,IACR,EACEuI,kBAAkB,CAAC9M,OAAO,CAAC8E,QAAQ,CAACgK,cAAc,CAAC,CAAC9B,UAAU,IAC9DF,kBAAkB,CAAC9M,OAAO,CAAC8E,QAAQ,CAACiI,IAAI,CAAC,CAACC,UAAU,CACrD,EACD;UACAuC,aAAa,CACXlL,KAAK,EACLrE,OAAO,CAACqC,MAAM,CAACtB,QAAQ,EACvBf,OAAO,CAACkD,WAAW,EACnBlD,OAAO,CAAC8E,QAAQ,CAACyH,YAAY,KAAKlO,eAAe,CAACK,GAAG,EACrDsB,OAAO,CAAC8E,QAAQ,CAAC0H,yBAAyB,EAC1C,IAAI,CACL,CAACsN,IAAI,CACH7V,KAAK,IACJ,CAACwF,aAAa,CAACxF,KAAK,CAAC,IACrBjE,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;YAC3BhG,MAAM,EAAEmN,yBAAyB,CAC/BhP,OAAO,CAACmB,UAAU,CAACU,MAAmC,EACtDoC,KAAK,EACLlJ,IAAI;UAEP,EAAC,CACL;;;;IAKPiF,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B9M,IAAI;MACJkI,MAAM,EAAElH,WAAW,CAACiE,OAAO,CAACkD,WAAW;IACxC,EAAC;IAEFlD,OAAO,CAACqC,MAAM,CAACmC,KAAK,IAClBuJ,qBAAqB,CAAC/N,OAAO,CAACsE,OAAO,EAAE,CAACH,GAAG,EAAE9H,GAAW,KAAI;MAC1D,IACE2D,OAAO,CAACqC,MAAM,CAACmC,KAAK,IACpBnI,GAAG,CAACyR,UAAU,CAAC9N,OAAO,CAACqC,MAAM,CAACmC,KAAK,CAAC,IACpCL,GAAG,CAACK,KAAK,EACT;QACAL,GAAG,CAACK,KAAK,EAAE;QACX,OAAO,CAAC;;MAEV;IACF,CAAC,CAAC;IAEJxE,OAAO,CAACqC,MAAM,CAACmC,KAAK,GAAG,EAAE;IAEzBxE,OAAO,CAACiC,SAAS,EAAE;IACnBua,SAAS,CAACza,OAAO,GAAG,KAAK;GAC1B,EAAE,CAAC+I,MAAM,EAAE/P,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE3BZ,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,CAAC1D,GAAG,CAAC8C,OAAO,CAACkD,WAAW,EAAEnI,IAAI,CAAC,IAAIiF,OAAO,CAAC8S,cAAc,CAAC/X,IAAI,CAAC;IAE/D,OAAO,MAAK;MACV,MAAMgK,aAAa,GAAGA,CAAChK,IAAuB,EAAEZ,KAAc,KAAI;QAChE,MAAMkK,KAAK,GAAUnH,GAAG,CAAC8C,OAAO,CAACsE,OAAO,EAAEvJ,IAAI,CAAC;QAC/C,IAAIsJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;UACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG7K,KAAK;;MAE1B,CAAC;MAED6F,OAAO,CAAC8E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAA,GACjCrD,OAAO,CAACmF,UAAU,CAACpK,IAA+B,IAClDgK,aAAa,CAAChK,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEmc,OAAO,EAAE9Y,gBAAgB,CAAC,CAAC;EAE9C,OAAO;IACL6Z,IAAI,EAAE9d,cAAK,CAAC8E,WAAW,CAACgZ,IAAI,EAAE,CAACR,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5Dmd,IAAI,EAAE/d,cAAK,CAAC8E,WAAW,CAACiZ,IAAI,EAAE,CAACT,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5D6c,OAAO,EAAEzd,cAAK,CAAC8E,WAAW,CAAC2Y,OAAO,EAAE,CAACH,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClEkH,MAAM,EAAE9H,cAAK,CAAC8E,WAAW,CAACgD,MAAM,EAAE,CAACwV,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChE+c,MAAM,EAAE3d,cAAK,CAAC8E,WAAW,CAAC6Y,MAAM,EAAE,CAACL,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEib,MAAM,EAAE7b,cAAK,CAAC8E,WAAW,CAAC8Y,QAAM,EAAE,CAACN,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEod,MAAM,EAAEhe,cAAK,CAAC8E,WAAW,CAACkZ,MAAM,EAAE,CAACV,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEhD,OAAO,EAAEoC,cAAK,CAAC8E,WAAW,CAAClH,OAAO,EAAE,CAAC0f,YAAY,EAAE3hB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClE8K,MAAM,EAAE1L,cAAK,CAAC8C,OAAO,CACnB,MACE4I,MAAM,CAACpI,GAAG,CAAC,CAAC2B,KAAK,EAAE3G,KAAK,MAAM;MAC5B,GAAG2G,KAAK;MACR,CAAC8X,OAAO,GAAGE,GAAG,CAACta,OAAO,CAACrE,KAAK,CAAC,IAAIuc,UAAU;IAC5C,EAAC,CAAgE,EACpE,CAACnP,MAAM,EAAEqR,OAAO,CAAC;GAEpB;AACH;;AClbA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACG,SAAUoB,OAAOA,CAKrB7d,KAAA,GAAkE,EAAE;EAEpE,MAAM8d,YAAY,GAAGpe,cAAK,CAACiC,MAAM,CAE/B3E,SAAS,CAAC;EACZ,MAAM+gB,OAAO,GAAGre,cAAK,CAACiC,MAAM,CAAsB3E,SAAS,CAAC;EAC5D,MAAM,CAACqD,SAAS,EAAEkB,eAAe,CAAC,GAAG7B,cAAK,CAAC8B,QAAQ,CAA0B;IAC3EI,OAAO,EAAE,KAAK;IACdK,YAAY,EAAE,KAAK;IACnBJ,SAAS,EAAEoI,UAAU,CAACjK,KAAK,CAACS,aAAa,CAAC;IAC1C0O,WAAW,EAAE,KAAK;IAClB+C,YAAY,EAAE,KAAK;IACnB9J,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACd8P,WAAW,EAAE,CAAC;IACdlQ,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEnC,KAAK,CAACmC,MAAM,IAAI,EAAE;IAC1Bd,QAAQ,EAAErB,KAAK,CAACqB,QAAQ,IAAI,KAAK;IACjC4Q,OAAO,EAAE,KAAK;IACdxR,aAAa,EAAEwJ,UAAU,CAACjK,KAAK,CAACS,aAAa,IACzCzD,SAAA,GACAgD,KAAK,CAACS;EACX,EAAC;EAEF,IAAI,CAACqd,YAAY,CAACzb,OAAO,EAAE;IACzB,IAAIrC,KAAK,CAACsa,WAAW,EAAE;MACrBwD,YAAY,CAACzb,OAAO,GAAG;QACrB,GAAGrC,KAAK,CAACsa,WAAW;QACpBja;OACD;MAED,IAAIL,KAAK,CAACS,aAAa,IAAI,CAACwJ,UAAU,CAACjK,KAAK,CAACS,aAAa,CAAC,EAAE;QAC3DT,KAAK,CAACsa,WAAW,CAACX,KAAK,CAAC3Z,KAAK,CAACS,aAAa,EAAET,KAAK,CAACqa,YAAY,CAAC;;WAE7D;MACL,MAAM;QAAEC,WAAW;QAAE,GAAGzT;MAAI,CAAE,GAAGkL,iBAAiB,CAAC/R,KAAK,CAAC;MAEzD8d,YAAY,CAACzb,OAAO,GAAG;QACrB,GAAGwE,IAAI;QACPxG;OACD;;;EAIL,MAAMC,OAAO,GAAGwd,YAAY,CAACzb,OAAO,CAAC/B,OAAO;EAC5CA,OAAO,CAAC8E,QAAQ,GAAGpF,KAAK;EAExBe,yBAAyB,CAAC,MAAK;IAC7B,MAAMid,GAAG,GAAG1d,OAAO,CAAC8B,UAAU,CAAC;MAC7B/B,SAAS,EAAEC,OAAO,CAACQ,eAAe;MAClCwB,QAAQ,EAAEA,CAAA,KAAMf,eAAe,CAAC;QAAE,GAAGjB,OAAO,CAACmB;MAAU,CAAE,CAAC;MAC1D+V,YAAY,EAAE;IACf,EAAC;IAEFjW,eAAe,CAAEjF,IAAI,KAAM;MACzB,GAAGA,IAAI;MACP2V,OAAO,EAAE;IACV,EAAC,CAAC;IAEH3R,OAAO,CAACmB,UAAU,CAACwQ,OAAO,GAAG,IAAI;IAEjC,OAAO+L,GAAG;EACZ,CAAC,EAAE,CAAC1d,OAAO,CAAC,CAAC;EAEbZ,cAAK,CAACwB,SAAS,CACb,MAAMZ,OAAO,CAACiY,YAAY,CAACvY,KAAK,CAACqB,QAAQ,CAAC,EAC1C,CAACf,OAAO,EAAEN,KAAK,CAACqB,QAAQ,CAAC,CAC1B;EAED3B,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,IAAIlB,KAAK,CAACqN,IAAI,EAAE;MACd/M,OAAO,CAAC8E,QAAQ,CAACiI,IAAI,GAAGrN,KAAK,CAACqN,IAAI;;IAEpC,IAAIrN,KAAK,CAACoP,cAAc,EAAE;MACxB9O,OAAO,CAAC8E,QAAQ,CAACgK,cAAc,GAAGpP,KAAK,CAACoP,cAAc;;EAE1D,CAAC,EAAE,CAAC9O,OAAO,EAAEN,KAAK,CAACqN,IAAI,EAAErN,KAAK,CAACoP,cAAc,CAAC,CAAC;EAE/C1P,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,IAAIlB,KAAK,CAACmC,MAAM,EAAE;MAChB7B,OAAO,CAACuT,UAAU,CAAC7T,KAAK,CAACmC,MAAM,CAAC;MAChC7B,OAAO,CAACgY,WAAW,EAAE;;GAExB,EAAE,CAAChY,OAAO,EAAEN,KAAK,CAACmC,MAAM,CAAC,CAAC;EAE3BzC,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnBlB,KAAK,CAAC2D,gBAAgB,IACpBrD,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B5E,MAAM,EAAEjD,OAAO,CAACgD,SAAS;IAC1B,EAAC;GACL,EAAE,CAAChD,OAAO,EAAEN,KAAK,CAAC2D,gBAAgB,CAAC,CAAC;EAErCjE,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,IAAIZ,OAAO,CAACQ,eAAe,CAACc,OAAO,EAAE;MACnC,MAAMA,OAAO,GAAGtB,OAAO,CAACqT,SAAS,EAAE;MACnC,IAAI/R,OAAO,KAAKvB,SAAS,CAACuB,OAAO,EAAE;QACjCtB,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAC3BvG;QACD,EAAC;;;GAGP,EAAE,CAACtB,OAAO,EAAED,SAAS,CAACuB,OAAO,CAAC,CAAC;EAEhClC,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,IAAIlB,KAAK,CAACuD,MAAM,IAAI,CAAC8F,SAAS,CAACrJ,KAAK,CAACuD,MAAM,EAAEwa,OAAO,CAAC1b,OAAO,CAAC,EAAE;MAC7D/B,OAAO,CAAC0Y,MAAM,CAAChZ,KAAK,CAACuD,MAAM,EAAE;QAC3BqW,aAAa,EAAE,IAAI;QACnB,GAAGtZ,OAAO,CAAC8E,QAAQ,CAACiV;MACrB,EAAC;MACF0D,OAAO,CAAC1b,OAAO,GAAGrC,KAAK,CAACuD,MAAM;MAC9BhC,eAAe,CAAE2G,KAAK,KAAM;QAAE,GAAGA;MAAK,CAAE,CAAC,CAAC;WACrC;MACL5H,OAAO,CAAC6Z,mBAAmB,EAAE;;GAEhC,EAAE,CAAC7Z,OAAO,EAAEN,KAAK,CAACuD,MAAM,CAAC,CAAC;EAE3B7D,cAAK,CAACwB,SAAS,CAAC,MAAK;IACnB,IAAI,CAACZ,OAAO,CAACiF,MAAM,CAACD,KAAK,EAAE;MACzBhF,OAAO,CAACiC,SAAS,EAAE;MACnBjC,OAAO,CAACiF,MAAM,CAACD,KAAK,GAAG,IAAI;;IAG7B,IAAIhF,OAAO,CAACiF,MAAM,CAACzC,KAAK,EAAE;MACxBxC,OAAO,CAACiF,MAAM,CAACzC,KAAK,GAAG,KAAK;MAC5BxC,OAAO,CAAC2H,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG7H,OAAO,CAACmB;MAAU,CAAE,CAAC;;IAGzDnB,OAAO,CAACmD,gBAAgB,EAAE;EAC5B,CAAC,CAAC;EAEFqa,YAAY,CAACzb,OAAO,CAAChC,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;EAEtE,OAAOwd,YAAY,CAACzb,OAAO;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}