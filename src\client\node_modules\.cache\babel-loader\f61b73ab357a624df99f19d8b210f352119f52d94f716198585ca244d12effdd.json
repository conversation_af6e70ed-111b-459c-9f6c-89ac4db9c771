{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Link,useSearchParams}from'react-router-dom';import{FaEnvelopeOpenText,FaCheckCircle}from'react-icons/fa';import{auth}from'../firebase';import{applyActionCode}from'firebase/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckEmail=()=>{const[searchParams]=useSearchParams();const[isVerified,setIsVerified]=useState(false);const[isLoading,setIsLoading]=useState(true);const[error,setError]=useState('');useEffect(()=>{const oobCode=searchParams.get('oobCode');const mode=searchParams.get('mode');// Check if this is a verification link\nif(mode==='verifyEmail'&&oobCode){const verifyEmail=async()=>{try{await applyActionCode(auth,oobCode);setIsVerified(true);}catch(error){console.error('Email verification error:',error);setError('The verification link is invalid or has expired.');}finally{setIsLoading(false);}};verifyEmail();}else{setIsLoading(false);}},[searchParams]);if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gray-900 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-2\",children:\"Verifying your email...\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Please wait while we verify your email address.\"})]})});}if(isVerified){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gray-900 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",children:[/*#__PURE__*/_jsx(FaCheckCircle,{className:\"text-6xl text-green-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-4\",children:\"Email Verified!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-6\",children:\"Your email has been successfully verified. You can now log in to your account.\"}),/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\",children:\"Go to Login\"})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gray-900 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",children:[/*#__PURE__*/_jsx(FaEnvelopeOpenText,{className:\"text-6xl text-blue-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-4\",children:\"Check Your Inbox\"}),error?/*#__PURE__*/_jsx(\"p\",{className:\"text-red-500 mb-6\",children:error}):/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-6\",children:\"We have sent a verification link to your email address. Please click the link to complete your registration.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm mb-6\",children:\"Didn't receive the email? Check your spam folder or try signing up again.\"}),/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\",children:\"Back to Login\"})]})});};export default CheckEmail;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "useSearchParams", "FaEnvelopeOpenText", "FaCheckCircle", "auth", "applyActionCode", "jsx", "_jsx", "jsxs", "_jsxs", "CheckEmail", "searchParams", "isVerified", "setIsVerified", "isLoading", "setIsLoading", "error", "setError", "oobCode", "get", "mode", "verifyEmail", "console", "className", "children", "to"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/CheckEmail.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link, useSearchParams } from 'react-router-dom';\nimport { FaEnvelopeOpenText, FaCheckCircle } from 'react-icons/fa';\nimport { auth } from '../firebase';\nimport { applyActionCode } from 'firebase/auth';\n\nconst CheckEmail = () => {\n  const [searchParams] = useSearchParams();\n  const [isVerified, setIsVerified] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const oobCode = searchParams.get('oobCode');\n    const mode = searchParams.get('mode');\n\n    // Check if this is a verification link\n    if (mode === 'verifyEmail' && oobCode) {\n      const verifyEmail = async () => {\n        try {\n          await applyActionCode(auth, oobCode);\n          setIsVerified(true);\n        } catch (error) {\n          console.error('Email verification error:', error);\n          setError('The verification link is invalid or has expired.');\n        } finally {\n          setIsLoading(false);\n        }\n      };\n\n      verifyEmail();\n    } else {\n      setIsLoading(false);\n    }\n  }, [searchParams]);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n        <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-6\"></div>\n          <h2 className=\"text-2xl font-bold mb-2\">Verifying your email...</h2>\n          <p className=\"text-gray-400\">Please wait while we verify your email address.</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (isVerified) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n        <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n          <FaCheckCircle className=\"text-6xl text-green-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold mb-4\">Email Verified!</h1>\n          <p className=\"text-gray-300 mb-6\">\n            Your email has been successfully verified. You can now log in to your account.\n          </p>\n          <Link \n            to=\"/login\" \n            className=\"mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\"\n          >\n            Go to Login\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n        <FaEnvelopeOpenText className=\"text-6xl text-blue-500 mx-auto mb-6\" />\n        <h1 className=\"text-3xl font-bold mb-4\">Check Your Inbox</h1>\n        {error ? (\n          <p className=\"text-red-500 mb-6\">{error}</p>\n        ) : (\n          <p className=\"text-gray-300 mb-6\">\n            We have sent a verification link to your email address. Please click the link to complete your registration.\n          </p>\n        )}\n        <p className=\"text-gray-400 text-sm mb-6\">\n          Didn't receive the email? Check your spam folder or try signing up again.\n        </p>\n        <Link \n          to=\"/login\" \n          className=\"mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300\"\n        >\n          Back to Login\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default CheckEmail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,eAAe,KAAQ,kBAAkB,CACxD,OAASC,kBAAkB,CAAEC,aAAa,KAAQ,gBAAgB,CAClE,OAASC,IAAI,KAAQ,aAAa,CAClC,OAASC,eAAe,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,YAAY,CAAC,CAAGV,eAAe,CAAC,CAAC,CACxC,KAAM,CAACW,UAAU,CAAEC,aAAa,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAEtCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoB,OAAO,CAAGP,YAAY,CAACQ,GAAG,CAAC,SAAS,CAAC,CAC3C,KAAM,CAAAC,IAAI,CAAGT,YAAY,CAACQ,GAAG,CAAC,MAAM,CAAC,CAErC;AACA,GAAIC,IAAI,GAAK,aAAa,EAAIF,OAAO,CAAE,CACrC,KAAM,CAAAG,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAhB,eAAe,CAACD,IAAI,CAAEc,OAAO,CAAC,CACpCL,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,MAAOG,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDC,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CAAC,OAAS,CACRF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAEDM,WAAW,CAAC,CAAC,CACf,CAAC,IAAM,CACLN,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACJ,YAAY,CAAC,CAAC,CAElB,GAAIG,SAAS,CAAE,CACb,mBACEP,IAAA,QAAKgB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFf,KAAA,QAAKc,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EjB,IAAA,QAAKgB,SAAS,CAAC,wFAAwF,CAAM,CAAC,cAC9GhB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cACpEjB,IAAA,MAAGgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CAAC,CACH,CAAC,CAEV,CAEA,GAAIZ,UAAU,CAAE,CACd,mBACEL,IAAA,QAAKgB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFf,KAAA,QAAKc,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EjB,IAAA,CAACJ,aAAa,EAACoB,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAClEhB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC5DjB,IAAA,MAAGgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gFAElC,CAAG,CAAC,cACJjB,IAAA,CAACP,IAAI,EACHyB,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,mHAAmH,CAAAC,QAAA,CAC9H,aAED,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAEA,mBACEjB,IAAA,QAAKgB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFf,KAAA,QAAKc,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EjB,IAAA,CAACL,kBAAkB,EAACqB,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACtEhB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,CAC5DR,KAAK,cACJT,IAAA,MAAGgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAER,KAAK,CAAI,CAAC,cAE5CT,IAAA,MAAGgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,8GAElC,CAAG,CACJ,cACDjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,2EAE1C,CAAG,CAAC,cACJjB,IAAA,CAACP,IAAI,EACHyB,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,mHAAmH,CAAAC,QAAA,CAC9H,eAED,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAd,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}