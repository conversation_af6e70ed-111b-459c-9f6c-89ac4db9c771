{"name": "@sendgrid/helpers", "description": "<PERSON><PERSON><PERSON>rid NodeJS internal helpers", "version": "8.0.0", "author": "Twilio SendGrid <<EMAIL>> (sendgrid.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Swift <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://sendgrid.com", "repository": {"type": "git", "url": "git://github.com/sendgrid/sendgrid-nodejs.git"}, "publishConfig": {"access": "public"}, "engines": {"node": ">= 12.0.0"}, "tags": ["sendgrid", "helpers"], "dependencies": {"deepmerge": "^4.2.2"}, "gitHead": "b1c831ff33b848e09f9afc644426fd22615f51bd"}