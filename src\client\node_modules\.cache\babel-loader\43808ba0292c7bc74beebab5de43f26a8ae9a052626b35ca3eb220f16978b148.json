{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst splitNameEmail = require('../helpers/split-name-email');\n\n/**\n * Email address class\n */\nclass EmailAddress {\n  /**\n  * Constructor\n  */\n  constructor(data) {\n    //Construct from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n    //String given\n    if (typeof data === 'string') {\n      const [name, email] = splitNameEmail(data);\n      data = {\n        name,\n        email\n      };\n    }\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object or string for EmailAddress data');\n    }\n\n    //Extract name and email\n    const {\n      name,\n      email\n    } = data;\n\n    //Set\n    this.setEmail(email);\n    this.setName(name);\n  }\n\n  /**\n   * Set name\n   */\n  setName(name) {\n    if (typeof name === 'undefined') {\n      return;\n    }\n    if (typeof name !== 'string') {\n      throw new Error('String expected for `name`');\n    }\n    this.name = name;\n  }\n\n  /**\n   * Set email (mandatory)\n   */\n  setEmail(email) {\n    if (typeof email === 'undefined') {\n      throw new Error('Must provide `email`');\n    }\n    if (typeof email !== 'string') {\n      throw new Error('String expected for `email`');\n    }\n    this.email = email;\n  }\n\n  /**\n  * To JSON\n  */\n  toJSON() {\n    //Get properties\n    const {\n      email,\n      name\n    } = this;\n\n    //Initialize with mandatory properties\n    const json = {\n      email\n    };\n\n    //Add name if present\n    if (name !== '') {\n      json.name = name;\n    }\n\n    //Return\n    return json;\n  }\n\n  /**************************************************************************\n   * Static helpers\n   ***/\n\n  /**\n   * Create an EmailAddress instance from given data\n   */\n  static create(data) {\n    //Array?\n    if (Array.isArray(data)) {\n      return data.filter(item => !!item).map(item => this.create(item));\n    }\n\n    //Already instance of EmailAddress class?\n    if (data instanceof EmailAddress) {\n      return data;\n    }\n\n    //Create instance\n    return new EmailAddress(data);\n  }\n}\n\n//Export class\nmodule.exports = EmailAddress;", "map": {"version": 3, "names": ["splitNameEmail", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "data", "fromData", "name", "email", "Error", "setEmail", "setName", "toJSON", "json", "create", "Array", "isArray", "filter", "item", "map", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/email-address.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst splitNameEmail = require('../helpers/split-name-email');\n\n/**\n * Email address class\n */\nclass EmailAddress {\n\n  /**\n\t * Constructor\n\t */\n  constructor(data) {\n\n    //Construct from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //String given\n    if (typeof data === 'string') {\n      const [name, email] = splitNameEmail(data);\n      data = {name, email};\n    }\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object or string for EmailAddress data');\n    }\n\n    //Extract name and email\n    const {name, email} = data;\n\n    //Set\n    this.setEmail(email);\n    this.setName(name);\n  }\n\n  /**\n   * Set name\n   */\n  setName(name) {\n    if (typeof name === 'undefined') {\n      return;\n    }\n    if (typeof name !== 'string') {\n      throw new Error('String expected for `name`');\n    }\n    this.name = name;\n  }\n\n  /**\n   * Set email (mandatory)\n   */\n  setEmail(email) {\n    if (typeof email === 'undefined') {\n      throw new Error('Must provide `email`');\n    }\n    if (typeof email !== 'string') {\n      throw new Error('String expected for `email`');\n    }\n    this.email = email;\n  }\n\n  /**\n\t * To JSON\n\t */\n  toJSON() {\n\n    //Get properties\n    const {email, name} = this;\n\n    //Initialize with mandatory properties\n    const json = {email};\n\n    //Add name if present\n    if (name !== '') {\n      json.name = name;\n    }\n\n    //Return\n    return json;\n  }\n\n  /**************************************************************************\n   * Static helpers\n   ***/\n\n  /**\n   * Create an EmailAddress instance from given data\n   */\n  static create(data) {\n\n    //Array?\n    if (Array.isArray(data)) {\n      return data\n        .filter(item => !!item)\n        .map(item => this.create(item));\n    }\n\n    //Already instance of EmailAddress class?\n    if (data instanceof EmailAddress) {\n      return data;\n    }\n\n    //Create instance\n    return new EmailAddress(data);\n  }\n}\n\n//Export class\nmodule.exports = EmailAddress;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,cAAc,GAAGC,OAAO,CAAC,6BAA6B,CAAC;;AAE7D;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EAEjB;AACF;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAEhB;IACA,IAAIA,IAAI,EAAE;MACR,IAAI,CAACC,QAAQ,CAACD,IAAI,CAAC;IACrB;EACF;;EAEA;AACF;AACA;EACEC,QAAQA,CAACD,IAAI,EAAE;IAEb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAACI,IAAI,CAAC;MAC1CA,IAAI,GAAG;QAACE,IAAI;QAAEC;MAAK,CAAC;IACtB;;IAEA;IACA,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAII,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACA,MAAM;MAACF,IAAI;MAAEC;IAAK,CAAC,GAAGH,IAAI;;IAE1B;IACA,IAAI,CAACK,QAAQ,CAACF,KAAK,CAAC;IACpB,IAAI,CAACG,OAAO,CAACJ,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;EACEI,OAAOA,CAACJ,IAAI,EAAE;IACZ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC/B;IACF;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;IAC/C;IACA,IAAI,CAACF,IAAI,GAAGA,IAAI;EAClB;;EAEA;AACF;AACA;EACEG,QAAQA,CAACF,KAAK,EAAE;IACd,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAChC,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;IACzC;IACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,IAAI,CAACD,KAAK,GAAGA,KAAK;EACpB;;EAEA;AACF;AACA;EACEI,MAAMA,CAAA,EAAG;IAEP;IACA,MAAM;MAACJ,KAAK;MAAED;IAAI,CAAC,GAAG,IAAI;;IAE1B;IACA,MAAMM,IAAI,GAAG;MAACL;IAAK,CAAC;;IAEpB;IACA,IAAID,IAAI,KAAK,EAAE,EAAE;MACfM,IAAI,CAACN,IAAI,GAAGA,IAAI;IAClB;;IAEA;IACA,OAAOM,IAAI;EACb;;EAEA;AACF;AACA;;EAEE;AACF;AACA;EACE,OAAOC,MAAMA,CAACT,IAAI,EAAE;IAElB;IACA,IAAIU,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CACRY,MAAM,CAACC,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CACtBC,GAAG,CAACD,IAAI,IAAI,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,CAAC;IACnC;;IAEA;IACA,IAAIb,IAAI,YAAYF,YAAY,EAAE;MAChC,OAAOE,IAAI;IACb;;IAEA;IACA,OAAO,IAAIF,YAAY,CAACE,IAAI,CAAC;EAC/B;AACF;;AAEA;AACAe,MAAM,CAACC,OAAO,GAAGlB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}