{"ast": null, "code": "'use strict';\n\n/**\n * Wrap substitutions\n */\nmodule.exports = function wrap(substitutions, left = '{{', right = '}}') {\n  //Process arrays\n  if (Array.isArray(substitutions)) {\n    return substitutions.map(subs => wrap(subs, left, right));\n  }\n\n  //Initialize new wrapped object\n  const wrapped = {};\n\n  //Map substitutions and ensure string for value\n  for (const key in substitutions) {\n    //istanbul ignore else\n    if (substitutions.hasOwnProperty(key)) {\n      wrapped[left + key + right] = String(substitutions[key]);\n    }\n  }\n\n  //Return wrapped substitutions\n  return wrapped;\n};", "map": {"version": 3, "names": ["module", "exports", "wrap", "substitutions", "left", "right", "Array", "isArray", "map", "subs", "wrapped", "key", "hasOwnProperty", "String"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js"], "sourcesContent": ["'use strict';\n\n/**\n * Wrap substitutions\n */\nmodule.exports = function wrap(substitutions, left = '{{', right = '}}') {\n\n  //Process arrays\n  if (Array.isArray(substitutions)) {\n    return substitutions.map(subs => wrap(subs, left, right));\n  }\n\n  //Initialize new wrapped object\n  const wrapped = {};\n\n  //Map substitutions and ensure string for value\n  for (const key in substitutions) {\n    //istanbul ignore else\n    if (substitutions.hasOwnProperty(key)) {\n      wrapped[left + key + right] = String(substitutions[key]);\n    }\n  }\n\n  //Return wrapped substitutions\n  return wrapped;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,aAAa,EAAEC,IAAI,GAAG,IAAI,EAAEC,KAAK,GAAG,IAAI,EAAE;EAEvE;EACA,IAAIC,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC,EAAE;IAChC,OAAOA,aAAa,CAACK,GAAG,CAACC,IAAI,IAAIP,IAAI,CAACO,IAAI,EAAEL,IAAI,EAAEC,KAAK,CAAC,CAAC;EAC3D;;EAEA;EACA,MAAMK,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA,KAAK,MAAMC,GAAG,IAAIR,aAAa,EAAE;IAC/B;IACA,IAAIA,aAAa,CAACS,cAAc,CAACD,GAAG,CAAC,EAAE;MACrCD,OAAO,CAACN,IAAI,GAAGO,GAAG,GAAGN,KAAK,CAAC,GAAGQ,MAAM,CAACV,aAAa,CAACQ,GAAG,CAAC,CAAC;IAC1D;EACF;;EAEA;EACA,OAAOD,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}