{"ast": null, "code": "'use strict';\n\n/**\n * Deep cloning helper for objects\n */\nmodule.exports = function deepClone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n};", "map": {"version": 3, "names": ["module", "exports", "deepClone", "obj", "JSON", "parse", "stringify"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/deep-clone.js"], "sourcesContent": ["'use strict';\n\n/**\n * Deep cloning helper for objects\n */\nmodule.exports = function deepClone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,GAAG,EAAE;EACvC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAAC,CAAC;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}