{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst convertKeys = require('./convert-keys');\nconst strToSnakeCase = require('./str-to-snake-case');\n\n/**\n * Convert object keys to snake case\n */\nmodule.exports = function toSnakeCase(obj, ignored) {\n  return convertKeys(obj, strToSnakeCase, ignored);\n};", "map": {"version": 3, "names": ["convertKeys", "require", "strToSnakeCase", "module", "exports", "toSnakeCase", "obj", "ignored"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/to-snake-case.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst convertKeys = require('./convert-keys');\nconst strToSnakeCase = require('./str-to-snake-case');\n\n/**\n * Convert object keys to snake case\n */\nmodule.exports = function toSnakeCase(obj, ignored) {\n  return convertKeys(obj, strToSnakeCase, ignored);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,qBAAqB,CAAC;;AAErD;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAClD,OAAOP,WAAW,CAACM,GAAG,EAAEJ,cAAc,EAAEK,OAAO,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}