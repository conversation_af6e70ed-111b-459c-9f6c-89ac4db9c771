{"ast": null, "code": "import React,{useEffect}from'react';import{motion,useAnimation}from'framer-motion';import{useInView}from'react-intersection-observer';import{FaCheckCircle,FaUsers,FaGlobe,FaShieldAlt,FaAward,FaBitcoin}from'react-icons/fa';import neekahImage from'../assets/images/neekah.jpg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const About=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"pt-16\",children:[/*#__PURE__*/_jsx(AboutHero,{}),/*#__PURE__*/_jsx(MissionVision,{}),/*#__PURE__*/_jsx(CoreValues,{}),/*#__PURE__*/_jsx(TeamSection,{}),/*#__PURE__*/_jsx(Timeline,{})]});};// About Hero Section\nconst AboutHero=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"relative bg-primary-dark text-white py-20\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"container-custom relative z-10\",children:/*#__PURE__*/_jsxs(motion.div,{className:\"max-w-3xl\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5},children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-bold mb-6\",children:\"About BlazeTrade\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-300 mb-8\",children:\"Your trusted partner in the world of cryptocurrency exchange and trading. Learn about our mission, values, and the team behind BlazeTrade.\"})]})})]});};// Mission & Vision Section\nconst MissionVision=()=>{const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.3}}},children:[/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-accent uppercase tracking-wider\",children:\"Our Mission\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold mb-6 text-primary-dark\",children:\"Making Cryptocurrency Accessible to Everyone\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"At BlazeTrade, our mission is to provide a secure, reliable, and user-friendly platform for cryptocurrency exchange and trading. We aim to bridge the gap between traditional finance and the digital asset economy, making cryptocurrency accessible to everyone regardless of their technical expertise or investment experience.\"}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-3\",children:[\"Providing secure and reliable cryptocurrency services\",\"Offering transparent and competitive fee structures\",\"Delivering exceptional customer support and education\",\"Continuously innovating to improve user experience\"].map((item,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(FaCheckCircle,{className:\"text-accent mt-1 mr-2 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:item})]},index))})]}),/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,x:30},visible:{opacity:1,x:0,transition:{duration:0.6}}},className:\"bg-gray-50 p-8 rounded-lg border border-gray-100\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-accent uppercase tracking-wider\",children:\"Our Vision\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold mb-6 text-primary-dark\",children:\"Shaping the Future of Digital Finance\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"We envision a world where digital assets are seamlessly integrated into the global financial ecosystem, providing greater financial freedom, security, and opportunity for individuals and businesses worldwide. BlazeTrade aims to be at the forefront of this transformation, setting new standards for excellence in the cryptocurrency industry.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 bg-white rounded border-l-4 border-accent shadow-sm\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"italic text-gray-700\",children:\"\\\"Our vision is to create a world where everyone has equal access to the benefits of blockchain technology and cryptocurrency, empowering financial independence and innovation across the globe.\\\"\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-500\",children:\"\\u2014 BlazeTrade Founding Team\"})]})]})]})})});};// Core Values Section\nconst CoreValues=()=>{const values=[{icon:/*#__PURE__*/_jsx(FaShieldAlt,{className:\"text-3xl text-accent\"}),title:\"Security\",description:\"We prioritize the security of our users' assets and data above all else, implementing industry-leading security measures.\"},{icon:/*#__PURE__*/_jsx(FaUsers,{className:\"text-3xl text-accent\"}),title:\"Transparency\",description:\"We believe in complete transparency in our operations, fee structures, and communications with our users.\"},{icon:/*#__PURE__*/_jsx(FaAward,{className:\"text-3xl text-accent\"}),title:\"Excellence\",description:\"We strive for excellence in everything we do, from our platform's performance to our customer service.\"},{icon:/*#__PURE__*/_jsx(FaGlobe,{className:\"text-3xl text-accent\"}),title:\"Accessibility\",description:\"We are committed to making cryptocurrency accessible to everyone, regardless of their background or experience.\"}];return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center max-w-3xl mx-auto mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:\"Our Core Values\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"These principles guide everything we do at BlazeTrade, from product development to customer service.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:values.map((value,index)=>/*#__PURE__*/_jsx(ValueCard,{icon:value.icon,title:value.title,description:value.description,index:index},index))})]})});};// Team Section\nconst TeamSection=()=>{const ambassador={name:\"Neekah\",position:\"Brand Ambassador\",bio:\"Neekah is a passionate influential representative of our brand and she's helping to spread awareness and adoption of BlazeTrade's services.\",image:neekahImage};const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center max-w-3xl mx-auto mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:\"Meet Our Brand Ambassador\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"Our brand ambassador is dedicated to representing BlazeTrade and connecting with our community.\"})]}),/*#__PURE__*/_jsx(motion.div,{ref:ref,className:\"flex justify-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.2}}},children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full md:w-2/3 lg:w-1/2\",children:/*#__PURE__*/_jsx(TeamMember,{name:ambassador.name,position:ambassador.position,bio:ambassador.bio,image:ambassador.image,index:0})})})]})});};// Timeline Section\nconst Timeline=()=>{const milestones=[{year:\"2019\",title:\"BlazeTradee Founded\",description:\"BlazeTrade was established with a vision to create a secure and accessible cryptocurrency exchange platform.\"},{year:\"2020\",title:\"Brand Launch\",description:\"Our cryptocurrency exchange brand, BlazeTradee, was officially launched, offering Bitcoin and major altcoin trading.\"},{year:\"2021\",title:\"Security Certification\",description:\"BlazeTrade received industry-leading security certifications and implemented enhanced protection measures.\"},{year:\"2022\",title:\"Global Expansion\",description:\"We expanded our services to multiple countries, making cryptocurrency trading accessible to more users worldwide.\"},{year:\"2023\",title:\"Advanced Trading Tools\",description:\"Introduced advanced trading tools and features, including buying of giftcards and giftcard redemption.\"},{year:\"2025\",title:\"web App Launch\",description:\"Launched our web application, allowing users to know more about cryptocurrencies and how to trade with BlazeTradee anytime, anywhere.\"}];const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center max-w-3xl mx-auto mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:\"Our Journey\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"From our founding to the present day, we've been committed to innovation and excellence in cryptocurrency services.\"})]}),/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"relative\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.3}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gray-200 hidden md:block\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-12 relative\",children:milestones.map((milestone,index)=>/*#__PURE__*/_jsx(TimelineItem,{year:milestone.year,title:milestone.title,description:milestone.description,index:index,isEven:index%2===0},index))})]})]})});};// Value Card Component\nconst ValueCard=_ref=>{let{icon,title,description,index}=_ref;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 text-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-primary-dark bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4\",children:icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-3 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:description})]});};// Team Member Component\nconst TeamMember=_ref2=>{let{name,position,bio,image,index}=_ref2;return/*#__PURE__*/_jsxs(motion.div,{className:\"bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300\",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-200\",children:/*#__PURE__*/_jsx(\"img\",{src:image,alt:name,className:\"w-full h-auto object-contain\",onError:e=>{e.target.src=`https://ui-avatars.com/api/?name=${name.replace(' ','+')}&background=0A2463&color=fff&size=256`;}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-1 text-primary-dark\",children:name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-accent mb-3\",children:position}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:bio})]})]});};// Timeline Item Component\nconst TimelineItem=_ref3=>{let{year,title,description,index,isEven}=_ref3;return/*#__PURE__*/_jsxs(motion.div,{className:`flex flex-col md:flex-row ${isEven?'md:flex-row-reverse':''}`,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative flex items-center justify-center md:w-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-8 w-8 rounded-full bg-primary-dark text-white flex items-center justify-center z-10\",children:/*#__PURE__*/_jsx(FaBitcoin,{})})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 pt-4 md:pt-0 md:px-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-bold text-accent mb-1\",children:year}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:description})]})})]});};export default About;", "map": {"version": 3, "names": ["React", "useEffect", "motion", "useAnimation", "useInView", "FaCheckCircle", "FaUsers", "FaGlobe", "FaShieldAlt", "FaAward", "FaBitcoin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "About", "className", "children", "AboutHero", "MissionVision", "CoreValues", "TeamSection", "Timeline", "div", "initial", "opacity", "y", "animate", "transition", "duration", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "x", "map", "item", "index", "values", "icon", "title", "description", "value", "ValueCard", "ambassador", "name", "position", "bio", "image", "TeamMember", "milestones", "year", "milestone", "TimelineItem", "isEven", "_ref", "delay", "_ref2", "src", "alt", "onError", "e", "target", "replace", "_ref3"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/About.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaCheckCircle, FaUsers, FaGlobe, FaShieldAlt, FaAward, FaBitcoin } from 'react-icons/fa';\nimport neekahImage from '../assets/images/neekah.jpg';\n\nconst About = () => {\n  return (\n    <div className=\"pt-16\">\n      {/* Hero Section */}\n      <AboutHero />\n      \n      {/* Mission & Vision */}\n      <MissionVision />\n      \n      {/* Core Values */}\n      <CoreValues />\n      \n      {/* Team Section */}\n      <TeamSection />\n      \n      {/* Timeline */}\n      <Timeline />\n    </div>\n  );\n};\n\n// About Hero Section\nconst AboutHero = () => {\n  return (\n    <section className=\"relative bg-primary-dark text-white py-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"></div>\n        <div className=\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10\">\n        <motion.div \n          className=\"max-w-3xl\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">About BlazeTrade</h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Your trusted partner in the world of cryptocurrency exchange and trading. \n            Learn about our mission, values, and the team behind BlazeTrade.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Mission & Vision Section\nconst MissionVision = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.3\n              }\n            }\n          }}\n        >\n          {/* Mission */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: -30 },\n              visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }\n            }}\n          >\n            <div className=\"mb-4\">\n              <span className=\"text-sm font-semibold text-accent uppercase tracking-wider\">Our Mission</span>\n            </div>\n            <h2 className=\"text-3xl font-bold mb-6 text-primary-dark\">Making Cryptocurrency Accessible to Everyone</h2>\n            <p className=\"text-gray-600 mb-6\">\n              At BlazeTrade, our mission is to provide a secure, reliable, and user-friendly platform for cryptocurrency exchange and trading. \n              We aim to bridge the gap between traditional finance and the digital asset economy, making cryptocurrency accessible to everyone \n              regardless of their technical expertise or investment experience.\n            </p>\n            <ul className=\"space-y-3\">\n              {[\n                \"Providing secure and reliable cryptocurrency services\",\n                \"Offering transparent and competitive fee structures\",\n                \"Delivering exceptional customer support and education\",\n                \"Continuously innovating to improve user experience\"\n              ].map((item, index) => (\n                <li key={index} className=\"flex items-start\">\n                  <FaCheckCircle className=\"text-accent mt-1 mr-2 flex-shrink-0\" />\n                  <span className=\"text-gray-700\">{item}</span>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n          \n          {/* Vision */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: 30 },\n              visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }\n            }}\n            className=\"bg-gray-50 p-8 rounded-lg border border-gray-100\"\n          >\n            <div className=\"mb-4\">\n              <span className=\"text-sm font-semibold text-accent uppercase tracking-wider\">Our Vision</span>\n            </div>\n            <h2 className=\"text-3xl font-bold mb-6 text-primary-dark\">Shaping the Future of Digital Finance</h2>\n            <p className=\"text-gray-600 mb-6\">\n              We envision a world where digital assets are seamlessly integrated into the global financial ecosystem, \n              providing greater financial freedom, security, and opportunity for individuals and businesses worldwide. \n              BlazeTrade aims to be at the forefront of this transformation, setting new standards for excellence in the \n              cryptocurrency industry.\n            </p>\n            <div className=\"p-4 bg-white rounded border-l-4 border-accent shadow-sm\">\n              <p className=\"italic text-gray-700\">\n                \"Our vision is to create a world where everyone has equal access to the benefits of blockchain technology \n                and cryptocurrency, empowering financial independence and innovation across the globe.\"\n              </p>\n              <p className=\"mt-2 text-sm text-gray-500\">— BlazeTrade Founding Team</p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Core Values Section\nconst CoreValues = () => {\n  const values = [\n    {\n      icon: <FaShieldAlt className=\"text-3xl text-accent\" />,\n      title: \"Security\",\n      description: \"We prioritize the security of our users' assets and data above all else, implementing industry-leading security measures.\"\n    },\n    {\n      icon: <FaUsers className=\"text-3xl text-accent\" />,\n      title: \"Transparency\",\n      description: \"We believe in complete transparency in our operations, fee structures, and communications with our users.\"\n    },\n    {\n      icon: <FaAward className=\"text-3xl text-accent\" />,\n      title: \"Excellence\",\n      description: \"We strive for excellence in everything we do, from our platform's performance to our customer service.\"\n    },\n    {\n      icon: <FaGlobe className=\"text-3xl text-accent\" />,\n      title: \"Accessibility\",\n      description: \"We are committed to making cryptocurrency accessible to everyone, regardless of their background or experience.\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center max-w-3xl mx-auto mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Our Core Values</h2>\n          <p className=\"text-lg text-gray-600\">\n            These principles guide everything we do at BlazeTrade, from product development to customer service.\n          </p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {values.map((value, index) => (\n            <ValueCard \n              key={index}\n              icon={value.icon}\n              title={value.title}\n              description={value.description}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Team Section\nconst TeamSection = () => {\n  const ambassador = {\n    name: \"Neekah\",\n    position: \"Brand Ambassador\",\n    bio: \"Neekah is a passionate influential representative of our brand and she's helping to spread awareness and adoption of BlazeTrade's services.\",\n    image: neekahImage\n  };\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"text-center max-w-3xl mx-auto mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Meet Our Brand Ambassador</h2>\n          <p className=\"text-lg text-gray-600\">\n            Our brand ambassador is dedicated to representing BlazeTrade and connecting with our community.\n          </p>\n        </div>\n        \n        <motion.div \n          ref={ref}\n          className=\"flex justify-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.2\n              }\n            }\n          }}\n        >\n          <div className=\"w-full md:w-2/3 lg:w-1/2\">\n            <TeamMember \n              name={ambassador.name}\n              position={ambassador.position}\n              bio={ambassador.bio}\n              image={ambassador.image}\n              index={0}\n            />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Timeline Section\nconst Timeline = () => {\n  const milestones = [\n    {\n      year: \"2019\",\n      title: \"BlazeTradee Founded\",\n      description: \"BlazeTrade was established with a vision to create a secure and accessible cryptocurrency exchange platform.\"\n    },\n    {\n      year: \"2020\",\n      title: \"Brand Launch\",\n      description: \"Our cryptocurrency exchange brand, BlazeTradee, was officially launched, offering Bitcoin and major altcoin trading.\"\n    },\n    {\n      year: \"2021\",\n      title: \"Security Certification\",\n      description: \"BlazeTrade received industry-leading security certifications and implemented enhanced protection measures.\"\n    },\n    {\n      year: \"2022\",\n      title: \"Global Expansion\",\n      description: \"We expanded our services to multiple countries, making cryptocurrency trading accessible to more users worldwide.\"\n    },\n    {\n      year: \"2023\",\n      title: \"Advanced Trading Tools\",\n      description: \"Introduced advanced trading tools and features, including buying of giftcards and giftcard redemption.\"\n    },\n    {\n      year: \"2025\",\n      title: \"web App Launch\",\n      description: \"Launched our web application, allowing users to know more about cryptocurrencies and how to trade with BlazeTradee anytime, anywhere.\"\n    }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center max-w-3xl mx-auto mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Our Journey</h2>\n          <p className=\"text-lg text-gray-600\">\n            From our founding to the present day, we've been committed to innovation and excellence in cryptocurrency services.\n          </p>\n        </div>\n        \n        <motion.div \n          ref={ref}\n          className=\"relative\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.3\n              }\n            }\n          }}\n        >\n          {/* Timeline Line */}\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gray-200 hidden md:block\"></div>\n          \n          {/* Timeline Items */}\n          <div className=\"space-y-12 relative\">\n            {milestones.map((milestone, index) => (\n              <TimelineItem \n                key={index}\n                year={milestone.year}\n                title={milestone.title}\n                description={milestone.description}\n                index={index}\n                isEven={index % 2 === 0}\n              />\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Value Card Component\nconst ValueCard = ({ icon, title, description, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 text-center\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"w-16 h-16 bg-primary-dark bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4\">\n        {icon}\n      </div>\n      <h3 className=\"text-xl font-semibold mb-3 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600\">{description}</p>\n    </motion.div>\n  );\n};\n\n// Team Member Component\nconst TeamMember = ({ name, position, bio, image, index }) => {\n  return (\n    <motion.div \n      className=\"bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300\"\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"bg-gray-200\">\n        <img \n          src={image} \n          alt={name} \n          className=\"w-full h-auto object-contain\"\n          onError={(e) => {\n            e.target.src = `https://ui-avatars.com/api/?name=${name.replace(' ', '+')}&background=0A2463&color=fff&size=256`;\n          }}\n        />\n      </div>\n      <div className=\"p-6\">\n        <h3 className=\"text-xl font-semibold mb-1 text-primary-dark\">{name}</h3>\n        <p className=\"text-accent mb-3\">{position}</p>\n        <p className=\"text-gray-600 text-sm\">{bio}</p>\n      </div>\n    </motion.div>\n  );\n};\n\n// Timeline Item Component\nconst TimelineItem = ({ year, title, description, index, isEven }) => {\n  return (\n    <motion.div \n      className={`flex flex-col md:flex-row ${isEven ? 'md:flex-row-reverse' : ''}`}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"md:w-1/2\"></div>\n      <div className=\"relative flex items-center justify-center md:w-0\">\n        <div className=\"h-8 w-8 rounded-full bg-primary-dark text-white flex items-center justify-center z-10\">\n          <FaBitcoin />\n        </div>\n      </div>\n      <div className=\"md:w-1/2 pt-4 md:pt-0 md:px-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n          <div className=\"text-sm font-bold text-accent mb-1\">{year}</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-primary-dark\">{title}</h3>\n          <p className=\"text-gray-600\">{description}</p>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default About;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,YAAY,KAAQ,eAAe,CACpD,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OAASC,aAAa,CAAEC,OAAO,CAAEC,OAAO,CAAEC,WAAW,CAAEC,OAAO,CAAEC,SAAS,KAAQ,gBAAgB,CACjG,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,mBACED,KAAA,QAAKE,SAAS,CAAC,OAAO,CAAAC,QAAA,eAEpBL,IAAA,CAACM,SAAS,GAAE,CAAC,cAGbN,IAAA,CAACO,aAAa,GAAE,CAAC,cAGjBP,IAAA,CAACQ,UAAU,GAAE,CAAC,cAGdR,IAAA,CAACS,WAAW,GAAE,CAAC,cAGfT,IAAA,CAACU,QAAQ,GAAE,CAAC,EACT,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAJ,SAAS,CAAGA,CAAA,GAAM,CACtB,mBACEJ,KAAA,YAASE,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eAE5DH,KAAA,QAAKE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CL,IAAA,QAAKI,SAAS,CAAC,qEAAqE,CAAM,CAAC,cAC3FJ,IAAA,QAAKI,SAAS,CAAC,wEAAwE,CAAM,CAAC,EAC3F,CAAC,cAENJ,IAAA,QAAKI,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAC,WAAW,CACrBQ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,eAE9BL,IAAA,OAAII,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACzEL,IAAA,MAAGI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,4IAG1C,CAAG,CAAC,EACM,CAAC,CACV,CAAC,EACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAW,QAAQ,CAAG5B,YAAY,CAAC,CAAC,CAC/B,KAAM,CAAC6B,GAAG,CAAEC,MAAM,CAAC,CAAG7B,SAAS,CAAC,CAC9B8B,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFlC,SAAS,CAAC,IAAM,CACd,GAAIgC,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACEpB,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCL,IAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTQ,GAAG,CAAEA,GAAI,CACTf,SAAS,CAAC,qDAAqD,CAC/DQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEG,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAE,CAAC,CACtBa,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVW,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAtB,QAAA,eAGFH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTa,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAC,CAAEe,CAAC,CAAE,CAAC,EAAG,CAAC,CAC9BF,OAAO,CAAE,CAAEb,OAAO,CAAE,CAAC,CAAEe,CAAC,CAAE,CAAC,CAAEZ,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAZ,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBL,IAAA,SAAMI,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,CAC5F,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,8CAA4C,CAAI,CAAC,cAC3GL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,qUAIlC,CAAG,CAAC,cACJL,IAAA,OAAII,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtB,CACC,uDAAuD,CACvD,qDAAqD,CACrD,uDAAuD,CACvD,oDAAoD,CACrD,CAACwB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAChB7B,KAAA,OAAgBE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC1CL,IAAA,CAACR,aAAa,EAACY,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACjEJ,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEyB,IAAI,CAAO,CAAC,GAFtCC,KAGL,CACL,CAAC,CACA,CAAC,EACK,CAAC,cAGb7B,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTa,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAC,CAAEe,CAAC,CAAE,EAAG,CAAC,CAC7BF,OAAO,CAAE,CAAEb,OAAO,CAAE,CAAC,CAAEe,CAAC,CAAE,CAAC,CAAEZ,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CACFb,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAE5DL,IAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBL,IAAA,SAAMI,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,CAC3F,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,uCAAqC,CAAI,CAAC,cACpGL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,sVAKlC,CAAG,CAAC,cACJH,KAAA,QAAKE,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACtEL,IAAA,MAAGI,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,qMAGpC,CAAG,CAAC,cACJL,IAAA,MAAGI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iCAA0B,CAAG,CAAC,EACrE,CAAC,EACI,CAAC,EACH,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAwB,MAAM,CAAG,CACb,CACEC,IAAI,cAAEjC,IAAA,CAACL,WAAW,EAACS,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACtD8B,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,2HACf,CAAC,CACD,CACEF,IAAI,cAAEjC,IAAA,CAACP,OAAO,EAACW,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAClD8B,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,2GACf,CAAC,CACD,CACEF,IAAI,cAAEjC,IAAA,CAACJ,OAAO,EAACQ,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAClD8B,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,wGACf,CAAC,CACD,CACEF,IAAI,cAAEjC,IAAA,CAACN,OAAO,EAACU,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAClD8B,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,iHACf,CAAC,CACF,CAED,mBACEnC,IAAA,YAASI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACrCH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BH,KAAA,QAAKE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDL,IAAA,OAAII,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1FL,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sGAErC,CAAG,CAAC,EACD,CAAC,cAENL,IAAA,QAAKI,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClE2B,MAAM,CAACH,GAAG,CAAC,CAACO,KAAK,CAAEL,KAAK,gBACvB/B,IAAA,CAACqC,SAAS,EAERJ,IAAI,CAAEG,KAAK,CAACH,IAAK,CACjBC,KAAK,CAAEE,KAAK,CAACF,KAAM,CACnBC,WAAW,CAAEC,KAAK,CAACD,WAAY,CAC/BJ,KAAK,CAAEA,KAAM,EAJRA,KAKN,CACF,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAtB,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAA6B,UAAU,CAAG,CACjBC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,kBAAkB,CAC5BC,GAAG,CAAE,6IAA6I,CAClJC,KAAK,CAAE5C,WACT,CAAC,CAED,KAAM,CAAAoB,QAAQ,CAAG5B,YAAY,CAAC,CAAC,CAC/B,KAAM,CAAC6B,GAAG,CAAEC,MAAM,CAAC,CAAG7B,SAAS,CAAC,CAC9B8B,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFlC,SAAS,CAAC,IAAM,CACd,GAAIgC,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACEpB,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BH,KAAA,QAAKE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDL,IAAA,OAAII,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACpGL,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iGAErC,CAAG,CAAC,EACD,CAAC,cAENL,IAAA,CAACX,MAAM,CAACsB,GAAG,EACTQ,GAAG,CAAEA,GAAI,CACTf,SAAS,CAAC,qBAAqB,CAC/BQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEG,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAE,CAAC,CACtBa,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVW,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAtB,QAAA,cAEFL,IAAA,QAAKI,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCL,IAAA,CAAC2C,UAAU,EACTJ,IAAI,CAAED,UAAU,CAACC,IAAK,CACtBC,QAAQ,CAAEF,UAAU,CAACE,QAAS,CAC9BC,GAAG,CAAEH,UAAU,CAACG,GAAI,CACpBC,KAAK,CAAEJ,UAAU,CAACI,KAAM,CACxBX,KAAK,CAAE,CAAE,CACV,CAAC,CACC,CAAC,CACI,CAAC,EACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAArB,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAAkC,UAAU,CAAG,CACjB,CACEC,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CAAE,8GACf,CAAC,CACD,CACEU,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,sHACf,CAAC,CACD,CACEU,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,4GACf,CAAC,CACD,CACEU,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,mHACf,CAAC,CACD,CACEU,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,wGACf,CAAC,CACD,CACEU,IAAI,CAAE,MAAM,CACZX,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,uIACf,CAAC,CACF,CAED,KAAM,CAAAjB,QAAQ,CAAG5B,YAAY,CAAC,CAAC,CAC/B,KAAM,CAAC6B,GAAG,CAAEC,MAAM,CAAC,CAAG7B,SAAS,CAAC,CAC9B8B,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFlC,SAAS,CAAC,IAAM,CACd,GAAIgC,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACEpB,IAAA,YAASI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACrCH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BH,KAAA,QAAKE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDL,IAAA,OAAII,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cACtFL,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qHAErC,CAAG,CAAC,EACD,CAAC,cAENH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTQ,GAAG,CAAEA,GAAI,CACTf,SAAS,CAAC,UAAU,CACpBQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEG,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAE,CAAC,CACtBa,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVW,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAtB,QAAA,eAGFL,IAAA,QAAKI,SAAS,CAAC,qFAAqF,CAAM,CAAC,cAG3GJ,IAAA,QAAKI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCuC,UAAU,CAACf,GAAG,CAAC,CAACiB,SAAS,CAAEf,KAAK,gBAC/B/B,IAAA,CAAC+C,YAAY,EAEXF,IAAI,CAAEC,SAAS,CAACD,IAAK,CACrBX,KAAK,CAAEY,SAAS,CAACZ,KAAM,CACvBC,WAAW,CAAEW,SAAS,CAACX,WAAY,CACnCJ,KAAK,CAAEA,KAAM,CACbiB,MAAM,CAAEjB,KAAK,CAAG,CAAC,GAAK,CAAE,EALnBA,KAMN,CACF,CAAC,CACC,CAAC,EACI,CAAC,EACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAM,SAAS,CAAGY,IAAA,EAAyC,IAAxC,CAAEhB,IAAI,CAAEC,KAAK,CAAEC,WAAW,CAAEJ,KAAM,CAAC,CAAAkB,IAAA,CACpD,KAAM,CAAA/B,QAAQ,CAAG5B,YAAY,CAAC,CAAC,CAC/B,KAAM,CAAC6B,GAAG,CAAEC,MAAM,CAAC,CAAG7B,SAAS,CAAC,CAC9B8B,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEFlC,SAAS,CAAC,IAAM,CACd,GAAIgC,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACElB,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTQ,GAAG,CAAEA,GAAI,CACTf,SAAS,CAAC,8FAA8F,CACxGQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEG,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BY,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbiC,KAAK,CAAEnB,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAA1B,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,oGAAoG,CAAAC,QAAA,CAChH4B,IAAI,CACF,CAAC,cACNjC,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAE6B,KAAK,CAAK,CAAC,cACzElC,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE8B,WAAW,CAAI,CAAC,EACpC,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAQ,UAAU,CAAGQ,KAAA,EAA2C,IAA1C,CAAEZ,IAAI,CAAEC,QAAQ,CAAEC,GAAG,CAAEC,KAAK,CAAEX,KAAM,CAAC,CAAAoB,KAAA,CACvD,mBACEjD,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAC,8FAA8F,CACxGoB,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BY,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbiC,KAAK,CAAEnB,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAA1B,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BL,IAAA,QACEoD,GAAG,CAAEV,KAAM,CACXW,GAAG,CAAEd,IAAK,CACVnC,SAAS,CAAC,8BAA8B,CACxCkD,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,CAAG,oCAAoCb,IAAI,CAACkB,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,uCAAuC,CAClH,CAAE,CACH,CAAC,CACC,CAAC,cACNvD,KAAA,QAAKE,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBL,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEkC,IAAI,CAAK,CAAC,cACxEvC,IAAA,MAAGI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEmC,QAAQ,CAAI,CAAC,cAC9CxC,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEoC,GAAG,CAAI,CAAC,EAC3C,CAAC,EACI,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAM,YAAY,CAAGW,KAAA,EAAiD,IAAhD,CAAEb,IAAI,CAAEX,KAAK,CAAEC,WAAW,CAAEJ,KAAK,CAAEiB,MAAO,CAAC,CAAAU,KAAA,CAC/D,mBACExD,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAE,6BAA6B4C,MAAM,CAAG,qBAAqB,CAAG,EAAE,EAAG,CAC9ExB,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEZ,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BY,OAAO,CAAE,CACPb,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbiC,KAAK,CAAEnB,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAA1B,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,UAAU,CAAM,CAAC,cAChCJ,IAAA,QAAKI,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/DL,IAAA,QAAKI,SAAS,CAAC,uFAAuF,CAAAC,QAAA,cACpGL,IAAA,CAACH,SAAS,GAAE,CAAC,CACV,CAAC,CACH,CAAC,cACNG,IAAA,QAAKI,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CH,KAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,IAAA,QAAKI,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAEwC,IAAI,CAAM,CAAC,cAChE7C,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAE6B,KAAK,CAAK,CAAC,cACzElC,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE8B,WAAW,CAAI,CAAC,EAC3C,CAAC,CACH,CAAC,EACI,CAAC,CAEjB,CAAC,CAED,cAAe,CAAAhC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}