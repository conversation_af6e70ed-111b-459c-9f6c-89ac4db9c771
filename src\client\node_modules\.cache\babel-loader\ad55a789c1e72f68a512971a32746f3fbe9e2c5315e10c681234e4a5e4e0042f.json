{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{FaEnvelopeOpenText}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckEmail=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gray-900 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\",children:[/*#__PURE__*/_jsx(FaEnvelopeOpenText,{className:\"text-6xl text-blue-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-4\",children:\"Check Your Inbox\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-6\",children:\"We have sent a verification link to your email address. Please click the link to complete your registration.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm\",children:\"Didn't receive the email? Check your spam folder or try signing up again.\"}),/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"mt-8 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\",children:\"Back to Login\"})]})});};export default CheckEmail;", "map": {"version": 3, "names": ["React", "Link", "FaEnvelopeOpenText", "jsx", "_jsx", "jsxs", "_jsxs", "CheckEmail", "className", "children", "to"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/CheckEmail.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaEnvelopeOpenText } from 'react-icons/fa';\n\nconst CheckEmail = () => {\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900 text-white\">\n      <div className=\"bg-gray-800 p-8 rounded-lg shadow-xl text-center max-w-md w-full\">\n        <FaEnvelopeOpenText className=\"text-6xl text-blue-500 mx-auto mb-6\" />\n        <h1 className=\"text-3xl font-bold mb-4\">Check Your Inbox</h1>\n        <p className=\"text-gray-300 mb-6\">\n          We have sent a verification link to your email address. Please click the link to complete your registration.\n        </p>\n        <p className=\"text-gray-400 text-sm\">\n          Didn't receive the email? Check your spam folder or try signing up again.\n        </p>\n        <Link to=\"/login\" className=\"mt-8 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300\">\n          Back to Login\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default CheckEmail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,kBAAkB,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACEH,IAAA,QAAKI,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFH,KAAA,QAAKE,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EL,IAAA,CAACF,kBAAkB,EAACM,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACtEJ,IAAA,OAAII,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC7DL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,8GAElC,CAAG,CAAC,cACJL,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2EAErC,CAAG,CAAC,cACJL,IAAA,CAACH,IAAI,EAACS,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,mHAAmH,CAAAC,QAAA,CAAC,eAEhJ,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}