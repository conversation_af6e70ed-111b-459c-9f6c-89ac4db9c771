{"ast": null, "code": "'use strict';\n\n/**\n * Split name and email address from string\n */\nmodule.exports = function splitNameEmail(str) {\n  //If no email bracket present, return as is\n  if (str.indexOf('<') === -1) {\n    return ['', str];\n  }\n\n  //Split into name and email\n  let [name, email] = str.split('<');\n\n  //Trim and fix up\n  name = name.trim();\n  email = email.replace('>', '').trim();\n\n  //Return as array\n  return [name, email];\n};", "map": {"version": 3, "names": ["module", "exports", "splitNameEmail", "str", "indexOf", "name", "email", "split", "trim", "replace"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/split-name-email.js"], "sourcesContent": ["'use strict';\n\n/**\n * Split name and email address from string\n */\nmodule.exports = function splitNameEmail(str) {\n\n  //If no email bracket present, return as is\n  if (str.indexOf('<') === -1) {\n    return ['', str];\n  }\n\n  //Split into name and email\n  let [name, email] = str.split('<');\n\n  //Trim and fix up\n  name = name.trim();\n  email = email.replace('>', '').trim();\n\n  //Return as array\n  return [name, email];\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,GAAG,EAAE;EAE5C;EACA,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC3B,OAAO,CAAC,EAAE,EAAED,GAAG,CAAC;EAClB;;EAEA;EACA,IAAI,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC;;EAElC;EACAF,IAAI,GAAGA,IAAI,CAACG,IAAI,CAAC,CAAC;EAClBF,KAAK,GAAGA,KAAK,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACD,IAAI,CAAC,CAAC;;EAErC;EACA,OAAO,CAACH,IAAI,EAAEC,KAAK,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}