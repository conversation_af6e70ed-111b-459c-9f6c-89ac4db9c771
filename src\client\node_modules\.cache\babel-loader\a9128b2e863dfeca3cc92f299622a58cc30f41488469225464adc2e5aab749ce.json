{"ast": null, "code": "'use strict';\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToSnakeCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str.trim().replace(/(\\s*\\-*\\b\\w|[A-Z])/g, function ($1) {\n    $1 = $1.trim().toLowerCase().replace('-', '');\n    return ($1[0] === '_' ? '' : '_') + $1;\n  }).slice(1);\n};", "map": {"version": 3, "names": ["module", "exports", "strToSnakeCase", "str", "Error", "trim", "replace", "$1", "toLowerCase", "slice"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/str-to-snake-case.js"], "sourcesContent": ["'use strict';\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToSnakeCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str.trim().replace(/(\\s*\\-*\\b\\w|[A-Z])/g, function($1) {\n    $1 = $1.trim().toLowerCase().replace('-', '');\n    return ($1[0] === '_' ? '' : '_') + $1;\n  }).slice(1);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,GAAG,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,qBAAqB,EAAE,UAASC,EAAE,EAAE;IAC5DA,EAAE,GAAGA,EAAE,CAACF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC7C,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,IAAIA,EAAE;EACxC,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}