{"ast": null, "code": "import emailjs from '@emailjs/browser';\n\n// Initialize EmailJS with your public key\n// Get these from your EmailJS dashboard\nconst EMAILJS_SERVICE_ID = 'YOUR_EMAILJS_SERVICE_ID';\nconst EMAILJS_TEMPLATE_VERIFY = 'YOUR_VERIFICATION_TEMPLATE_ID';\nconst EMAILJS_TEMPLATE_WELCOME = 'YOUR_WELCOME_TEMPLATE_ID';\nconst EMAILJS_PUBLIC_KEY = 'YOUR_EMAILJS_PUBLIC_KEY';\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  try {\n    const templateParams = {\n      to_email: email,\n      to_name: displayName || 'Trader',\n      verification_link: verificationLink,\n      app_name: 'Blaze<PERSON>rade'\n    };\n    await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_VERIFY, templateParams, EMAILJS_PUBLIC_KEY);\n    console.log('Verification email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  try {\n    const templateParams = {\n      to_email: email,\n      to_name: displayName || 'Trader',\n      login_time: new Date().toLocaleString(),\n      app_name: 'BlazeTrade'\n    };\n    await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_WELCOME, templateParams, EMAILJS_PUBLIC_KEY);\n    console.log('Welcome email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["emailjs", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_VERIFY", "EMAILJS_TEMPLATE_WELCOME", "EMAILJS_PUBLIC_KEY", "sendVerificationEmail", "email", "displayName", "verificationLink", "templateParams", "to_email", "to_name", "verification_link", "app_name", "send", "console", "log", "success", "error", "sendWelcomeEmail", "login_time", "Date", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/utils/emailService.js"], "sourcesContent": ["import emailjs from '@emailjs/browser';\n\n// Initialize EmailJS with your public key\n// Get these from your EmailJS dashboard\nconst EMAILJS_SERVICE_ID = 'YOUR_EMAILJS_SERVICE_ID';\nconst EMAILJS_TEMPLATE_VERIFY = 'YOUR_VERIFICATION_TEMPLATE_ID';\nconst EMAILJS_TEMPLATE_WELCOME = 'YOUR_WELCOME_TEMPLATE_ID';\nconst EMAILJS_PUBLIC_KEY = 'YOUR_EMAILJS_PUBLIC_KEY';\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  try {\n    const templateParams = {\n      to_email: email,\n      to_name: displayName || 'Trader',\n      verification_link: verificationLink,\n      app_name: 'Blaze<PERSON>rade'\n    };\n\n    await emailjs.send(\n      EMAILJS_SERVICE_ID,\n      EMAILJS_TEMPLATE_VERIFY,\n      templateParams,\n      EMAILJS_PUBLIC_KEY\n    );\n\n    console.log('Verification email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  try {\n    const templateParams = {\n      to_email: email,\n      to_name: displayName || 'Trader',\n      login_time: new Date().toLocaleString(),\n      app_name: 'BlazeTrade'\n    };\n\n    await emailjs.send(\n      EMAILJS_SERVICE_ID,\n      EMAILJS_TEMPLATE_WELCOME,\n      templateParams,\n      EMAILJS_PUBLIC_KEY\n    );\n\n    console.log('Welcome email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;;AAEtC;AACA;AACA,MAAMC,kBAAkB,GAAG,yBAAyB;AACpD,MAAMC,uBAAuB,GAAG,+BAA+B;AAC/D,MAAMC,wBAAwB,GAAG,0BAA0B;AAC3D,MAAMC,kBAAkB,GAAG,yBAAyB;;AAEpD;AACA,OAAO,MAAMC,qBAAqB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,WAAW,EAAEC,gBAAgB,KAAK;EACnF,IAAI;IACF,MAAMC,cAAc,GAAG;MACrBC,QAAQ,EAAEJ,KAAK;MACfK,OAAO,EAAEJ,WAAW,IAAI,QAAQ;MAChCK,iBAAiB,EAAEJ,gBAAgB;MACnCK,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMb,OAAO,CAACc,IAAI,CAChBb,kBAAkB,EAClBC,uBAAuB,EACvBO,cAAc,EACdL,kBACF,CAAC;IAEDW,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEV,KAAK,CAAC;IAChD,OAAO;MAAEW,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOb,KAAK,EAAEC,WAAW,KAAK;EAC5D,IAAI;IACF,MAAME,cAAc,GAAG;MACrBC,QAAQ,EAAEJ,KAAK;MACfK,OAAO,EAAEJ,WAAW,IAAI,QAAQ;MAChCa,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACvCT,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMb,OAAO,CAACc,IAAI,CAChBb,kBAAkB,EAClBE,wBAAwB,EACxBM,cAAc,EACdL,kBACF,CAAC;IAEDW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEV,KAAK,CAAC;IAC3C,OAAO;MAAEW,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}