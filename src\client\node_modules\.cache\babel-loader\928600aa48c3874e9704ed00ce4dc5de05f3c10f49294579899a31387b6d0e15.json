{"ast": null, "code": "'use strict';\n\n/**\n * Merge data helper\n */\nmodule.exports = function mergeData(base, data) {\n  //Validate data\n  if (typeof base !== 'object' || base === null) {\n    throw new Error('Not an object provided for base');\n  }\n  if (typeof data !== 'object' || data === null) {\n    throw new Error('Not an object provided for data');\n  }\n\n  //Copy base\n  const merged = Object.assign({}, base);\n\n  //Add data\n  for (const key in data) {\n    //istanbul ignore else\n    if (data.hasOwnProperty(key)) {\n      if (data[key] && Array.isArray(data[key])) {\n        merged[key] = data[key];\n      } else if (data[key] && typeof data[key] === 'object') {\n        merged[key] = Object.assign({}, data[key]);\n      } else if (data[key]) {\n        merged[key] = data[key];\n      }\n    }\n  }\n\n  //Return\n  return merged;\n};", "map": {"version": 3, "names": ["module", "exports", "mergeData", "base", "data", "Error", "merged", "Object", "assign", "key", "hasOwnProperty", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/merge-data.js"], "sourcesContent": ["'use strict';\n\n/**\n * Merge data helper\n */\nmodule.exports = function mergeData(base, data) {\n\n  //Validate data\n  if (typeof base !== 'object' || base === null) {\n    throw new Error('Not an object provided for base');\n  }\n  if (typeof data !== 'object' || data === null) {\n    throw new Error('Not an object provided for data');\n  }\n\n  //Copy base\n  const merged = Object.assign({}, base);\n\n  //Add data\n  for (const key in data) {\n    //istanbul ignore else\n    if (data.hasOwnProperty(key)) {\n      if (data[key] && Array.isArray(data[key])) {\n        merged[key] = data[key];\n      } else if (data[key] && typeof data[key] === 'object') {\n        merged[key] = Object.assign({}, data[key]);\n      } else if (data[key]) {\n        merged[key] = data[key];\n      }\n    }\n  }\n\n  //Return\n  return merged;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAE9C;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC7C,MAAM,IAAIE,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC7C,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;;EAEA;EACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC;;EAEtC;EACA,KAAK,MAAMM,GAAG,IAAIL,IAAI,EAAE;IACtB;IACA,IAAIA,IAAI,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;MAC5B,IAAIL,IAAI,CAACK,GAAG,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACR,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;QACzCH,MAAM,CAACG,GAAG,CAAC,GAAGL,IAAI,CAACK,GAAG,CAAC;MACzB,CAAC,MAAM,IAAIL,IAAI,CAACK,GAAG,CAAC,IAAI,OAAOL,IAAI,CAACK,GAAG,CAAC,KAAK,QAAQ,EAAE;QACrDH,MAAM,CAACG,GAAG,CAAC,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAACK,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIL,IAAI,CAACK,GAAG,CAAC,EAAE;QACpBH,MAAM,CAACG,GAAG,CAAC,GAAGL,IAAI,CAACK,GAAG,CAAC;MACzB;IACF;EACF;;EAEA;EACA,OAAOH,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}