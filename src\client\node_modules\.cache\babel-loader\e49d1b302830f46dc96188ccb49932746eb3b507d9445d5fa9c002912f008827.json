{"ast": null, "code": "'use strict';\n\n/**\n * Helper to convert an object's keys\n */\nmodule.exports = function convertKeys(obj, converter, ignored) {\n  //Validate\n  if (typeof obj !== 'object' || obj === null) {\n    throw new Error('Non object passed to convertKeys: ' + obj);\n  }\n\n  //Ignore arrays\n  if (Array.isArray(obj)) {\n    return obj;\n  }\n\n  //Ensure array for ignored values\n  if (!Array.isArray(ignored)) {\n    ignored = [];\n  }\n\n  //Process all properties\n  for (const key in obj) {\n    //istanbul ignore else\n    if (obj.hasOwnProperty(key)) {\n      //Convert key to snake case\n      const converted = converter(key);\n\n      //Recursive for child objects, unless ignored\n      //The ignored check checks both variants of the key\n      if (typeof obj[key] === 'object' && obj[key] !== null) {\n        if (!ignored.includes(key) && !ignored.includes(converted)) {\n          obj[key] = convertKeys(obj[key], converter, ignored);\n        }\n      }\n\n      //Convert key to snake case and set if needed\n      if (converted !== key) {\n        obj[converted] = obj[key];\n        delete obj[key];\n      }\n    }\n  }\n\n  //Return object\n  return obj;\n};", "map": {"version": 3, "names": ["module", "exports", "convertKeys", "obj", "converter", "ignored", "Error", "Array", "isArray", "key", "hasOwnProperty", "converted", "includes"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/convert-keys.js"], "sourcesContent": ["'use strict';\n\n/**\n * Helper to convert an object's keys\n */\nmodule.exports = function convertKeys(obj, converter, ignored) {\n\n  //Validate\n  if (typeof obj !== 'object' || obj === null) {\n    throw new Error('Non object passed to convertKeys: ' + obj);\n  }\n\n  //Ignore arrays\n  if (Array.isArray(obj)) {\n    return obj;\n  }\n\n  //Ensure array for ignored values\n  if (!Array.isArray(ignored)) {\n    ignored = [];\n  }\n\n  //Process all properties\n  for (const key in obj) {\n    //istanbul ignore else\n    if (obj.hasOwnProperty(key)) {\n\n      //Convert key to snake case\n      const converted = converter(key);\n\n      //Recursive for child objects, unless ignored\n      //The ignored check checks both variants of the key\n      if (typeof obj[key] === 'object' && obj[key] !== null) {\n        if (!ignored.includes(key) && !ignored.includes(converted)) {\n          obj[key] = convertKeys(obj[key], converter, ignored);\n        }\n      }\n\n      //Convert key to snake case and set if needed\n      if (converted !== key) {\n        obj[converted] = obj[key];\n        delete obj[key];\n      }\n    }\n  }\n\n  //Return object\n  return obj;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAE7D;EACA,IAAI,OAAOF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAC3C,MAAM,IAAIG,KAAK,CAAC,oCAAoC,GAAGH,GAAG,CAAC;EAC7D;;EAEA;EACA,IAAII,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;IACtB,OAAOA,GAAG;EACZ;;EAEA;EACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;IAC3BA,OAAO,GAAG,EAAE;EACd;;EAEA;EACA,KAAK,MAAMI,GAAG,IAAIN,GAAG,EAAE;IACrB;IACA,IAAIA,GAAG,CAACO,cAAc,CAACD,GAAG,CAAC,EAAE;MAE3B;MACA,MAAME,SAAS,GAAGP,SAAS,CAACK,GAAG,CAAC;;MAEhC;MACA;MACA,IAAI,OAAON,GAAG,CAACM,GAAG,CAAC,KAAK,QAAQ,IAAIN,GAAG,CAACM,GAAG,CAAC,KAAK,IAAI,EAAE;QACrD,IAAI,CAACJ,OAAO,CAACO,QAAQ,CAACH,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACO,QAAQ,CAACD,SAAS,CAAC,EAAE;UAC1DR,GAAG,CAACM,GAAG,CAAC,GAAGP,WAAW,CAACC,GAAG,CAACM,GAAG,CAAC,EAAEL,SAAS,EAAEC,OAAO,CAAC;QACtD;MACF;;MAEA;MACA,IAAIM,SAAS,KAAKF,GAAG,EAAE;QACrBN,GAAG,CAACQ,SAAS,CAAC,GAAGR,GAAG,CAACM,GAAG,CAAC;QACzB,OAAON,GAAG,CAACM,GAAG,CAAC;MACjB;IACF;EACF;;EAEA;EACA,OAAON,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}