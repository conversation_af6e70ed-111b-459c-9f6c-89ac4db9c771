{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const onSubmit = async data => {\n    try {\n      const response = await axios.post('/api/auth/login', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Login failed:', error);\n    }\n  };\n  const renderForm = () => {\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-400\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          ...register('username', {\n            required: 'Username is required'\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Enter your username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.username.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-400\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/forgot-password\",\n            className: \"text-sm text-blue-400 hover:text-blue-300\",\n            children: \"Forgot Password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...register('password', {\n            required: 'Password is required'\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Enter password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",\n          children: \"Log In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Log In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), serverError && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",\n            children: serverError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 29\n          }, this), renderForm(), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-sm text-center text-gray-400\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"font-medium text-blue-400 hover:text-blue-300\",\n              children: \"Sign up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold mb-4\",\n            children: \"Log in with QR code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://i.imgur.com/O12wJvA.png\",\n            alt: \"QR Code\",\n            className: \"w-48 h-48 rounded-lg bg-white p-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-center text-gray-400\",\n            children: [\"Scan via \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold text-white\",\n              children: \"BlazeTrade App\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 70\n            }, this), \" to log in\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"3PkcchUfmNng38CuCoE6nQQEuEk=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "serverError", "setServerError", "onSubmit", "data", "response", "post", "localStorage", "setItem", "token", "error", "msg", "console", "renderForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "placeholder", "username", "message", "to", "password", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n\n  const onSubmit = async (data) => {\n    try {\n      const response = await axios.post('/api/auth/login', data);\n      localStorage.setItem('token', response.data.token);\n      navigate('/dashboard');\n    } catch (error) {\n      if (error.response && error.response.data.msg) {\n        setServerError(error.response.data.msg);\n      } else {\n        setServerError('An unexpected error occurred. Please try again.');\n      }\n      console.error('Login failed:', error);\n    }\n  };\n\n  const renderForm = () => {\n    return (\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Username</label>\n          <input\n            type=\"text\"\n            {...register('username', { required: 'Username is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter your username\"\n          />\n          {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n        </div>\n        <div>\n          <div className=\"flex justify-between items-center\">\n            <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n            <Link to=\"/forgot-password\" className=\"text-sm text-blue-400 hover:text-blue-300\">Forgot Password?</Link>\n          </div>\n          <input\n            type=\"password\"\n            {...register('password', { required: 'Password is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter password\"\n          />\n          {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n        </div>\n        <div>\n          <button\n            type=\"submit\"\n            className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n          >\n            Log In\n          </button>\n        </div>\n      </form>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Log In</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            {renderForm()}\n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Don't have an account?{' '}\n              <Link to=\"/signup\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Sign up\n              </Link>\n            </p>\n          </div>\n\n          {/* QR Code Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center bg-blue-900/50 p-12 rounded-2xl border border-blue-800\">\n              <h2 className=\"text-2xl font-bold mb-4\">Log in with QR code</h2>\n              <img src=\"https://i.imgur.com/O12wJvA.png\" alt=\"QR Code\" className=\"w-48 h-48 rounded-lg bg-white p-2\"/>\n              <p className=\"mt-4 text-center text-gray-400\">Scan via <span className=\"font-bold text-white\">BlazeTrade App</span> to log in</p>\n          </div>\n        </div>\n\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGX,OAAO,CAAC,CAAC;EACnE,MAAMY,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMiB,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,IAAI,CAAC,iBAAiB,EAAEF,IAAI,CAAC;MAC1DG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACD,IAAI,CAACK,KAAK,CAAC;MAClDT,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACd,IAAIA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,EAAE;QAC7CT,cAAc,CAACQ,KAAK,CAACL,QAAQ,CAACD,IAAI,CAACO,GAAG,CAAC;MACzC,CAAC,MAAM;QACLT,cAAc,CAAC,iDAAiD,CAAC;MACnE;MACAU,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,oBACEpB,OAAA;MAAMU,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;MAACW,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3DtB,OAAA;QAAAsB,QAAA,gBACEtB,OAAA;UAAOqB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3E1B,OAAA;UACE2B,IAAI,EAAC,MAAM;UAAA,GACPxB,QAAQ,CAAC,UAAU,EAAE;YAAEyB,QAAQ,EAAE;UAAuB,CAAC,CAAC;UAC9DP,SAAS,EAAC,oIAAoI;UAC9IQ,WAAW,EAAC;QAAqB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDpB,MAAM,CAACwB,QAAQ,iBAAI9B,OAAA;UAAGqB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhB,MAAM,CAACwB,QAAQ,CAACC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACN1B,OAAA;QAAAsB,QAAA,gBACEtB,OAAA;UAAKqB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtB,OAAA;YAAOqB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3E1B,OAAA,CAACH,IAAI;YAACmC,EAAE,EAAC,kBAAkB;YAACX,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC,eACN1B,OAAA;UACE2B,IAAI,EAAC,UAAU;UAAA,GACXxB,QAAQ,CAAC,UAAU,EAAE;YAAEyB,QAAQ,EAAE;UAAuB,CAAC,CAAC;UAC9DP,SAAS,EAAC,oIAAoI;UAC9IQ,WAAW,EAAC;QAAgB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACDpB,MAAM,CAAC2B,QAAQ,iBAAIjC,OAAA;UAAGqB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhB,MAAM,CAAC2B,QAAQ,CAACF;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACN1B,OAAA;QAAAsB,QAAA,eACEtB,OAAA;UACE2B,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,2MAA2M;UAAAC,QAAA,EACtN;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;EAED,oBACE1B,OAAA;IAAKqB,SAAS,EAAC,mDAAmD;IAAAC,QAAA,gBAChEtB,OAAA,CAACN,MAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV1B,OAAA;MAAKqB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DtB,OAAA;QAAKqB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAG/EtB,OAAA;UAAKqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BtB,OAAA;YAAIqB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClDlB,WAAW,iBAAIR,OAAA;YAAGqB,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAAEd;UAAW;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClHN,UAAU,CAAC,CAAC,eACbpB,OAAA;YAAGqB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,wBAC9B,EAAC,GAAG,eAC1BtB,OAAA,CAACH,IAAI;cAACmC,EAAE,EAAC,SAAS;cAACX,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN1B,OAAA;UAAKqB,SAAS,EAAC,4GAA4G;UAAAC,QAAA,gBACvHtB,OAAA;YAAIqB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE1B,OAAA;YAAKkC,GAAG,EAAC,iCAAiC;YAACC,GAAG,EAAC,SAAS;YAACd,SAAS,EAAC;UAAmC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACxG1B,OAAA;YAAGqB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,GAAC,WAAS,eAAAtB,OAAA;cAAMqB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,cAAU;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAxFID,KAAK;EAAA,QACiDN,OAAO,EAChDG,WAAW;AAAA;AAAAsC,EAAA,GAFxBnC,KAAK;AA0FX,eAAeA,KAAK;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}