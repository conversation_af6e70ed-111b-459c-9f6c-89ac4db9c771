{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\components\\\\AuthFooter.js\";\nimport React from 'react';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthFooter = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom flex flex-col items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: blazeTradeLogo,\n        alt: \"BlazeTrade Logo\",\n        className: \"h-8 object-contain mb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"\\xA9 \", new Date().getFullYear(), \" BlazeTrade. All rights reserved.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AuthFooter;\nexport default AuthFooter;\nvar _c;\n$RefreshReg$(_c, \"AuthFooter\");", "map": {"version": 3, "names": ["React", "blazeTradeLogo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/components/AuthFooter.js"], "sourcesContent": ["import React from 'react';\nimport blazeTradeLogo from '../assets/blazetrade-logo.png';\n\nconst AuthFooter = () => {\n  return (\n    <footer className=\"bg-gray-900 py-4\">\n      <div className=\"container-custom flex flex-col items-center justify-center\">\n        <img src={blazeTradeLogo} alt=\"BlazeTrade Logo\" className=\"h-8 object-contain mb-2\" />\n        <p className=\"text-sm text-gray-500\">&copy; {new Date().getFullYear()} BlazeTrade. All rights reserved.</p>\n      </div>\n    </footer>\n  );\n};\n\nexport default AuthFooter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAAQE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAClCH,OAAA;MAAKE,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzEH,OAAA;QAAKI,GAAG,EAAEN,cAAe;QAACO,GAAG,EAAC,iBAAiB;QAACH,SAAS,EAAC;MAAyB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtFT,OAAA;QAAGE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,OAAO,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,mCAAiC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACG,EAAA,GATIX,UAAU;AAWhB,eAAeA,UAAU;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}