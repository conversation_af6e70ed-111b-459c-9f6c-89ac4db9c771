{"ast": null, "code": "import sgMail from '@sendgrid/mail';\n\n// Initialize SendGrid with your API key\nsgMail.setApiKey(process.env.REACT_APP_SENDGRID_API_KEY);\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-c3eb32a8c66b4d47beff2b9c9513af62',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      verificationLink: verificationLink,\n      appName: 'BlazeTrade'\n    }\n  };\n  try {\n    await sgMail.send(msg);\n    console.log('Verification email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-8c1e56e48c05424faf4a25dd9cf637b2',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      loginTime: new Date().toLocaleString(),\n      appName: 'BlazeTrade'\n    }\n  };\n  try {\n    await sgMail.send(msg);\n    console.log('Welcome email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["sgMail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_SENDGRID_API_KEY", "sendVerificationEmail", "email", "displayName", "verificationLink", "msg", "to", "from", "templateId", "dynamicTemplateData", "name", "appName", "send", "console", "log", "success", "error", "sendWelcomeEmail", "loginTime", "Date", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/utils/emailService.js"], "sourcesContent": ["import sgMail from '@sendgrid/mail';\n\n// Initialize SendGrid with your API key\nsgMail.setApiKey(process.env.REACT_APP_SENDGRID_API_KEY);\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-c3eb32a8c66b4d47beff2b9c9513af62',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      verificationLink: verificationLink,\n      appName: 'BlazeTrade'\n    },\n  };\n\n  try {\n    await sgMail.send(msg);\n    console.log('Verification email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  const msg = {\n    to: email,\n    from: '<EMAIL>',\n    templateId: 'd-8c1e56e48c05424faf4a25dd9cf637b2',\n    dynamicTemplateData: {\n      name: displayName || 'Trader',\n      loginTime: new Date().toLocaleString(),\n      appName: 'BlazeTrade'\n    },\n  };\n\n  try {\n    await sgMail.send(msg);\n    console.log('Welcome email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,gBAAgB;;AAEnC;AACAA,MAAM,CAACC,SAAS,CAACC,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC;;AAExD;AACA,OAAO,MAAMC,qBAAqB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,WAAW,EAAEC,gBAAgB,KAAK;EACnF,MAAMC,GAAG,GAAG;IACVC,EAAE,EAAEJ,KAAK;IACTK,IAAI,EAAE,8BAA8B;IACpCC,UAAU,EAAE,oCAAoC;IAChDC,mBAAmB,EAAE;MACnBC,IAAI,EAAEP,WAAW,IAAI,QAAQ;MAC7BC,gBAAgB,EAAEA,gBAAgB;MAClCO,OAAO,EAAE;IACX;EACF,CAAC;EAED,IAAI;IACF,MAAMf,MAAM,CAACgB,IAAI,CAACP,GAAG,CAAC;IACtBQ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEZ,KAAK,CAAC;IAChD,OAAO;MAAEa,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOf,KAAK,EAAEC,WAAW,KAAK;EAC5D,MAAME,GAAG,GAAG;IACVC,EAAE,EAAEJ,KAAK;IACTK,IAAI,EAAE,8BAA8B;IACpCC,UAAU,EAAE,oCAAoC;IAChDC,mBAAmB,EAAE;MACnBC,IAAI,EAAEP,WAAW,IAAI,QAAQ;MAC7Be,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MACtCT,OAAO,EAAE;IACX;EACF,CAAC;EAED,IAAI;IACF,MAAMf,MAAM,CAACgB,IAAI,CAACP,GAAG,CAAC;IACtBQ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEZ,KAAK,CAAC;IAC3C,OAAO;MAAEa,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}