{"ast": null, "code": "'use strict';\n\nclass Response {\n  constructor(statusCode, body, headers) {\n    this.statusCode = statusCode;\n    this.body = body;\n    this.headers = headers;\n  }\n  toString() {\n    return 'HTTP ' + this.statusCode + ' ' + this.body;\n  }\n}\nmodule.exports = Response;", "map": {"version": 3, "names": ["Response", "constructor", "statusCode", "body", "headers", "toString", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/classes/response.js"], "sourcesContent": ["'use strict';\n\nclass Response {\n  constructor(statusCode, body, headers) {\n    this.statusCode = statusCode;\n    this.body = body;\n    this.headers = headers;\n  }\n\n  toString() {\n    return 'HTTP ' + this.statusCode + ' ' + this.body;\n  }\n}\n\nmodule.exports = Response;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,QAAQ,CAAC;EACbC,WAAWA,CAACC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACrC,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO,OAAO,GAAG,IAAI,CAACH,UAAU,GAAG,GAAG,GAAG,IAAI,CAACC,IAAI;EACpD;AACF;AAEAG,MAAM,CAACC,OAAO,GAAGP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}