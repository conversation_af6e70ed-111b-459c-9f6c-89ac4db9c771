{"name": "char-regex", "version": "2.0.2", "description": "A regex to match any full character, considering weird character ranges.", "repository": "Richienb/char-regex", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "type": "module", "exports": "./index.js", "files": ["index.js", "index.d.ts"], "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["character", "regex", "match", "split", "length"], "dependencies": {}, "devDependencies": {"all-chars": "^1.0.0", "ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.59.3"}}