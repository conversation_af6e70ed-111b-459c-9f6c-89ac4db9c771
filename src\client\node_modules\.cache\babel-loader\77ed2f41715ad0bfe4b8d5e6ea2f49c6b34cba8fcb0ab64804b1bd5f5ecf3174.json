{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Services from './pages/Services';\nimport Contact from './pages/Contact';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\nimport LandingPage from './pages/LandingPage';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport AuthFooter from './components/AuthFooter';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children\n}) => {\n  const token = localStorage.getItem('token');\n  return token ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 29\n  }, this);\n};\n_c = PrivateRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  const token = localStorage.getItem('token');\n  return token ? /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 20\n  }, this) : children;\n};\n_c2 = PublicRoute;\nconst Layout = () => {\n  _s();\n  const location = useLocation();\n  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col min-h-screen bg-gray-900\",\n    children: [!isAuthPage && /*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n      className: \"flex-grow\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n            children: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/services\",\n          element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/contact\",\n          element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/signup\",\n          element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), !isAuthPage && /*#__PURE__*/_jsxDEV(Chatbot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 23\n    }, this), !isAuthPage ? /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 22\n    }, this) : /*#__PURE__*/_jsxDEV(AuthFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 35\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c3 = Layout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"PrivateRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"Layout\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "motion", "Home", "About", "Services", "Contact", "Signup", "<PERSON><PERSON>", "LandingPage", "<PERSON><PERSON><PERSON>", "Footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ScrollToTop", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "token", "localStorage", "getItem", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PublicRoute", "_c2", "Layout", "_s", "location", "isAuthPage", "pathname", "className", "main", "initial", "opacity", "animate", "transition", "duration", "path", "element", "_c3", "App", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Services from './pages/Services';\nimport Contact from './pages/Contact';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\nimport LandingPage from './pages/LandingPage';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport AuthFooter from './components/AuthFooter';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\n\nconst PrivateRoute = ({ children }) => {\n  const token = localStorage.getItem('token');\n  return token ? children : <Navigate to=\"/\" />;\n};\n\nconst PublicRoute = ({ children }) => {\n    const token = localStorage.getItem('token');\n    return token ? <Navigate to=\"/dashboard\" /> : children;\n}\n\nconst Layout = () => {\n  const location = useLocation();\n  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';\n\n  return (\n    <div className=\"flex flex-col min-h-screen bg-gray-900\">\n      {!isAuthPage && <Navbar />}\n      <motion.main\n        className=\"flex-grow\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Routes>\n          <Route path=\"/\" element={<PublicRoute><LandingPage /></PublicRoute>} />\n          <Route path=\"/dashboard\" element={<PrivateRoute><Home /></PrivateRoute>} />\n          <Route path=\"/about\" element={<About />} />\n          <Route path=\"/services\" element={<Services />} />\n          <Route path=\"/contact\" element={<Contact />} />\n          <Route path=\"/signup\" element={<Signup />} />\n          <Route path=\"/login\" element={<Login />} />\n        </Routes>\n      </motion.main>\n      {!isAuthPage && <Chatbot />}\n      {!isAuthPage ? <Footer /> : <AuthFooter />}\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <ScrollToTop />\n      <Layout />\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAChG,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,WAAW,MAAM,qBAAqB;;AAE7C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,GAAGD,QAAQ,gBAAGF,OAAA,CAAChB,QAAQ;IAACsB,EAAE,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;AAACC,EAAA,GAHIV,YAAY;AAKlB,MAAMW,WAAW,GAAGA,CAAC;EAAEV;AAAS,CAAC,KAAK;EAClC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,gBAAGH,OAAA,CAAChB,QAAQ;IAACsB,EAAE,EAAC;EAAY;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,GAAGR,QAAQ;AAC1D,CAAC;AAAAW,GAAA,GAHKD,WAAW;AAKjB,MAAME,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,UAAU,GAAGD,QAAQ,CAACE,QAAQ,KAAK,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,KAAK,SAAS;EAEpF,oBACElB,OAAA;IAAKmB,SAAS,EAAC,wCAAwC;IAAAjB,QAAA,GACpD,CAACe,UAAU,iBAAIjB,OAAA,CAACN,MAAM;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1BV,OAAA,CAACd,MAAM,CAACkC,IAAI;MACVD,SAAS,EAAC,WAAW;MACrBE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAvB,QAAA,eAE9BF,OAAA,CAAClB,MAAM;QAAAoB,QAAA,gBACLF,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE3B,OAAA,CAACY,WAAW;YAAAV,QAAA,eAACF,OAAA,CAACP,WAAW;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,YAAY;UAACC,OAAO,eAAE3B,OAAA,CAACC,YAAY;YAAAC,QAAA,eAACF,OAAA,CAACb,IAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3EV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE3B,OAAA,CAACZ,KAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,WAAW;UAACC,OAAO,eAAE3B,OAAA,CAACX,QAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,UAAU;UAACC,OAAO,eAAE3B,OAAA,CAACV,OAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,SAAS;UAACC,OAAO,eAAE3B,OAAA,CAACT,MAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CV,OAAA,CAACjB,KAAK;UAAC2C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE3B,OAAA,CAACR,KAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACb,CAACO,UAAU,iBAAIjB,OAAA,CAACH,OAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1B,CAACO,UAAU,gBAAGjB,OAAA,CAACL,MAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGV,OAAA,CAACJ,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAACK,EAAA,CA3BID,MAAM;EAAA,QACO7B,WAAW;AAAA;AAAA2C,GAAA,GADxBd,MAAM;AA6BZ,SAASe,GAAGA,CAAA,EAAG;EACb,oBACE7B,OAAA,CAACnB,MAAM;IAAAqB,QAAA,gBACLF,OAAA,CAACF,WAAW;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfV,OAAA,CAACc,MAAM;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAACoB,GAAA,GAPQD,GAAG;AASZ,eAAeA,GAAG;AAAC,IAAAlB,EAAA,EAAAE,GAAA,EAAAe,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}