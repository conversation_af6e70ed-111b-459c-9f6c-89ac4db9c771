{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport { signInWithEmailAndPassword } from 'firebase/auth';\nimport { auth } from '../firebase';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { sendWelcomeEmail } from '../utils/emailService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n  const onSubmit = async data => {\n    setServerError(''); // Clear previous errors\n    try {\n      // Sign in with email and password using Firebase\n      const userCredential = await signInWithEmailAndPassword(auth, data.username, data.password);\n      const user = userCredential.user;\n\n      // Check if email is verified\n      if (!user.emailVerified) {\n        // Sign out the user\n        await auth.signOut();\n\n        // Send verification email again\n        await sendEmailVerification(user, {\n          url: `${window.location.origin}/login`\n        });\n\n        // Navigate to check-email page\n        navigate('/check-email');\n        return;\n      }\n\n      // Store the user's ID token in localStorage for session management\n      const token = await user.getIdToken();\n      localStorage.setItem('token', token);\n\n      // Send welcome email\n      try {\n        await sendWelcomeEmail(user.email, user.displayName || 'Trader');\n      } catch (emailError) {\n        console.error('Error sending welcome email:', emailError);\n        // Don't block login if email fails\n      }\n\n      // Navigate to the dashboard on successful login\n      navigate('/dashboard');\n    } catch (error) {\n      let friendlyMessage = 'An unexpected error occurred. Please try again.';\n\n      // Provide user-friendly error messages\n      switch (error.code) {\n        case 'auth/user-not-found':\n        case 'auth/wrong-password':\n          friendlyMessage = 'Incorrect username or password. Please try again.';\n          break;\n        case 'auth/too-many-requests':\n          friendlyMessage = 'Too many failed login attempts. Please try again later or reset your password.';\n          break;\n        case 'auth/user-disabled':\n          friendlyMessage = 'This account has been disabled. Please contact support.';\n          break;\n        default:\n          console.error('Firebase Login Error:', error);\n      }\n      setServerError(friendlyMessage);\n    }\n  };\n  const renderForm = () => {\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-400\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          ...register('username', {\n            required: 'Email is required',\n            pattern: {\n              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n              message: 'Please enter a valid email address'\n            }\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.username.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-400\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/forgot-password\",\n            className: \"text-sm text-blue-400 hover:text-blue-300 hover:underline\",\n            children: \"Forgot Password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          ...register('password', {\n            required: 'Password is required'\n          }),\n          className: \"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",\n          placeholder: \"Enter password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-red-500\",\n          children: errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",\n          children: \"Log In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Log In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), serverError && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",\n            children: serverError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 29\n          }, this), renderForm(), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-sm text-center text-gray-400\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"font-medium text-blue-400 hover:text-blue-300\",\n              children: \"Sign up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex flex-col items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/blazetrade-logo.png\",\n            alt: \"BlazeTrade Logo\",\n            className: \"w-48 h-48\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"3PkcchUfmNng38CuCoE6nQQEuEk=\", false, function () {\n  return [useForm, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "signInWithEmailAndPassword", "auth", "Link", "useNavigate", "sendWelcomeEmail", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "register", "handleSubmit", "formState", "errors", "navigate", "serverError", "setServerError", "onSubmit", "data", "userCredential", "username", "password", "user", "emailVerified", "signOut", "sendEmailVerification", "url", "window", "location", "origin", "token", "getIdToken", "localStorage", "setItem", "email", "displayName", "emailError", "console", "error", "friendlyMessage", "code", "renderForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "pattern", "value", "message", "placeholder", "to", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport { signInWithEmailAndPassword } from 'firebase/auth';\nimport { auth } from '../firebase';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { sendWelcomeEmail } from '../utils/emailService';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n\n  const onSubmit = async (data) => {\n    setServerError(''); // Clear previous errors\n    try {\n      // Sign in with email and password using Firebase\n      const userCredential = await signInWithEmailAndPassword(auth, data.username, data.password);\n      const user = userCredential.user;\n      \n      // Check if email is verified\n      if (!user.emailVerified) {\n        // Sign out the user\n        await auth.signOut();\n        \n        // Send verification email again\n        await sendEmailVerification(user, {\n          url: `${window.location.origin}/login`\n        });\n        \n        // Navigate to check-email page\n        navigate('/check-email');\n        return;\n      }\n      \n      // Store the user's ID token in localStorage for session management\n      const token = await user.getIdToken();\n      localStorage.setItem('token', token);\n      \n      // Send welcome email\n      try {\n        await sendWelcomeEmail(user.email, user.displayName || 'Trader');\n      } catch (emailError) {\n        console.error('Error sending welcome email:', emailError);\n        // Don't block login if email fails\n      }\n      \n      // Navigate to the dashboard on successful login\n      navigate('/dashboard');\n    } catch (error) {\n      let friendlyMessage = 'An unexpected error occurred. Please try again.';\n      \n      // Provide user-friendly error messages\n      switch (error.code) {\n        case 'auth/user-not-found':\n        case 'auth/wrong-password':\n          friendlyMessage = 'Incorrect username or password. Please try again.';\n          break;\n        case 'auth/too-many-requests':\n          friendlyMessage = 'Too many failed login attempts. Please try again later or reset your password.';\n          break;\n        case 'auth/user-disabled':\n          friendlyMessage = 'This account has been disabled. Please contact support.';\n          break;\n        default:\n          console.error('Firebase Login Error:', error);\n      }\n      \n      setServerError(friendlyMessage);\n    }\n  };\n\n  const renderForm = () => {\n    return (\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Email</label>\n          <input\n            type=\"email\"\n            {...register('username', { \n              required: 'Email is required',\n              pattern: {\n                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                message: 'Please enter a valid email address'\n              }\n            })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter your email\"\n          />\n          {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n        </div>\n        <div>\n          <div className=\"flex justify-between items-center\">\n            <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n            <Link to=\"/forgot-password\" className=\"text-sm text-blue-400 hover:text-blue-300 hover:underline\">\n              Forgot Password?\n            </Link>\n          </div>\n          <input\n            type=\"password\"\n            {...register('password', { required: 'Password is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter password\"\n          />\n          {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n        </div>\n        <div>\n          <button\n            type=\"submit\"\n            className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n          >\n            Log In\n          </button>\n        </div>\n      </form>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Log In</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            {renderForm()}\n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Don't have an account?{' '}\n              <Link to=\"/signup\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Sign up\n              </Link>\n            </p>\n          </div>\n\n          {/* Logo Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center\">\n              <img src=\"/blazetrade-logo.png\" alt=\"BlazeTrade Logo\" className=\"w-48 h-48\"/>\n          </div>\n        </div>\n\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,0BAA0B,QAAQ,eAAe;AAC1D,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGb,OAAO,CAAC,CAAC;EACnE,MAAMc,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMmB,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/BF,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,IAAI;MACF;MACA,MAAMG,cAAc,GAAG,MAAMlB,0BAA0B,CAACC,IAAI,EAAEgB,IAAI,CAACE,QAAQ,EAAEF,IAAI,CAACG,QAAQ,CAAC;MAC3F,MAAMC,IAAI,GAAGH,cAAc,CAACG,IAAI;;MAEhC;MACA,IAAI,CAACA,IAAI,CAACC,aAAa,EAAE;QACvB;QACA,MAAMrB,IAAI,CAACsB,OAAO,CAAC,CAAC;;QAEpB;QACA,MAAMC,qBAAqB,CAACH,IAAI,EAAE;UAChCI,GAAG,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM;QAChC,CAAC,CAAC;;QAEF;QACAf,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;;MAEA;MACA,MAAMgB,KAAK,GAAG,MAAMR,IAAI,CAACS,UAAU,CAAC,CAAC;MACrCC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,KAAK,CAAC;;MAEpC;MACA,IAAI;QACF,MAAMzB,gBAAgB,CAACiB,IAAI,CAACY,KAAK,EAAEZ,IAAI,CAACa,WAAW,IAAI,QAAQ,CAAC;MAClE,CAAC,CAAC,OAAOC,UAAU,EAAE;QACnBC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,UAAU,CAAC;QACzD;MACF;;MAEA;MACAtB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAIC,eAAe,GAAG,iDAAiD;;MAEvE;MACA,QAAQD,KAAK,CAACE,IAAI;QAChB,KAAK,qBAAqB;QAC1B,KAAK,qBAAqB;UACxBD,eAAe,GAAG,mDAAmD;UACrE;QACF,KAAK,wBAAwB;UAC3BA,eAAe,GAAG,gFAAgF;UAClG;QACF,KAAK,oBAAoB;UACvBA,eAAe,GAAG,yDAAyD;UAC3E;QACF;UACEF,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD;MAEAtB,cAAc,CAACuB,eAAe,CAAC;IACjC;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,oBACElC,OAAA;MAAMU,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;MAACyB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3DpC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxExC,OAAA;UACEyC,IAAI,EAAC,OAAO;UAAA,GACRtC,QAAQ,CAAC,UAAU,EAAE;YACvBuC,QAAQ,EAAE,mBAAmB;YAC7BC,OAAO,EAAE;cACPC,KAAK,EAAE,0CAA0C;cACjDC,OAAO,EAAE;YACX;UACF,CAAC,CAAC;UACFV,SAAS,EAAC,oIAAoI;UAC9IW,WAAW,EAAC;QAAkB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDlC,MAAM,CAACO,QAAQ,iBAAIb,OAAA;UAAGmC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAE9B,MAAM,CAACO,QAAQ,CAACgC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACNxC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAKmC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpC,OAAA;YAAOmC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3ExC,OAAA,CAACJ,IAAI;YAACmD,EAAE,EAAC,kBAAkB;YAACZ,SAAS,EAAC,2DAA2D;YAAAC,QAAA,EAAC;UAElG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNxC,OAAA;UACEyC,IAAI,EAAC,UAAU;UAAA,GACXtC,QAAQ,CAAC,UAAU,EAAE;YAAEuC,QAAQ,EAAE;UAAuB,CAAC,CAAC;UAC9DP,SAAS,EAAC,oIAAoI;UAC9IW,WAAW,EAAC;QAAgB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACDlC,MAAM,CAACQ,QAAQ,iBAAId,OAAA;UAAGmC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAE9B,MAAM,CAACQ,QAAQ,CAAC+B;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACNxC,OAAA;QAAAoC,QAAA,eACEpC,OAAA;UACEyC,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,2MAA2M;UAAAC,QAAA,EACtN;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;EAED,oBACExC,OAAA;IAAKmC,SAAS,EAAC,mDAAmD;IAAAC,QAAA,gBAChEpC,OAAA,CAACR,MAAM;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVxC,OAAA;MAAKmC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DpC,OAAA;QAAKmC,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAG/EpC,OAAA;UAAKmC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpC,OAAA;YAAImC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClDhC,WAAW,iBAAIR,OAAA;YAAGmC,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAAE5B;UAAW;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClHN,UAAU,CAAC,CAAC,eACblC,OAAA;YAAGmC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,wBAC9B,EAAC,GAAG,eAC1BpC,OAAA,CAACJ,IAAI;cAACmD,EAAE,EAAC,SAAS;cAACZ,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNxC,OAAA;UAAKmC,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAChEpC,OAAA;YAAKgD,GAAG,EAAC,sBAAsB;YAACC,GAAG,EAAC,iBAAiB;YAACd,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA1IID,KAAK;EAAA,QACiDR,OAAO,EAChDI,WAAW;AAAA;AAAAqD,EAAA,GAFxBjD,KAAK;AA4IX,eAAeA,KAAK;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}