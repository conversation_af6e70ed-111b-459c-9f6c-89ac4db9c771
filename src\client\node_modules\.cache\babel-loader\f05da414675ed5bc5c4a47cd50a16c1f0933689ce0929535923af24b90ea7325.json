{"ast": null, "code": "'use strict';\n\nconst validate = (parent, parentName, childName, childType) => {\n  if (typeof parent === 'undefined' || typeof parent[childName] === 'undefined') {\n    return;\n  }\n  if (typeof parent[childName] !== childType) {\n    throw new Error(`${childType} expected for \\`${parentName}.${childName}\\``);\n  }\n};\nmodule.exports = {\n  validateMailSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `mailSettings`');\n    }\n    const {\n      bcc,\n      bypassListManagement,\n      bypassSpamManagement,\n      bypassBounceManagement,\n      bypassUnsubscribeManagement,\n      footer,\n      sandboxMode,\n      spamCheck\n    } = settings;\n    validate(bcc, 'bcc', 'enable', 'boolean');\n    validate(bcc, 'bcc', 'email', 'string');\n    validate(bypassListManagement, 'bypassListManagement', 'enable', 'boolean');\n    validate(bypassSpamManagement, 'bypassSpamManagement', 'enable', 'boolean');\n    validate(bypassBounceManagement, 'bypassBounceManagement', 'enable', 'boolean');\n    validate(bypassUnsubscribeManagement, 'bypassUnsubscribeManagement', 'enable', 'boolean');\n    validate(footer, 'footer', 'enable', 'boolean');\n    validate(footer, 'footer', 'text', 'string');\n    validate(footer, 'footer', 'html', 'string');\n    validate(sandboxMode, 'sandboxMode', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'threshold', 'number');\n    validate(spamCheck, 'spamCheck', 'postToUrl', 'string');\n  },\n  validateTrackingSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `trackingSettings`');\n    }\n    const {\n      clickTracking,\n      openTracking,\n      subscriptionTracking,\n      ganalytics\n    } = settings;\n    validate(clickTracking, 'clickTracking', 'enable', 'boolean');\n    validate(clickTracking, 'clickTracking', 'enableText', 'boolean');\n    validate(openTracking, 'openTracking', 'enable', 'boolean');\n    validate(openTracking, 'openTracking', 'substitutionTag', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'enable', 'boolean');\n    validate(subscriptionTracking, 'subscriptionTracking', 'text', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'html', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'substitutionTag', 'string');\n    validate(ganalytics, 'ganalytics', 'enable', 'boolean');\n    validate(ganalytics, 'ganalytics', 'utm_source', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_medium', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_term', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_content', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_campaign', 'string');\n  }\n};", "map": {"version": 3, "names": ["validate", "parent", "parentName", "<PERSON><PERSON><PERSON>", "childType", "Error", "module", "exports", "validateMailSettings", "settings", "bcc", "bypassListManagement", "bypassSpamManagement", "bypassBounceManagement", "bypassUnsubscribeManagement", "footer", "sandboxMode", "spamCheck", "validateTrackingSettings", "clickTracking", "openTracking", "subscriptionTracking", "ganalytics"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/helpers/helpers/validate-settings.js"], "sourcesContent": ["'use strict';\n\nconst validate = (parent, parentName, childName, childType) => {\n  if (typeof parent === 'undefined' || typeof parent[childName] === 'undefined') {\n    return;\n  }\n  if (typeof parent[childName] !== childType) {\n    throw new Error(`${childType} expected for \\`${parentName}.${childName}\\``)\n  }\n};\n\nmodule.exports = {\n  validateMailSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `mailSettings`');\n    }\n    const {\n      bcc,\n      bypassListManagement,\n      bypassSpamManagement,\n      bypassBounceManagement,\n      bypassUnsubscribeManagement,\n      footer,\n      sandboxMode,\n      spamCheck,\n    } = settings;\n    validate(bcc, 'bcc', 'enable', 'boolean');\n    validate(bcc, 'bcc', 'email', 'string');\n    validate(bypassListManagement, 'bypassListManagement', 'enable', 'boolean');\n    validate(bypassSpamManagement, 'bypassSpamManagement', 'enable', 'boolean');\n    validate(bypassBounceManagement, 'bypassBounceManagement', 'enable', 'boolean');\n    validate(bypassUnsubscribeManagement, 'bypassUnsubscribeManagement', 'enable', 'boolean');\n    validate(footer, 'footer', 'enable', 'boolean');\n    validate(footer, 'footer', 'text', 'string');\n    validate(footer, 'footer', 'html', 'string');\n    validate(sandboxMode, 'sandboxMode', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'threshold', 'number');\n    validate(spamCheck, 'spamCheck', 'postToUrl', 'string');\n  },\n\n  validateTrackingSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `trackingSettings`');\n    }\n    const {\n      clickTracking,\n      openTracking,\n      subscriptionTracking,\n      ganalytics,\n    } = settings;\n    validate(clickTracking, 'clickTracking', 'enable', 'boolean');\n    validate(clickTracking, 'clickTracking', 'enableText', 'boolean');\n    validate(openTracking, 'openTracking', 'enable', 'boolean');\n    validate(openTracking, 'openTracking', 'substitutionTag', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'enable', 'boolean');\n    validate(subscriptionTracking, 'subscriptionTracking', 'text', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'html', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'substitutionTag', 'string');\n    validate(ganalytics, 'ganalytics', 'enable', 'boolean');\n    validate(ganalytics, 'ganalytics', 'utm_source', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_medium', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_term', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_content', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_campaign', 'string');\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,QAAQ,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,KAAK;EAC7D,IAAI,OAAOH,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACE,SAAS,CAAC,KAAK,WAAW,EAAE;IAC7E;EACF;EACA,IAAI,OAAOF,MAAM,CAACE,SAAS,CAAC,KAAKC,SAAS,EAAE;IAC1C,MAAM,IAAIC,KAAK,CAAC,GAAGD,SAAS,mBAAmBF,UAAU,IAAIC,SAAS,IAAI,CAAC;EAC7E;AACF,CAAC;AAEDG,MAAM,CAACC,OAAO,GAAG;EACfC,oBAAoBA,CAACC,QAAQ,EAAE;IAC7B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,MAAM,IAAIJ,KAAK,CAAC,oCAAoC,CAAC;IACvD;IACA,MAAM;MACJK,GAAG;MACHC,oBAAoB;MACpBC,oBAAoB;MACpBC,sBAAsB;MACtBC,2BAA2B;MAC3BC,MAAM;MACNC,WAAW;MACXC;IACF,CAAC,GAAGR,QAAQ;IACZT,QAAQ,CAACU,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;IACzCV,QAAQ,CAACU,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACvCV,QAAQ,CAACW,oBAAoB,EAAE,sBAAsB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC3EX,QAAQ,CAACY,oBAAoB,EAAE,sBAAsB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC3EZ,QAAQ,CAACa,sBAAsB,EAAE,wBAAwB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC/Eb,QAAQ,CAACc,2BAA2B,EAAE,6BAA6B,EAAE,QAAQ,EAAE,SAAS,CAAC;IACzFd,QAAQ,CAACe,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC/Cf,QAAQ,CAACe,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;IAC5Cf,QAAQ,CAACe,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;IAC5Cf,QAAQ,CAACgB,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;IACzDhB,QAAQ,CAACiB,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;IACrDjB,QAAQ,CAACiB,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;IACvDjB,QAAQ,CAACiB,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;EACzD,CAAC;EAEDC,wBAAwBA,CAACT,QAAQ,EAAE;IACjC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,MAAM,IAAIJ,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IACA,MAAM;MACJc,aAAa;MACbC,YAAY;MACZC,oBAAoB;MACpBC;IACF,CAAC,GAAGb,QAAQ;IACZT,QAAQ,CAACmB,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC7DnB,QAAQ,CAACmB,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,CAAC;IACjEnB,QAAQ,CAACoB,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC3DpB,QAAQ,CAACoB,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,CAAC;IACnEpB,QAAQ,CAACqB,oBAAoB,EAAE,sBAAsB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC3ErB,QAAQ,CAACqB,oBAAoB,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,CAAC;IACxErB,QAAQ,CAACqB,oBAAoB,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,CAAC;IACxErB,QAAQ,CAACqB,oBAAoB,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,QAAQ,CAAC;IACnFrB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACvDtB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC;IAC1DtB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC;IAC1DtB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,CAAC;IACxDtB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC;IAC3DtB,QAAQ,CAACsB,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,CAAC;EAC9D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}