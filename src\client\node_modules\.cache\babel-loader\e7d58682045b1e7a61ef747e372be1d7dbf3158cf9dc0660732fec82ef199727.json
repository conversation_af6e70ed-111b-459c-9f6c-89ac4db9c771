{"ast": null, "code": "import React,{useEffect}from'react';import{motion,useAnimation}from'framer-motion';import{useInView}from'react-intersection-observer';import{FaBitcoin,FaChartLine,FaChartBar,FaShieldAlt,Fa<PERSON>ser<PERSON>ie,<PERSON>a<PERSON><PERSON><PERSON><PERSON>,<PERSON>aCheck}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Services=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"pt-16\",children:[/*#__PURE__*/_jsx(ServicesHero,{}),/*#__PURE__*/_jsx(ServicesList,{}),/*#__PURE__*/_jsx(FeaturedService,{}),/*#__PURE__*/_jsx(PricingSection,{}),/*#__PURE__*/_jsx(FAQSection,{})]});};// Services Hero Section\nconst ServicesHero=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"relative bg-primary-dark text-white py-20\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"container-custom relative z-10\",children:/*#__PURE__*/_jsxs(motion.div,{className:\"max-w-3xl\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5},children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-bold mb-6\",children:\"Our Services\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-300 mb-8\",children:\"Comprehensive cryptocurrency solutions tailored to your needs. From Bitcoin exchange to advanced trading tools, we've got you covered.\"})]})})]});};// Services List Section\nconst ServicesList=()=>{const services=[{id:\"bitcoin-exchange\",icon:/*#__PURE__*/_jsx(FaBitcoin,{className:\"text-4xl text-yellow-500\"}),title:\"Bitcoin Exchange\",description:\"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees. Our exchange platform provides a secure and user-friendly experience for both beginners and experienced traders.\",features:[\"Competitive exchange rates\",\"Low transaction fees\",\"Fast processing times\",\"Multiple payment options\",\"Secure transactions\"]},{id:\"crypto-trading\",icon:/*#__PURE__*/_jsx(FaChartLine,{className:\"text-4xl text-accent\"}),title:\"Crypto Trading\",description:\"Trade cryptocurrencies with advanced tools, charts, and market analysis. Our trading platform offers a range of features to help you make informed trading decisions and maximize your returns.\",features:[\"Advanced trading tools\",\"Real-time market data\",\"Customizable charts\",\"Stop-loss and take-profit orders\",\"Margin trading options\"]},{id:\"market-analysis\",icon:/*#__PURE__*/_jsx(FaChartBar,{className:\"text-4xl text-green-500\"}),title:\"Market Analysis\",description:\"Get detailed market insights and analysis to make informed trading decisions. Our team of experts provides regular market reports, trend analysis, and trading recommendations.\",features:[\"Daily market reports\",\"Technical analysis\",\"Trend identification\",\"Price prediction models\",\"Trading signals\"]},{id:\"security-solutions\",icon:/*#__PURE__*/_jsx(FaShieldAlt,{className:\"text-4xl text-red-500\"}),title:\"Security Solutions\",description:\"Advanced security solutions to protect your digital assets and investments. We implement industry-leading security measures to ensure the safety of your cryptocurrency holdings.\",features:[\"Cold storage solutions\",\"Multi-signature wallets\",\"Two-factor authentication\",\"Regular security audits\",\"Insurance coverage\"]},{id:\"consulting\",icon:/*#__PURE__*/_jsx(FaUserTie,{className:\"text-4xl text-orange-500\"}),title:\"Consulting Services\",description:\"Expert consulting services for individuals and businesses entering the crypto space. Our consultants provide guidance on cryptocurrency investments, blockchain technology, and regulatory compliance.\",features:[\"Investment strategies\",\"Blockchain implementation\",\"Regulatory compliance\",\"ICO/STO advisory\",\"Market entry strategies\"]}];return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",id:\"services\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",children:services.map((service,index)=>/*#__PURE__*/_jsx(ServiceCard,{id:service.id,icon:service.icon,title:service.title,description:service.description,features:service.features,index:index},service.id))})})});};// Featured Service Section\nconst FeaturedService=()=>{const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",id:\"featured-service\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.3}}},children:[/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-accent uppercase tracking-wider\",children:\"Featured Service\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold mb-6 text-primary-dark\",children:\"Advanced Bitcoin Trading Platform\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Our advanced Bitcoin trading platform provides you with all the tools and resources you need to succeed in the cryptocurrency market. With real-time market data, customizable charts, and a range of order types, you can execute your trading strategies with precision and confidence.\"}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-3 mb-8\",children:[\"Real-time market data and price alerts\",\"Advanced charting tools with multiple indicators\",\"Various order types including limit, market, and stop orders\",\"Low latency execution for high-frequency trading\",\"Mobile trading app for trading on the go\"].map((item,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(FaCheck,{className:\"text-accent mt-1 mr-2 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:item})]},index))}),/*#__PURE__*/_jsxs(\"a\",{href:\"https://wa.me/2348163309355\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"btn-primary flex items-center\",children:[\"Start Trading Now \",/*#__PURE__*/_jsx(FaArrowRight,{className:\"ml-2\"})]})]}),/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,scale:0.8},visible:{opacity:1,scale:1,transition:{duration:0.6}}},className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-primary-dark rounded-lg opacity-10 blur-xl transform translate-x-4 translate-y-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative bg-white p-4 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-w-16 aspect-h-9 bg-gray-800 rounded overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-full bg-gradient-to-br from-primary-dark to-primary-light p-4 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(FaChartLine,{className:\"text-6xl mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg font-semibold\",children:\"Trading Interface\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm opacity-75\",children:\"Advanced charts and tools\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 p-4 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"BTC/USD\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-green-500 font-semibold\",children:\"$48,235.67\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm text-gray-500\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"24h Change\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-green-500\",children:\"*****%\"})]})]})]})]})]})})});};// Pricing Section\nconst PricingSection=()=>{const plans=[{name:\"Basic\",price:\"0.2%\",description:\"Per transaction\",features:[\"Standard exchange rates\",\"Basic trading tools\",\"Email support\",\"Standard security features\",\"Mobile app access\"],cta:\"Start Trading\",popular:false},{name:\"Pro\",price:\"0.15%\",description:\"Per transaction\",features:[\"Competitive exchange rates\",\"Advanced trading tools\",\"Priority email support\",\"Enhanced security features\",\"API access\",\"Portfolio management tools\"],cta:\"Go Pro\",popular:true},{name:\"Enterprise\",price:\"Custom\",description:\"Tailored pricing\",features:[\"Premium exchange rates\",\"Full suite of trading tools\",\"24/7 dedicated support\",\"Advanced security solutions\",\"Custom API integration\",\"Personalized consulting\",\"White-label solutions\"],cta:\"Contact Us\",popular:false}];const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);};// FAQ Section\nconst FAQSection=()=>{const faqs=[{question:\"How do I start trading with BlazeTrade?\",answer:\"To start trading on BlazeTrade, you need to chat us on whatsapp or instagram and then you can begin trading cryptocurrencies or redemption of gift cards.\"},{question:\"What cryptocurrencies can I trade with BlazeTrade?\",answer:\"BlazeTrade supports trading of major cryptocurrencies including Bitcoin (BTC), Ethereum (ETH), Litecoin (LTC), Ripple (XRP), buying of giftcards and many more. We regularly add new cryptocurrencies based on market demand and our evaluation process.\"},{question:\"How secure is BlazeTrade?\",answer:\"Security is our top priority. We implement industry-leading security measures including cold storage for the majority of assets. regular security audits. We're a registered trading brand and our CAC number is RC 3696301\"},{question:\"What are the fees for trading with BlazeTrade?\",answer:\"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.2% depending on your trading volume. Please contact us for more details.\"},{question:\"Is BlazeTrade avaliable to trade anytime?\",answer:\"Yes we're round-the-clock available to trade 24/7. \"},{question:\"Does BlazeTrade respond quickly?\",answer:\"Yes, BlazeTrade responds under 2 minutes and trades your coin in few seconds.\"}];const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center max-w-3xl mx-auto mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:\"Frequently Asked Questions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"Find answers to common questions about our services and platform.\"})]}),/*#__PURE__*/_jsx(motion.div,{ref:ref,className:\"max-w-3xl mx-auto\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.2}}},children:faqs.map((faq,index)=>/*#__PURE__*/_jsx(FAQItem,{question:faq.question,answer:faq.answer,index:index},index))})]})});};// Service Card Component\nconst ServiceCard=_ref=>{let{id,icon,title,description,features,index}=_ref;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsxs(motion.div,{id:id,ref:ref,className:\"bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-3 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:description}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-2 mb-6\",children:features.map((feature,idx)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(FaCheck,{className:\"text-accent mt-1 mr-2 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:feature})]},idx))}),/*#__PURE__*/_jsxs(\"button\",{className:\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\",children:[\"Learn More \",/*#__PURE__*/_jsx(FaArrowRight,{className:\"ml-2\"})]})]});};// Pricing Card Component\nconst PricingCard=_ref2=>{let{name,price,description,features,cta,popular,index}=_ref2;return/*#__PURE__*/_jsxs(motion.div,{className:`rounded-lg overflow-hidden ${popular?'border-2 border-accent ring-4 ring-accent ring-opacity-20':'border border-gray-200'}`,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[popular&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-accent text-white text-center py-2 text-sm font-semibold\",children:\"Most Popular\"}),/*#__PURE__*/_jsxs(\"div\",{className:`p-6 ${popular?'bg-white':'bg-gray-50'}`,children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold mb-2 text-primary-dark\",children:name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-end mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-4xl font-bold text-primary-dark\",children:price}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500 ml-2\",children:description})]}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-3 mb-6\",children:features.map((feature,idx)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(FaCheck,{className:`mt-1 mr-2 flex-shrink-0 ${popular?'text-accent':'text-gray-400'}`}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:feature})]},idx))}),/*#__PURE__*/_jsx(\"button\",{className:`w-full py-2 rounded-md font-medium transition-colors duration-300 ${popular?'bg-accent text-white hover:bg-primary-dark':'bg-gray-200 text-gray-800 hover:bg-gray-300'}`,children:cta})]})]});};// FAQ Item Component\nconst FAQItem=_ref3=>{let{question,answer,index}=_ref3;const[isOpen,setIsOpen]=React.useState(false);return/*#__PURE__*/_jsxs(motion.div,{className:\"mb-4 border-b border-gray-200 pb-4\",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsxs(\"button\",{className:\"flex justify-between items-center w-full text-left font-semibold text-primary-dark hover:text-accent transition-colors duration-300\",onClick:()=>setIsOpen(!isOpen),children:[/*#__PURE__*/_jsx(\"span\",{children:question}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl\",children:isOpen?'−':'+'})]}),isOpen&&/*#__PURE__*/_jsx(motion.div,{className:\"mt-3 text-gray-600\",initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},transition:{duration:0.3},children:answer})]});};export default Services;", "map": {"version": 3, "names": ["React", "useEffect", "motion", "useAnimation", "useInView", "FaBitcoin", "FaChartLine", "FaChartBar", "FaShieldAlt", "FaUserTie", "FaArrowRight", "FaCheck", "jsx", "_jsx", "jsxs", "_jsxs", "Services", "className", "children", "ServicesHero", "ServicesList", "FeaturedService", "PricingSection", "FAQSection", "div", "initial", "opacity", "y", "animate", "transition", "duration", "services", "id", "icon", "title", "description", "features", "map", "service", "index", "ServiceCard", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "x", "item", "href", "target", "rel", "scale", "plans", "name", "price", "cta", "popular", "faqs", "question", "answer", "faq", "FAQItem", "_ref", "delay", "feature", "idx", "PricingCard", "_ref2", "_ref3", "isOpen", "setIsOpen", "useState", "onClick", "height"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Services.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  FaBitcoin,\n  FaChartLine, \n  FaChartBar, \n  FaShieldAlt, \n  FaUserTie,\n  FaArrowRight,\n  FaCheck\n} from 'react-icons/fa';\n\nconst Services = () => {\n  return (\n    <div className=\"pt-16\">\n      {/* Hero Section */}\n      <ServicesHero />\n      \n      {/* Services List */}\n      <ServicesList />\n      \n      {/* Featured Service */}\n      <FeaturedService />\n      \n      {/* Pricing */}\n      <PricingSection />\n      \n      {/* FAQ */}\n      <FAQSection />\n    </div>\n  );\n};\n\n// Services Hero Section\nconst ServicesHero = () => {\n  return (\n    <section className=\"relative bg-primary-dark text-white py-20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute right-0 bottom-0 w-1/3 h-1/3 bg-accent opacity-10 blur-3xl\"></div>\n        <div className=\"absolute left-0 top-0 w-1/4 h-1/4 bg-primary-light opacity-20 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10\">\n        <motion.div \n          className=\"max-w-3xl\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">Our Services</h1>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Comprehensive cryptocurrency solutions tailored to your needs. \n            From Bitcoin exchange to advanced trading tools, we've got you covered.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Services List Section\nconst ServicesList = () => {\n  const services = [\n    {\n      id: \"bitcoin-exchange\",\n      icon: <FaBitcoin className=\"text-4xl text-yellow-500\" />,\n      title: \"Bitcoin Exchange\",\n      description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees. Our exchange platform provides a secure and user-friendly experience for both beginners and experienced traders.\",\n      features: [\n        \"Competitive exchange rates\",\n        \"Low transaction fees\",\n        \"Fast processing times\",\n        \"Multiple payment options\",\n        \"Secure transactions\"\n      ]\n    },\n    {\n      id: \"crypto-trading\",\n      icon: <FaChartLine className=\"text-4xl text-accent\" />,\n      title: \"Crypto Trading\",\n      description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis. Our trading platform offers a range of features to help you make informed trading decisions and maximize your returns.\",\n      features: [\n        \"Advanced trading tools\",\n        \"Real-time market data\",\n        \"Customizable charts\",\n        \"Stop-loss and take-profit orders\",\n        \"Margin trading options\"\n      ]\n    },\n    {\n      id: \"market-analysis\",\n      icon: <FaChartBar className=\"text-4xl text-green-500\" />,\n      title: \"Market Analysis\",\n      description: \"Get detailed market insights and analysis to make informed trading decisions. Our team of experts provides regular market reports, trend analysis, and trading recommendations.\",\n      features: [\n        \"Daily market reports\",\n        \"Technical analysis\",\n        \"Trend identification\",\n        \"Price prediction models\",\n        \"Trading signals\"\n      ]\n    },\n    \n    {\n      id: \"security-solutions\",\n      icon: <FaShieldAlt className=\"text-4xl text-red-500\" />,\n      title: \"Security Solutions\",\n      description: \"Advanced security solutions to protect your digital assets and investments. We implement industry-leading security measures to ensure the safety of your cryptocurrency holdings.\",\n      features: [\n        \"Cold storage solutions\",\n        \"Multi-signature wallets\",\n        \"Two-factor authentication\",\n        \"Regular security audits\",\n        \"Insurance coverage\"\n      ]\n    },\n    {\n      id: \"consulting\",\n      icon: <FaUserTie className=\"text-4xl text-orange-500\" />,\n      title: \"Consulting Services\",\n      description: \"Expert consulting services for individuals and businesses entering the crypto space. Our consultants provide guidance on cryptocurrency investments, blockchain technology, and regulatory compliance.\",\n      features: [\n        \"Investment strategies\",\n        \"Blockchain implementation\",\n        \"Regulatory compliance\",\n        \"ICO/STO advisory\",\n        \"Market entry strategies\"\n      ]\n    }\n  ];\n\n  return (\n    <section className=\"section bg-white\" id=\"services\">\n      <div className=\"container-custom\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <ServiceCard \n              key={service.id}\n              id={service.id}\n              icon={service.icon}\n              title={service.title}\n              description={service.description}\n              features={service.features}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Featured Service Section\nconst FeaturedService = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\" id=\"featured-service\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.3\n              }\n            }\n          }}\n        >\n          {/* Content */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: -30 },\n              visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }\n            }}\n          >\n            <div className=\"mb-4\">\n              <span className=\"text-sm font-semibold text-accent uppercase tracking-wider\">Featured Service</span>\n            </div>\n            <h2 className=\"text-3xl font-bold mb-6 text-primary-dark\">Advanced Bitcoin Trading Platform</h2>\n            <p className=\"text-gray-600 mb-6\">\n              Our advanced Bitcoin trading platform provides you with all the tools and resources you need to succeed in the cryptocurrency market. \n              With real-time market data, customizable charts, and a range of order types, you can execute your trading strategies with precision and confidence.\n            </p>\n            <ul className=\"space-y-3 mb-8\">\n              {[\n                \"Real-time market data and price alerts\",\n                \"Advanced charting tools with multiple indicators\",\n                \"Various order types including limit, market, and stop orders\",\n                \"Low latency execution for high-frequency trading\",\n                \"Mobile trading app for trading on the go\"\n              ].map((item, index) => (\n                <li key={index} className=\"flex items-start\">\n                  <FaCheck className=\"text-accent mt-1 mr-2 flex-shrink-0\" />\n                  <span className=\"text-gray-700\">{item}</span>\n                </li>\n              ))}\n            </ul>\n            <a href=\"https://wa.me/2348163309355\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"btn-primary flex items-center\">\n              Start Trading Now <FaArrowRight className=\"ml-2\" />\n            </a>\n          </motion.div>\n          \n          {/* Image */}\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, scale: 0.8 },\n              visible: { opacity: 1, scale: 1, transition: { duration: 0.6 } }\n            }}\n            className=\"relative\"\n          >\n            <div className=\"absolute inset-0 bg-primary-dark rounded-lg opacity-10 blur-xl transform translate-x-4 translate-y-4\"></div>\n            <div className=\"relative bg-white p-4 rounded-lg shadow-lg\">\n              <div className=\"aspect-w-16 aspect-h-9 bg-gray-800 rounded overflow-hidden\">\n                {/* This would be a chart or trading interface image */}\n                <div className=\"w-full h-full bg-gradient-to-br from-primary-dark to-primary-light p-4 flex items-center justify-center\">\n                  <div className=\"text-center text-white\">\n                    <FaChartLine className=\"text-6xl mx-auto mb-4\" />\n                    <p className=\"text-lg font-semibold\">Trading Interface</p>\n                    <p className=\"text-sm opacity-75\">Advanced charts and tools</p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"mt-4 p-4 bg-gray-50 rounded\">\n                <div className=\"flex justify-between items-center mb-2\">\n                  <span className=\"font-medium\">BTC/USD</span>\n                  <span className=\"text-green-500 font-semibold\">$48,235.67</span>\n                </div>\n                <div className=\"flex justify-between text-sm text-gray-500\">\n                  <span>24h Change</span>\n                  <span className=\"text-green-500\">*****%</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Pricing Section\nconst PricingSection = () => {\n  const plans = [\n    {\n      name: \"Basic\",\n      price: \"0.2%\",\n      description: \"Per transaction\",\n      features: [\n        \"Standard exchange rates\",\n        \"Basic trading tools\",\n        \"Email support\",\n        \"Standard security features\",\n        \"Mobile app access\"\n      ],\n      cta: \"Start Trading\",\n      popular: false\n    },\n    {\n      name: \"Pro\",\n      price: \"0.15%\",\n      description: \"Per transaction\",\n      features: [\n        \"Competitive exchange rates\",\n        \"Advanced trading tools\",\n        \"Priority email support\",\n        \"Enhanced security features\",\n        \"API access\",\n        \"Portfolio management tools\"\n      ],\n      cta: \"Go Pro\",\n      popular: true\n    },\n    {\n      name: \"Enterprise\",\n      price: \"Custom\",\n      description: \"Tailored pricing\",\n      features: [\n        \"Premium exchange rates\",\n        \"Full suite of trading tools\",\n        \"24/7 dedicated support\",\n        \"Advanced security solutions\",\n        \"Custom API integration\",\n        \"Personalized consulting\",\n        \"White-label solutions\"\n      ],\n      cta: \"Contact Us\",\n      popular: false\n    }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  \n};\n\n// FAQ Section\nconst FAQSection = () => {\n  const faqs = [\n    {\n      question: \"How do I start trading with BlazeTrade?\",\n      answer: \"To start trading on BlazeTrade, you need to chat us on whatsapp or instagram and then you can begin trading cryptocurrencies or redemption of gift cards.\"\n    },\n    {\n      question: \"What cryptocurrencies can I trade with BlazeTrade?\",\n      answer: \"BlazeTrade supports trading of major cryptocurrencies including Bitcoin (BTC), Ethereum (ETH), Litecoin (LTC), Ripple (XRP), buying of giftcards and many more. We regularly add new cryptocurrencies based on market demand and our evaluation process.\"\n    },\n    {\n      question: \"How secure is BlazeTrade?\",\n      answer: \"Security is our top priority. We implement industry-leading security measures including cold storage for the majority of assets. regular security audits. We're a registered trading brand and our CAC number is RC 3696301\"\n    },\n    {\n      question: \"What are the fees for trading with BlazeTrade?\",\n      answer: \"Our fee structure is transparent and competitive. Trading fees range from 0.1% to 0.2% depending on your trading volume. Please contact us for more details.\"\n    },\n    {\n      question: \"Is BlazeTrade avaliable to trade anytime?\",\n      answer: \"Yes we're round-the-clock available to trade 24/7. \"\n    },\n    {\n      question: \"Does BlazeTrade respond quickly?\",\n      answer: \"Yes, BlazeTrade responds under 2 minutes and trades your coin in few seconds.\"\n    }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center max-w-3xl mx-auto mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Frequently Asked Questions</h2>\n          <p className=\"text-lg text-gray-600\">\n            Find answers to common questions about our services and platform.\n          </p>\n        </div>\n        \n        <motion.div \n          ref={ref}\n          className=\"max-w-3xl mx-auto\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.2\n              }\n            }\n          }}\n        >\n          {faqs.map((faq, index) => (\n            <FAQItem \n              key={index}\n              question={faq.question}\n              answer={faq.answer}\n              index={index}\n            />\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Service Card Component\nconst ServiceCard = ({ id, icon, title, description, features, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      id={id}\n      ref={ref}\n      className=\"bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"mb-4\">{icon}</div>\n      <h3 className=\"text-xl font-semibold mb-3 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600 mb-4\">{description}</p>\n      <ul className=\"space-y-2 mb-6\">\n        {features.map((feature, idx) => (\n          <li key={idx} className=\"flex items-start\">\n            <FaCheck className=\"text-accent mt-1 mr-2 flex-shrink-0\" />\n            <span className=\"text-gray-700\">{feature}</span>\n          </li>\n        ))}\n      </ul>\n      <button className=\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\">\n        Learn More <FaArrowRight className=\"ml-2\" />\n      </button>\n    </motion.div>\n  );\n};\n\n// Pricing Card Component\nconst PricingCard = ({ name, price, description, features, cta, popular, index }) => {\n  return (\n    <motion.div \n      className={`rounded-lg overflow-hidden ${popular ? 'border-2 border-accent ring-4 ring-accent ring-opacity-20' : 'border border-gray-200'}`}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      {popular && (\n        <div className=\"bg-accent text-white text-center py-2 text-sm font-semibold\">\n          Most Popular\n        </div>\n      )}\n      <div className={`p-6 ${popular ? 'bg-white' : 'bg-gray-50'}`}>\n        <h3 className=\"text-xl font-bold mb-2 text-primary-dark\">{name}</h3>\n        <div className=\"flex items-end mb-4\">\n          <span className=\"text-4xl font-bold text-primary-dark\">{price}</span>\n          <span className=\"text-gray-500 ml-2\">{description}</span>\n        </div>\n        <ul className=\"space-y-3 mb-6\">\n          {features.map((feature, idx) => (\n            <li key={idx} className=\"flex items-start\">\n              <FaCheck className={`mt-1 mr-2 flex-shrink-0 ${popular ? 'text-accent' : 'text-gray-400'}`} />\n              <span className=\"text-gray-700\">{feature}</span>\n            </li>\n          ))}\n        </ul>\n        <button \n          className={`w-full py-2 rounded-md font-medium transition-colors duration-300 ${popular \n            ? 'bg-accent text-white hover:bg-primary-dark' \n            : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}\n        >\n          {cta}\n        </button>\n      </div>\n    </motion.div>\n  );\n};\n\n// FAQ Item Component\nconst FAQItem = ({ question, answer, index }) => {\n  const [isOpen, setIsOpen] = React.useState(false);\n\n  return (\n    <motion.div \n      className=\"mb-4 border-b border-gray-200 pb-4\"\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <button \n        className=\"flex justify-between items-center w-full text-left font-semibold text-primary-dark hover:text-accent transition-colors duration-300\"\n        onClick={() => setIsOpen(!isOpen)}\n      >\n        <span>{question}</span>\n        <span className=\"text-xl\">{isOpen ? '−' : '+'}</span>\n      </button>\n      {isOpen && (\n        <motion.div \n          className=\"mt-3 text-gray-600\"\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          transition={{ duration: 0.3 }}\n        >\n          {answer}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Services;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,YAAY,KAAQ,eAAe,CACpD,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OACEC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,WAAW,CACXC,SAAS,CACTC,YAAY,CACZC,OAAO,KACF,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExB,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,mBACED,KAAA,QAAKE,SAAS,CAAC,OAAO,CAAAC,QAAA,eAEpBL,IAAA,CAACM,YAAY,GAAE,CAAC,cAGhBN,IAAA,CAACO,YAAY,GAAE,CAAC,cAGhBP,IAAA,CAACQ,eAAe,GAAE,CAAC,cAGnBR,IAAA,CAACS,cAAc,GAAE,CAAC,cAGlBT,IAAA,CAACU,UAAU,GAAE,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAJ,YAAY,CAAGA,CAAA,GAAM,CACzB,mBACEJ,KAAA,YAASE,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eAE5DH,KAAA,QAAKE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CL,IAAA,QAAKI,SAAS,CAAC,qEAAqE,CAAM,CAAC,cAC3FJ,IAAA,QAAKI,SAAS,CAAC,wEAAwE,CAAM,CAAC,EAC3F,CAAC,cAENJ,IAAA,QAAKI,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAC,WAAW,CACrBQ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,eAE9BL,IAAA,OAAII,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACrEL,IAAA,MAAGI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,wIAG1C,CAAG,CAAC,EACM,CAAC,CACV,CAAC,EACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAW,QAAQ,CAAG,CACf,CACEC,EAAE,CAAE,kBAAkB,CACtBC,IAAI,cAAEpB,IAAA,CAACR,SAAS,EAACY,SAAS,CAAC,0BAA0B,CAAE,CAAC,CACxDiB,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,uMAAuM,CACpNC,QAAQ,CAAE,CACR,4BAA4B,CAC5B,sBAAsB,CACtB,uBAAuB,CACvB,0BAA0B,CAC1B,qBAAqB,CAEzB,CAAC,CACD,CACEJ,EAAE,CAAE,gBAAgB,CACpBC,IAAI,cAAEpB,IAAA,CAACP,WAAW,EAACW,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACtDiB,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,iMAAiM,CAC9MC,QAAQ,CAAE,CACR,wBAAwB,CACxB,uBAAuB,CACvB,qBAAqB,CACrB,kCAAkC,CAClC,wBAAwB,CAE5B,CAAC,CACD,CACEJ,EAAE,CAAE,iBAAiB,CACrBC,IAAI,cAAEpB,IAAA,CAACN,UAAU,EAACU,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACxDiB,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,iLAAiL,CAC9LC,QAAQ,CAAE,CACR,sBAAsB,CACtB,oBAAoB,CACpB,sBAAsB,CACtB,yBAAyB,CACzB,iBAAiB,CAErB,CAAC,CAED,CACEJ,EAAE,CAAE,oBAAoB,CACxBC,IAAI,cAAEpB,IAAA,CAACL,WAAW,EAACS,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvDiB,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,mLAAmL,CAChMC,QAAQ,CAAE,CACR,wBAAwB,CACxB,yBAAyB,CACzB,2BAA2B,CAC3B,yBAAyB,CACzB,oBAAoB,CAExB,CAAC,CACD,CACEJ,EAAE,CAAE,YAAY,CAChBC,IAAI,cAAEpB,IAAA,CAACJ,SAAS,EAACQ,SAAS,CAAC,0BAA0B,CAAE,CAAC,CACxDiB,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CAAE,wMAAwM,CACrNC,QAAQ,CAAE,CACR,uBAAuB,CACvB,2BAA2B,CAC3B,uBAAuB,CACvB,kBAAkB,CAClB,yBAAyB,CAE7B,CAAC,CACF,CAED,mBACEvB,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAACe,EAAE,CAAC,UAAU,CAAAd,QAAA,cACjDL,IAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BL,IAAA,QAAKI,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEa,QAAQ,CAACM,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3B1B,IAAA,CAAC2B,WAAW,EAEVR,EAAE,CAAEM,OAAO,CAACN,EAAG,CACfC,IAAI,CAAEK,OAAO,CAACL,IAAK,CACnBC,KAAK,CAAEI,OAAO,CAACJ,KAAM,CACrBC,WAAW,CAAEG,OAAO,CAACH,WAAY,CACjCC,QAAQ,CAAEE,OAAO,CAACF,QAAS,CAC3BG,KAAK,CAAEA,KAAM,EANRD,OAAO,CAACN,EAOd,CACF,CAAC,CACC,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAX,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAoB,QAAQ,CAAGtC,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACuC,GAAG,CAAEC,MAAM,CAAC,CAAGvC,SAAS,CAAC,CAC9BwC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF5C,SAAS,CAAC,IAAM,CACd,GAAI0C,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE9B,IAAA,YAASI,SAAS,CAAC,oBAAoB,CAACe,EAAE,CAAC,kBAAkB,CAAAd,QAAA,cAC3DL,IAAA,QAAKI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTkB,GAAG,CAAEA,GAAI,CACTzB,SAAS,CAAC,qDAAqD,CAC/DQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEa,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAE,CAAC,CACtBuB,OAAO,CAAE,CACPvB,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVqB,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAhC,QAAA,eAGFH,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTuB,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAC,CAAEyB,CAAC,CAAE,CAAC,EAAG,CAAC,CAC9BF,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEyB,CAAC,CAAE,CAAC,CAAEtB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAZ,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBL,IAAA,SAAMI,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,CACjG,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,mCAAiC,CAAI,CAAC,cAChGL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2RAGlC,CAAG,CAAC,cACJL,IAAA,OAAII,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,CACC,wCAAwC,CACxC,kDAAkD,CAClD,8DAA8D,CAC9D,kDAAkD,CAClD,0CAA0C,CAC3C,CAACmB,GAAG,CAAC,CAACe,IAAI,CAAEb,KAAK,gBAChBxB,KAAA,OAAgBE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC1CL,IAAA,CAACF,OAAO,EAACM,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC3DJ,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEkC,IAAI,CAAO,CAAC,GAFtCb,KAGL,CACL,CAAC,CACA,CAAC,cACLxB,KAAA,MAAGsC,IAAI,CAAC,6BAA6B,CAACC,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAACtC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,EAAC,oBACtG,cAAAL,IAAA,CAACH,YAAY,EAACO,SAAS,CAAC,MAAM,CAAE,CAAC,EAClD,CAAC,EACM,CAAC,cAGbF,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTuB,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAC,CAAE8B,KAAK,CAAE,GAAI,CAAC,CAClCP,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE8B,KAAK,CAAE,CAAC,CAAE3B,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CACjE,CAAE,CACFb,SAAS,CAAC,UAAU,CAAAC,QAAA,eAEpBL,IAAA,QAAKI,SAAS,CAAC,sGAAsG,CAAM,CAAC,cAC5HF,KAAA,QAAKE,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDL,IAAA,QAAKI,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cAEzEL,IAAA,QAAKI,SAAS,CAAC,yGAAyG,CAAAC,QAAA,cACtHH,KAAA,QAAKE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCL,IAAA,CAACP,WAAW,EAACW,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACjDJ,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,cAC1DL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC5D,CAAC,CACH,CAAC,CACH,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CH,KAAA,QAAKE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDL,IAAA,SAAMI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cAC5CL,IAAA,SAAMI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EAC7D,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDL,IAAA,SAAAK,QAAA,CAAM,YAAU,CAAM,CAAC,cACvBL,IAAA,SAAMI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EAC3C,CAAC,EACH,CAAC,EACH,CAAC,EACI,CAAC,EACH,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAI,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAmC,KAAK,CAAG,CACZ,CACEC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,MAAM,CACbxB,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CACR,yBAAyB,CACzB,qBAAqB,CACrB,eAAe,CACf,4BAA4B,CAC5B,mBAAmB,CACpB,CACDwB,GAAG,CAAE,eAAe,CACpBC,OAAO,CAAE,KACX,CAAC,CACD,CACEH,IAAI,CAAE,KAAK,CACXC,KAAK,CAAE,OAAO,CACdxB,WAAW,CAAE,iBAAiB,CAC9BC,QAAQ,CAAE,CACR,4BAA4B,CAC5B,wBAAwB,CACxB,wBAAwB,CACxB,4BAA4B,CAC5B,YAAY,CACZ,4BAA4B,CAC7B,CACDwB,GAAG,CAAE,QAAQ,CACbC,OAAO,CAAE,IACX,CAAC,CACD,CACEH,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,QAAQ,CACfxB,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,CACR,wBAAwB,CACxB,6BAA6B,CAC7B,wBAAwB,CACxB,6BAA6B,CAC7B,wBAAwB,CACxB,yBAAyB,CACzB,uBAAuB,CACxB,CACDwB,GAAG,CAAE,YAAY,CACjBC,OAAO,CAAE,KACX,CAAC,CACF,CAED,KAAM,CAAApB,QAAQ,CAAGtC,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACuC,GAAG,CAAEC,MAAM,CAAC,CAAGvC,SAAS,CAAC,CAC9BwC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF5C,SAAS,CAAC,IAAM,CACd,GAAI0C,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAGxB,CAAC,CAED;AACA,KAAM,CAAApB,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAuC,IAAI,CAAG,CACX,CACEC,QAAQ,CAAE,yCAAyC,CACnDC,MAAM,CAAE,2JACV,CAAC,CACD,CACED,QAAQ,CAAE,oDAAoD,CAC9DC,MAAM,CAAE,0PACV,CAAC,CACD,CACED,QAAQ,CAAE,2BAA2B,CACrCC,MAAM,CAAE,6NACV,CAAC,CACD,CACED,QAAQ,CAAE,gDAAgD,CAC1DC,MAAM,CAAE,8JACV,CAAC,CACD,CACED,QAAQ,CAAE,2CAA2C,CACrDC,MAAM,CAAE,qDACV,CAAC,CACD,CACED,QAAQ,CAAE,kCAAkC,CAC5CC,MAAM,CAAE,+EACV,CAAC,CACF,CAED,KAAM,CAAAvB,QAAQ,CAAGtC,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACuC,GAAG,CAAEC,MAAM,CAAC,CAAGvC,SAAS,CAAC,CAC9BwC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF5C,SAAS,CAAC,IAAM,CACd,GAAI0C,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE9B,IAAA,YAASI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACrCH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BH,KAAA,QAAKE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDL,IAAA,OAAII,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,4BAA0B,CAAI,CAAC,cACrGL,IAAA,MAAGI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mEAErC,CAAG,CAAC,EACD,CAAC,cAENL,IAAA,CAACX,MAAM,CAACsB,GAAG,EACTkB,GAAG,CAAEA,GAAI,CACTzB,SAAS,CAAC,mBAAmB,CAC7BQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEa,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAE,CAAC,CACtBuB,OAAO,CAAE,CACPvB,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVqB,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAhC,QAAA,CAED4C,IAAI,CAACzB,GAAG,CAAC,CAAC4B,GAAG,CAAE1B,KAAK,gBACnB1B,IAAA,CAACqD,OAAO,EAENH,QAAQ,CAAEE,GAAG,CAACF,QAAS,CACvBC,MAAM,CAAEC,GAAG,CAACD,MAAO,CACnBzB,KAAK,CAAEA,KAAM,EAHRA,KAIN,CACF,CAAC,CACQ,CAAC,EACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAG2B,IAAA,EAAuD,IAAtD,CAAEnC,EAAE,CAAEC,IAAI,CAAEC,KAAK,CAAEC,WAAW,CAAEC,QAAQ,CAAEG,KAAM,CAAC,CAAA4B,IAAA,CACpE,KAAM,CAAA1B,QAAQ,CAAGtC,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACuC,GAAG,CAAEC,MAAM,CAAC,CAAGvC,SAAS,CAAC,CAC9BwC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF5C,SAAS,CAAC,IAAM,CACd,GAAI0C,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE5B,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTQ,EAAE,CAAEA,EAAG,CACPU,GAAG,CAAEA,GAAI,CACTzB,SAAS,CAAC,yGAAyG,CACnHQ,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEa,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BsB,OAAO,CAAE,CACPvB,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbsC,KAAK,CAAE7B,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAArB,QAAA,eAEFL,IAAA,QAAKI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEe,IAAI,CAAM,CAAC,cAClCpB,IAAA,OAAII,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEgB,KAAK,CAAK,CAAC,cACzErB,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEiB,WAAW,CAAI,CAAC,cACnDtB,IAAA,OAAII,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3BkB,QAAQ,CAACC,GAAG,CAAC,CAACgC,OAAO,CAAEC,GAAG,gBACzBvD,KAAA,OAAcE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACxCL,IAAA,CAACF,OAAO,EAACM,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC3DJ,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEmD,OAAO,CAAO,CAAC,GAFzCC,GAGL,CACL,CAAC,CACA,CAAC,cACLvD,KAAA,WAAQE,SAAS,CAAC,kGAAkG,CAAAC,QAAA,EAAC,aACxG,cAAAL,IAAA,CAACH,YAAY,EAACO,SAAS,CAAC,MAAM,CAAE,CAAC,EACtC,CAAC,EACC,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAsD,WAAW,CAAGC,KAAA,EAAiE,IAAhE,CAAEd,IAAI,CAAEC,KAAK,CAAExB,WAAW,CAAEC,QAAQ,CAAEwB,GAAG,CAAEC,OAAO,CAAEtB,KAAM,CAAC,CAAAiC,KAAA,CAC9E,mBACEzD,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAE,8BAA8B4C,OAAO,CAAG,2DAA2D,CAAG,wBAAwB,EAAG,CAC5Id,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BsB,OAAO,CAAE,CACPvB,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbsC,KAAK,CAAE7B,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAArB,QAAA,EAED2C,OAAO,eACNhD,IAAA,QAAKI,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,cAE7E,CAAK,CACN,cACDH,KAAA,QAAKE,SAAS,CAAE,OAAO4C,OAAO,CAAG,UAAU,CAAG,YAAY,EAAG,CAAA3C,QAAA,eAC3DL,IAAA,OAAII,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEwC,IAAI,CAAK,CAAC,cACpE3C,KAAA,QAAKE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCL,IAAA,SAAMI,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAEyC,KAAK,CAAO,CAAC,cACrE9C,IAAA,SAAMI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEiB,WAAW,CAAO,CAAC,EACtD,CAAC,cACNtB,IAAA,OAAII,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3BkB,QAAQ,CAACC,GAAG,CAAC,CAACgC,OAAO,CAAEC,GAAG,gBACzBvD,KAAA,OAAcE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACxCL,IAAA,CAACF,OAAO,EAACM,SAAS,CAAE,2BAA2B4C,OAAO,CAAG,aAAa,CAAG,eAAe,EAAG,CAAE,CAAC,cAC9FhD,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEmD,OAAO,CAAO,CAAC,GAFzCC,GAGL,CACL,CAAC,CACA,CAAC,cACLzD,IAAA,WACEI,SAAS,CAAE,qEAAqE4C,OAAO,CACnF,4CAA4C,CAC5C,6CAA6C,EAAG,CAAA3C,QAAA,CAEnD0C,GAAG,CACE,CAAC,EACN,CAAC,EACI,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAM,OAAO,CAAGO,KAAA,EAAiC,IAAhC,CAAEV,QAAQ,CAAEC,MAAM,CAAEzB,KAAM,CAAC,CAAAkC,KAAA,CAC1C,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG3E,KAAK,CAAC4E,QAAQ,CAAC,KAAK,CAAC,CAEjD,mBACE7D,KAAA,CAACb,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAC,oCAAoC,CAC9C8B,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAEtB,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7BsB,OAAO,CAAE,CACPvB,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbsC,KAAK,CAAE7B,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAArB,QAAA,eAEFH,KAAA,WACEE,SAAS,CAAC,qIAAqI,CAC/I4D,OAAO,CAAEA,CAAA,GAAMF,SAAS,CAAC,CAACD,MAAM,CAAE,CAAAxD,QAAA,eAElCL,IAAA,SAAAK,QAAA,CAAO6C,QAAQ,CAAO,CAAC,cACvBlD,IAAA,SAAMI,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEwD,MAAM,CAAG,GAAG,CAAG,GAAG,CAAO,CAAC,EAC/C,CAAC,CACRA,MAAM,eACL7D,IAAA,CAACX,MAAM,CAACsB,GAAG,EACTP,SAAS,CAAC,oBAAoB,CAC9BQ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEoD,MAAM,CAAE,CAAE,CAAE,CACnClD,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEoD,MAAM,CAAE,MAAO,CAAE,CACxCjD,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,CAE7B8C,MAAM,CACG,CACb,EACS,CAAC,CAEjB,CAAC,CAED,cAAe,CAAAhD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}