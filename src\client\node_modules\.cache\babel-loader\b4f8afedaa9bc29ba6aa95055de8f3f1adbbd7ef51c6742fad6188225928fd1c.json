{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,useLocation,Navigate}from'react-router-dom';import{motion}from'framer-motion';// Pages\nimport Home from'./pages/Home';import About from'./pages/About';import Services from'./pages/Services';import Contact from'./pages/Contact';import Signup from'./pages/Signup';import Login from'./pages/Login';import LandingPage from'./pages/LandingPage';import CheckEmail from'./pages/CheckEmail';import EmailVerification from'./pages/EmailVerification';import ForgotPassword from'./pages/ForgotPassword';// Components\nimport Navbar from'./components/Navbar';import Footer from'./components/Footer';import AuthFooter from'./components/AuthFooter';import Chatbot from'./components/Chatbot';import ScrollToTop from'./components/ScrollToTop';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PrivateRoute=_ref=>{let{children}=_ref;const token=localStorage.getItem('token');return token?children:/*#__PURE__*/_jsx(Navigate,{to:\"/\"});};const PublicRoute=_ref2=>{let{children}=_ref2;const isAuthenticated=!!localStorage.getItem('token');return isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\"}):children;};const Layout=()=>{const location=useLocation();const isAuthPage=location.pathname==='/login'||location.pathname==='/signup';const isLandingPage=location.pathname==='/';return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col min-h-screen bg-gray-900\",children:[!isAuthPage&&/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(motion.main,{className:\"flex-grow\",initial:{opacity:0},animate:{opacity:1},transition:{duration:0.5},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(LandingPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(PrivateRoute,{children:/*#__PURE__*/_jsx(Home,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/about\",element:/*#__PURE__*/_jsx(PrivateRoute,{children:/*#__PURE__*/_jsx(About,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/services\",element:/*#__PURE__*/_jsx(PrivateRoute,{children:/*#__PURE__*/_jsx(Services,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/contact\",element:/*#__PURE__*/_jsx(PrivateRoute,{children:/*#__PURE__*/_jsx(Contact,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/signup\",element:/*#__PURE__*/_jsx(Signup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(ForgotPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/check-email\",element:/*#__PURE__*/_jsx(CheckEmail,{})}),/*#__PURE__*/_jsx(Route,{path:\"/verify-email/:token\",element:/*#__PURE__*/_jsx(EmailVerification,{})})]})}),!isAuthPage&&/*#__PURE__*/_jsx(Chatbot,{}),!isAuthPage&&!isLandingPage&&/*#__PURE__*/_jsx(Footer,{}),isAuthPage&&/*#__PURE__*/_jsx(AuthFooter,{})]});};function App(){return/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsx(ScrollToTop,{}),/*#__PURE__*/_jsx(Layout,{})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "Navigate", "motion", "Home", "About", "Services", "Contact", "Signup", "<PERSON><PERSON>", "LandingPage", "CheckEmail", "EmailVerification", "ForgotPassword", "<PERSON><PERSON><PERSON>", "Footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ScrollToTop", "jsx", "_jsx", "jsxs", "_jsxs", "PrivateRoute", "_ref", "children", "token", "localStorage", "getItem", "to", "PublicRoute", "_ref2", "isAuthenticated", "Layout", "location", "isAuthPage", "pathname", "isLandingPage", "className", "main", "initial", "opacity", "animate", "transition", "duration", "path", "element", "App"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Services from './pages/Services';\nimport Contact from './pages/Contact';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\nimport LandingPage from './pages/LandingPage';\nimport CheckEmail from './pages/CheckEmail';\nimport EmailVerification from './pages/EmailVerification';\nimport ForgotPassword from './pages/ForgotPassword';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport AuthFooter from './components/AuthFooter';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\n\nconst PrivateRoute = ({ children }) => {\n  const token = localStorage.getItem('token');\n  return token ? children : <Navigate to=\"/\" />;\n};\n\nconst PublicRoute = ({ children }) => {\n  const isAuthenticated = !!localStorage.getItem('token');\n  return isAuthenticated ? <Navigate to=\"/dashboard\" /> : children;\n};\n\n\n\nconst Layout = () => {\n  const location = useLocation();\n  const isAuthPage = location.pathname === '/login' || location.pathname === '/signup';\n  const isLandingPage = location.pathname === '/';\n\n  return (\n    <div className=\"flex flex-col min-h-screen bg-gray-900\">\n      {!isAuthPage && <Navbar />}\n      <motion.main\n        className=\"flex-grow\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Routes>\n          <Route path=\"/\" element={<PublicRoute><LandingPage /></PublicRoute>} />\n          <Route path=\"/dashboard\" element={<PrivateRoute><Home /></PrivateRoute>} />\n          <Route path=\"/about\" element={<PrivateRoute><About /></PrivateRoute>} />\n          <Route path=\"/services\" element={<PrivateRoute><Services /></PrivateRoute>} />\n          <Route path=\"/contact\" element={<PrivateRoute><Contact /></PrivateRoute>} />\n          <Route path=\"/signup\" element={<Signup />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n          <Route path=\"/check-email\" element={<CheckEmail />} />\n          <Route path=\"/verify-email/:token\" element={<EmailVerification />} />\n        </Routes>\n      </motion.main>\n      {!isAuthPage && <Chatbot />}\n      {!isAuthPage && !isLandingPage && <Footer />}\n      {isAuthPage && <AuthFooter />}\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <ScrollToTop />\n      <Layout />\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,WAAW,CAAEC,QAAQ,KAAQ,kBAAkB,CAChG,OAASC,MAAM,KAAQ,eAAe,CAEtC;AACA,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,MAAM,KAAM,gBAAgB,CACnC,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CAEnD;AACA,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAChC,KAAM,CAAAE,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,MAAO,CAAAF,KAAK,CAAGD,QAAQ,cAAGL,IAAA,CAAClB,QAAQ,EAAC2B,EAAE,CAAC,GAAG,CAAE,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGC,KAAA,EAAkB,IAAjB,CAAEN,QAAS,CAAC,CAAAM,KAAA,CAC/B,KAAM,CAAAC,eAAe,CAAG,CAAC,CAACL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CACvD,MAAO,CAAAI,eAAe,cAAGZ,IAAA,CAAClB,QAAQ,EAAC2B,EAAE,CAAC,YAAY,CAAE,CAAC,CAAGJ,QAAQ,CAClE,CAAC,CAID,KAAM,CAAAQ,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAAC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkC,UAAU,CAAGD,QAAQ,CAACE,QAAQ,GAAK,QAAQ,EAAIF,QAAQ,CAACE,QAAQ,GAAK,SAAS,CACpF,KAAM,CAAAC,aAAa,CAAGH,QAAQ,CAACE,QAAQ,GAAK,GAAG,CAE/C,mBACEd,KAAA,QAAKgB,SAAS,CAAC,wCAAwC,CAAAb,QAAA,EACpD,CAACU,UAAU,eAAIf,IAAA,CAACN,MAAM,GAAE,CAAC,cAC1BM,IAAA,CAACjB,MAAM,CAACoC,IAAI,EACVD,SAAS,CAAC,WAAW,CACrBE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAnB,QAAA,cAE9BH,KAAA,CAACvB,MAAM,EAAA0B,QAAA,eACLL,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE1B,IAAA,CAACU,WAAW,EAAAL,QAAA,cAACL,IAAA,CAACV,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvEU,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,YAAY,CAACC,OAAO,cAAE1B,IAAA,CAACG,YAAY,EAAAE,QAAA,cAACL,IAAA,CAAChB,IAAI,GAAE,CAAC,CAAc,CAAE,CAAE,CAAC,cAC3EgB,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE1B,IAAA,CAACG,YAAY,EAAAE,QAAA,cAACL,IAAA,CAACf,KAAK,GAAE,CAAC,CAAc,CAAE,CAAE,CAAC,cACxEe,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE1B,IAAA,CAACG,YAAY,EAAAE,QAAA,cAACL,IAAA,CAACd,QAAQ,GAAE,CAAC,CAAc,CAAE,CAAE,CAAC,cAC9Ec,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,UAAU,CAACC,OAAO,cAAE1B,IAAA,CAACG,YAAY,EAAAE,QAAA,cAACL,IAAA,CAACb,OAAO,GAAE,CAAC,CAAc,CAAE,CAAE,CAAC,cAC5Ea,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE1B,IAAA,CAACZ,MAAM,GAAE,CAAE,CAAE,CAAC,cAC7CY,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE1B,IAAA,CAACX,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CW,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE1B,IAAA,CAACP,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9DO,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,cAAc,CAACC,OAAO,cAAE1B,IAAA,CAACT,UAAU,GAAE,CAAE,CAAE,CAAC,cACtDS,IAAA,CAACpB,KAAK,EAAC6C,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAE1B,IAAA,CAACR,iBAAiB,GAAE,CAAE,CAAE,CAAC,EAC/D,CAAC,CACE,CAAC,CACb,CAACuB,UAAU,eAAIf,IAAA,CAACH,OAAO,GAAE,CAAC,CAC1B,CAACkB,UAAU,EAAI,CAACE,aAAa,eAAIjB,IAAA,CAACL,MAAM,GAAE,CAAC,CAC3CoB,UAAU,eAAIf,IAAA,CAACJ,UAAU,GAAE,CAAC,EAC1B,CAAC,CAEV,CAAC,CAED,QAAS,CAAA+B,GAAGA,CAAA,CAAG,CACb,mBACEzB,KAAA,CAACxB,MAAM,EAAA2B,QAAA,eACLL,IAAA,CAACF,WAAW,GAAE,CAAC,cACfE,IAAA,CAACa,MAAM,GAAE,CAAC,EACJ,CAAC,CAEb,CAEA,cAAe,CAAAc,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}