{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Link}from'react-router-dom';import{auth}from'../firebase';import{onAuthStateChanged}from'firebase/auth';import{motion,useAnimation}from'framer-motion';import{useInView}from'react-intersection-observer';import{FaBitcoin,FaChartLine,FaShieldAlt,FaUserTie,FaArrowRight}from'react-icons/fa';import bitcoinHero from'../assets/images/bitcoin-icon.png';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{const[user,setUser]=useState(null);useEffect(()=>{const unsubscribe=onAuthStateChanged(auth,currentUser=>{if(currentUser){setUser(currentUser);}else{setUser(null);}});return()=>unsubscribe();},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"pt-16\",children:[user&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-lg text-white\",children:[\"Welcome back, \",/*#__PURE__*/_jsx(\"span\",{className:\"font-bold\",children:user.displayName?user.displayName.split(' ')[0]:'Trader'}),\"! \\uD83D\\uDC4B\",user.emailVerified?/*#__PURE__*/_jsx(\"span\",{className:\"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\",children:\"\\u2713 Email Verified\"}):/*#__PURE__*/_jsx(\"span\",{className:\"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\",children:\"Please verify your email\"})]})})})}),/*#__PURE__*/_jsx(HeroSection,{}),/*#__PURE__*/_jsx(InstagramCTA,{}),/*#__PURE__*/_jsx(CTASection,{}),/*#__PURE__*/_jsx(FeaturesSection,{}),/*#__PURE__*/_jsx(StatsSection,{}),/*#__PURE__*/_jsx(ServicesSection,{})]});};// Hero Section Component\nconst HeroSection=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"relative bg-primary-dark text-white overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 mb-10 md:mb-0\",children:[/*#__PURE__*/_jsx(motion.h1,{className:\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5},children:\"Your Trusted Bitcoin Exchange & Trading Partner\"}),/*#__PURE__*/_jsx(motion.p,{className:\"text-lg md:text-xl text-gray-300 mb-8\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5,delay:0.2},children:\"Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. We make Bitcoin trading accessible for everyone.\"}),/*#__PURE__*/_jsx(motion.div,{className:\"flex flex-wrap gap-4\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5,delay:0.4},children:/*#__PURE__*/_jsxs(\"a\",{href:\"https://wa.me/2348163309355\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"btn-primary inline-flex items-center text-lg px-8 py-3\",children:[\"Trade with Us \",/*#__PURE__*/_jsx(FaArrowRight,{className:\"ml-2\"})]})})]}),/*#__PURE__*/_jsx(motion.div,{className:\"md:w-1/2 flex justify-center\",initial:{opacity:0,scale:0.8},animate:{opacity:1,scale:1},transition:{duration:0.7},children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"}),/*#__PURE__*/_jsx(motion.div,{animate:{rotateY:[0,360]},transition:{duration:20,repeat:Infinity,ease:\"linear\"},className:\"relative z-10\",children:/*#__PURE__*/_jsx(\"img\",{src:bitcoinHero,alt:\"Bitcoin\",className:\"w-64 h-64 md:w-80 md:h-80\"})})]})})]})]});};// Features Section Component with Scroll Animation\nconst FeaturesSection=()=>{const features=[{icon:/*#__PURE__*/_jsx(FaBitcoin,{className:\"text-4xl text-yellow-500\"}),title:\"Secure Bitcoin Exchange\",description:\"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"},{icon:/*#__PURE__*/_jsx(FaChartLine,{className:\"text-4xl text-accent\"}),title:\"Advanced Trading Tools\",description:\"Access professional trading tools and real-time market data for informed decisions.\"},{icon:/*#__PURE__*/_jsx(FaShieldAlt,{className:\"text-4xl text-green-500\"}),title:\"Maximum Security\",description:\"Your assets are protected with industry-leading security measures and encryption.\"},{icon:/*#__PURE__*/_jsx(FaUserTie,{className:\"text-4xl text-purple-500\"}),title:\"Expert Support\",description:\"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"}];return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsx(SectionHeader,{title:\"Why Choose BlazeTrade\",subtitle:\"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\",children:features.map((feature,index)=>/*#__PURE__*/_jsx(FeatureCard,{icon:feature.icon,title:feature.title,description:feature.description,index:index},index))})]})});};// Services Section Component\nconst ServicesSection=()=>{const services=[{title:\"Bitcoin Exchange\",description:\"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",link:\"/services#bitcoin-exchange\"},{title:\"Crypto Trading\",description:\"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",link:\"/services#crypto-trading\"},{title:\"Market Analysis\",description:\"Get detailed market insights and analysis to make informed trading decisions.\",link:\"/services#market-analysis\"},{title:\"Portfolio Management\",description:\"Professional management of your cryptocurrency portfolio for optimal returns.\",link:\"/services#portfolio-management\"},{title:\"Security Solutions\",description:\"Advanced security solutions to protect your digital assets and investments.\",link:\"/services#security-solutions\"},{title:\"Consulting Services\",description:\"Expert consulting services for individuals and businesses entering the crypto space.\",link:\"/services#consulting\"}];return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container-custom\",children:[/*#__PURE__*/_jsx(SectionHeader,{title:\"Our Services\",subtitle:\"Comprehensive cryptocurrency services tailored to your needs\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\",children:services.map((service,index)=>/*#__PURE__*/_jsx(ServiceCard,{title:service.title,description:service.description,link:service.link,index:index},index))})]})});};// Stats Section Component\nconst StatsSection=()=>{const stats=[{value:\"10K+\",label:\"Active Users\"},{value:\"$250M+\",label:\"Monthly Volume\"},{value:\"99.9%\",label:\"Uptime\"},{value:\"24/7\",label:\"Support\"}];const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"py-16 bg-primary-dark text-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsx(motion.div,{ref:ref,className:\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.2}}},children:stats.map((stat,index)=>/*#__PURE__*/_jsxs(motion.div,{variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl md:text-5xl font-bold mb-2\",children:stat.value}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-300\",children:stat.label})]},index))})})});};// CTA Section Component\nconst CTASection=()=>{const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-gray-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4\",children:\"Ready to trade?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\",children:\"Hit the link below to start trading your cryptocurrencies with Blaze Trade.\"}),/*#__PURE__*/_jsxs(\"a\",{href:\"https://wa.me/2348163309355\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"inline-flex items-center btn-primary text-lg px-8 py-3\",children:[\"Trade Now \",/*#__PURE__*/_jsx(FaArrowRight,{className:\"ml-2\"})]})]})})});};// Reusable Section Header Component\nconst SectionHeader=_ref=>{let{title,subtitle}=_ref;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"text-center max-w-3xl mx-auto\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:subtitle})]});};// Feature Card Component with Animation\nconst FeatureCard=_ref2=>{let{icon,title,description,index}=_ref2;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:description})]});};// Service Card Component with Animation\nconst ServiceCard=_ref3=>{let{title,description,link,index}=_ref3;const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.1});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5,delay:index*0.1}}},children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-3 text-primary-dark\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:description}),/*#__PURE__*/_jsxs(Link,{to:link,className:\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\",children:[\"Learn More \",/*#__PURE__*/_jsx(FaArrowRight,{className:\"ml-2\"})]})]});};// Instagram CTA Section\nconst InstagramCTA=()=>{const controls=useAnimation();const[ref,inView]=useInView({triggerOnce:true,threshold:0.2});useEffect(()=>{if(inView){controls.start(\"visible\");}},[controls,inView]);return/*#__PURE__*/_jsx(\"section\",{className:\"section bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container-custom\",children:/*#__PURE__*/_jsxs(motion.div,{ref:ref,className:\"text-center max-w-3xl mx-auto\",initial:\"hidden\",animate:controls,variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.6}}},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",children:\"Follow Us on Instagram\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mb-8\",children:\"Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\"}),/*#__PURE__*/_jsx(\"a\",{href:\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"inline-flex items-center btn-secondary text-lg px-8 py-3\",children:\"@blaze__trade\"})]})})});};export default Home;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "auth", "onAuthStateChanged", "motion", "useAnimation", "useInView", "FaBitcoin", "FaChartLine", "FaShieldAlt", "FaUserTie", "FaArrowRight", "bitcoinHero", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "user", "setUser", "unsubscribe", "currentUser", "className", "children", "displayName", "split", "emailVerified", "HeroSection", "InstagramCTA", "CTASection", "FeaturesSection", "StatsSection", "ServicesSection", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "href", "target", "rel", "scale", "rotateY", "repeat", "Infinity", "ease", "src", "alt", "features", "icon", "title", "description", "SectionHeader", "subtitle", "map", "feature", "index", "FeatureCard", "services", "link", "service", "ServiceCard", "stats", "value", "label", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "stat", "_ref", "_ref2", "_ref3", "to"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Home.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { auth } from '../firebase';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaBitcoin, FaChartLine, FaShieldAlt, FaUserTie, FaArrowRight } from 'react-icons/fa';\nimport bitcoinHero from '../assets/images/bitcoin-icon.png';\n\nconst Home = () => {\n  const [user, setUser] = useState(null);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\n      if (currentUser) {\n        setUser(currentUser);\n      } else {\n        setUser(null);\n      }\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  return (\n    <div className=\"pt-16\">\n      {/* Welcome Message */}\n      {user && (\n        <div className=\"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\">\n          <div className=\"container-custom\">\n            <div className=\"flex items-center justify-center\">\n              <p className=\"text-lg text-white\">\n                Welcome back, <span className=\"font-bold\">\n                  {user.displayName ? user.displayName.split(' ')[0] : 'Trader'}\n                </span>! 👋\n                {user.emailVerified ? (\n                  <span className=\"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\">\n                    ✓ Email Verified\n                  </span>\n                ) : (\n                  <span className=\"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\">\n                    Please verify your email\n                  </span>\n                )}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {/* Hero Section */}\n      <HeroSection />\n\n      {/* Instagram CTA Section */}\n      <InstagramCTA />\n\n      {/* CTA Section */}\n      <CTASection />\n      \n      {/* Features Section */}\n      <FeaturesSection />\n      \n      {/* Stats Section */}\n      <StatsSection />\n\n      {/* Services Section */}\n      <ServicesSection />\n    </div>\n  );\n};\n\n// Hero Section Component\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-primary-dark text-white overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"></div>\n        <div className=\"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\">\n        {/* Hero Content */}\n        <div className=\"md:w-1/2 mb-10 md:mb-0\">\n          <motion.h1 \n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            Your Trusted Bitcoin Exchange & Trading Partner\n          </motion.h1>\n          <motion.p \n            className=\"text-lg md:text-xl text-gray-300 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. \n            We make Bitcoin trading accessible for everyone.\n          </motion.p>\n          <motion.div \n            className=\"flex flex-wrap gap-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            <a \n              href=\"https://wa.me/2348163309355\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"btn-primary inline-flex items-center text-lg px-8 py-3\"\n            >\n              Trade with Us <FaArrowRight className=\"ml-2\" />\n            </a>\n          </motion.div>\n        </div>\n        \n        {/* Hero Image */}\n        <motion.div \n          className=\"md:w-1/2 flex justify-center\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.7 }}\n        >\n          <div className=\"relative\">\n            <div className=\"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"></div>\n            <motion.div\n              animate={{ \n                rotateY: [0, 360],\n              }}\n              transition={{ \n                duration: 20,\n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              className=\"relative z-10\"\n            >\n              <img src={bitcoinHero} alt=\"Bitcoin\" className=\"w-64 h-64 md:w-80 md:h-80\" />\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Features Section Component with Scroll Animation\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: <FaBitcoin className=\"text-4xl text-yellow-500\" />,\n      title: \"Secure Bitcoin Exchange\",\n      description: \"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"\n    },\n    {\n      icon: <FaChartLine className=\"text-4xl text-accent\" />,\n      title: \"Advanced Trading Tools\",\n      description: \"Access professional trading tools and real-time market data for informed decisions.\"\n    },\n    {\n      icon: <FaShieldAlt className=\"text-4xl text-green-500\" />,\n      title: \"Maximum Security\",\n      description: \"Your assets are protected with industry-leading security measures and encryption.\"\n    },\n    {\n      icon: <FaUserTie className=\"text-4xl text-purple-500\" />,\n      title: \"Expert Support\",\n      description: \"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Why Choose BlazeTrade\" \n          subtitle=\"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\">\n          {features.map((feature, index) => (\n            <FeatureCard \n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Services Section Component\nconst ServicesSection = () => {\n  const services = [\n    {\n      title: \"Bitcoin Exchange\",\n      description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",\n      link: \"/services#bitcoin-exchange\"\n    },\n    {\n      title: \"Crypto Trading\",\n      description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",\n      link: \"/services#crypto-trading\"\n    },\n    {\n      title: \"Market Analysis\",\n      description: \"Get detailed market insights and analysis to make informed trading decisions.\",\n      link: \"/services#market-analysis\"\n    },\n    {\n      title: \"Portfolio Management\",\n      description: \"Professional management of your cryptocurrency portfolio for optimal returns.\",\n      link: \"/services#portfolio-management\"\n    },\n    {\n      title: \"Security Solutions\",\n      description: \"Advanced security solutions to protect your digital assets and investments.\",\n      link: \"/services#security-solutions\"\n    },\n    {\n      title: \"Consulting Services\",\n      description: \"Expert consulting services for individuals and businesses entering the crypto space.\",\n      link: \"/services#consulting\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Our Services\" \n          subtitle=\"Comprehensive cryptocurrency services tailored to your needs\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\">\n          {services.map((service, index) => (\n            <ServiceCard \n              key={index}\n              title={service.title}\n              description={service.description}\n              link={service.link}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Stats Section Component\nconst StatsSection = () => {\n  const stats = [\n    { value: \"10K+\", label: \"Active Users\" },\n    { value: \"$250M+\", label: \"Monthly Volume\" },\n    { value: \"99.9%\", label: \"Uptime\" },\n    { value: \"24/7\", label: \"Support\" }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"py-16 bg-primary-dark text-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.2\n              }\n            }\n          }}\n        >\n          {stats.map((stat, index) => (\n            <motion.div \n              key={index}\n              variants={{\n                hidden: { opacity: 0, y: 20 },\n                visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n              }}\n            >\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{stat.value}</div>\n              <div className=\"text-gray-300\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// CTA Section Component\nconst CTASection = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Ready to trade?</h2>\n          <p className=\"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Hit the link below to start trading your cryptocurrencies with Blaze Trade.\n          </p>\n          <a \n            href=\"https://wa.me/2348163309355\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-primary text-lg px-8 py-3\"\n          >\n            Trade Now <FaArrowRight className=\"ml-2\" />\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Reusable Section Header Component\nconst SectionHeader = ({ title, subtitle }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"text-center max-w-3xl mx-auto\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n      }}\n    >\n      <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">{title}</h2>\n      <p className=\"text-lg text-gray-600\">{subtitle}</p>\n    </motion.div>\n  );\n};\n\n// Feature Card Component with Animation\nconst FeatureCard = ({ icon, title, description, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"mb-4\">{icon}</div>\n      <h3 className=\"text-xl font-semibold mb-2 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600\">{description}</p>\n    </motion.div>\n  );\n};\n\n// Service Card Component with Animation\nconst ServiceCard = ({ title, description, link, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <h3 className=\"text-xl font-semibold mb-3 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600 mb-4\">{description}</p>\n      <Link \n        to={link} \n        className=\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\"\n      >\n        Learn More <FaArrowRight className=\"ml-2\" />\n      </Link>\n    </motion.div>\n  );\n};\n\n// Instagram CTA Section\nconst InstagramCTA = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"text-center max-w-3xl mx-auto\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Follow Us on Instagram</h2>\n          <p className=\"text-lg text-gray-600 mb-8\">\n            Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\n          </p>\n          <a \n            href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-secondary text-lg px-8 py-3\"\n          >\n            @blaze__trade\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Home;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,IAAI,KAAQ,aAAa,CAClC,OAASC,kBAAkB,KAAQ,eAAe,CAClD,OAASC,MAAM,CAAEC,YAAY,KAAQ,eAAe,CACpD,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OAASC,SAAS,CAAEC,WAAW,CAAEC,WAAW,CAAEC,SAAS,CAAEC,YAAY,KAAQ,gBAAgB,CAC7F,MAAO,CAAAC,WAAW,KAAM,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAEtCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqB,WAAW,CAAGjB,kBAAkB,CAACD,IAAI,CAAGmB,WAAW,EAAK,CAC5D,GAAIA,WAAW,CAAE,CACfF,OAAO,CAACE,WAAW,CAAC,CACtB,CAAC,IAAM,CACLF,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAC,CAAC,CAEF,MAAO,IAAMC,WAAW,CAAC,CAAC,CAC5B,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEJ,KAAA,QAAKM,SAAS,CAAC,OAAO,CAAAC,QAAA,EAEnBL,IAAI,eACHJ,IAAA,QAAKQ,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9ET,IAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BT,IAAA,QAAKQ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/CP,KAAA,MAAGM,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,gBAClB,cAAAT,IAAA,SAAMQ,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtCL,IAAI,CAACM,WAAW,CAAGN,IAAI,CAACM,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAG,QAAQ,CACzD,CAAC,iBACP,CAACP,IAAI,CAACQ,aAAa,cACjBZ,IAAA,SAAMQ,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,uBAErF,CAAM,CAAC,cAEPT,IAAA,SAAMQ,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,0BAEvF,CAAM,CACP,EACA,CAAC,CACD,CAAC,CACH,CAAC,CACH,CACN,cAGDT,IAAA,CAACa,WAAW,GAAE,CAAC,cAGfb,IAAA,CAACc,YAAY,GAAE,CAAC,cAGhBd,IAAA,CAACe,UAAU,GAAE,CAAC,cAGdf,IAAA,CAACgB,eAAe,GAAE,CAAC,cAGnBhB,IAAA,CAACiB,YAAY,GAAE,CAAC,cAGhBjB,IAAA,CAACkB,eAAe,GAAE,CAAC,EAChB,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAL,WAAW,CAAGA,CAAA,GAAM,CACxB,mBACEX,KAAA,YAASM,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAEtEP,KAAA,QAAKM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CT,IAAA,QAAKQ,SAAS,CAAC,wFAAwF,CAAM,CAAC,cAC9GR,IAAA,QAAKQ,SAAS,CAAC,iFAAiF,CAAM,CAAC,EACpG,CAAC,cAENN,KAAA,QAAKM,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eAEnGP,KAAA,QAAKM,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCT,IAAA,CAACV,MAAM,CAAC6B,EAAE,EACRX,SAAS,CAAC,iDAAiD,CAC3DY,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAhB,QAAA,CAC/B,iDAED,CAAW,CAAC,cACZT,IAAA,CAACV,MAAM,CAACoC,CAAC,EACPlB,SAAS,CAAC,uCAAuC,CACjDY,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEE,KAAK,CAAE,GAAI,CAAE,CAAAlB,QAAA,CAC3C,wIAGD,CAAU,CAAC,cACXT,IAAA,CAACV,MAAM,CAACsC,GAAG,EACTpB,SAAS,CAAC,sBAAsB,CAChCY,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEE,KAAK,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAE1CP,KAAA,MACE2B,IAAI,CAAC,6BAA6B,CAClCC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBvB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,EACnE,gBACe,cAAAT,IAAA,CAACH,YAAY,EAACW,SAAS,CAAC,MAAM,CAAE,CAAC,EAC9C,CAAC,CACM,CAAC,EACV,CAAC,cAGNR,IAAA,CAACV,MAAM,CAACsC,GAAG,EACTpB,SAAS,CAAC,8BAA8B,CACxCY,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEW,KAAK,CAAE,GAAI,CAAE,CACpCT,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEW,KAAK,CAAE,CAAE,CAAE,CAClCR,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAhB,QAAA,cAE9BP,KAAA,QAAKM,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBT,IAAA,QAAKQ,SAAS,CAAC,2IAA2I,CAAM,CAAC,cACjKR,IAAA,CAACV,MAAM,CAACsC,GAAG,EACTL,OAAO,CAAE,CACPU,OAAO,CAAE,CAAC,CAAC,CAAE,GAAG,CAClB,CAAE,CACFT,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZS,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,QACR,CAAE,CACF5B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAEzBT,IAAA,QAAKqC,GAAG,CAAEvC,WAAY,CAACwC,GAAG,CAAC,SAAS,CAAC9B,SAAS,CAAC,2BAA2B,CAAE,CAAC,CACnE,CAAC,EACV,CAAC,CACI,CAAC,EACV,CAAC,EACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAQ,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAuB,QAAQ,CAAG,CACf,CACEC,IAAI,cAAExC,IAAA,CAACP,SAAS,EAACe,SAAS,CAAC,0BAA0B,CAAE,CAAC,CACxDiC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CAAE,wFACf,CAAC,CACD,CACEF,IAAI,cAAExC,IAAA,CAACN,WAAW,EAACc,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACtDiC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,qFACf,CAAC,CACD,CACEF,IAAI,cAAExC,IAAA,CAACL,WAAW,EAACa,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACzDiC,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,mFACf,CAAC,CACD,CACEF,IAAI,cAAExC,IAAA,CAACJ,SAAS,EAACY,SAAS,CAAC,0BAA0B,CAAE,CAAC,CACxDiC,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,wFACf,CAAC,CACF,CAED,mBACE1C,IAAA,YAASQ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACrCP,KAAA,QAAKM,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BT,IAAA,CAAC2C,aAAa,EACZF,KAAK,CAAC,uBAAuB,CAC7BG,QAAQ,CAAC,6GAA6G,CACvH,CAAC,cAEF5C,IAAA,QAAKQ,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CACxE8B,QAAQ,CAACM,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3B/C,IAAA,CAACgD,WAAW,EAEVR,IAAI,CAAEM,OAAO,CAACN,IAAK,CACnBC,KAAK,CAAEK,OAAO,CAACL,KAAM,CACrBC,WAAW,CAAEI,OAAO,CAACJ,WAAY,CACjCK,KAAK,CAAEA,KAAM,EAJRA,KAKN,CACF,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAA7B,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAA+B,QAAQ,CAAG,CACf,CACER,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,sFAAsF,CACnGQ,IAAI,CAAE,4BACR,CAAC,CACD,CACET,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,0EAA0E,CACvFQ,IAAI,CAAE,0BACR,CAAC,CACD,CACET,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,+EAA+E,CAC5FQ,IAAI,CAAE,2BACR,CAAC,CACD,CACET,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CAAE,+EAA+E,CAC5FQ,IAAI,CAAE,gCACR,CAAC,CACD,CACET,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,6EAA6E,CAC1FQ,IAAI,CAAE,8BACR,CAAC,CACD,CACET,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CAAE,sFAAsF,CACnGQ,IAAI,CAAE,sBACR,CAAC,CACF,CAED,mBACElD,IAAA,YAASQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCP,KAAA,QAAKM,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BT,IAAA,CAAC2C,aAAa,EACZF,KAAK,CAAC,cAAc,CACpBG,QAAQ,CAAC,8DAA8D,CACxE,CAAC,cAEF5C,IAAA,QAAKQ,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CACxEwC,QAAQ,CAACJ,GAAG,CAAC,CAACM,OAAO,CAAEJ,KAAK,gBAC3B/C,IAAA,CAACoD,WAAW,EAEVX,KAAK,CAAEU,OAAO,CAACV,KAAM,CACrBC,WAAW,CAAES,OAAO,CAACT,WAAY,CACjCQ,IAAI,CAAEC,OAAO,CAACD,IAAK,CACnBH,KAAK,CAAEA,KAAM,EAJRA,KAKN,CACF,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAA9B,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAoC,KAAK,CAAG,CACZ,CAAEC,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,cAAe,CAAC,CACxC,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,gBAAiB,CAAC,CAC5C,CAAED,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,QAAS,CAAC,CACnC,CAAED,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CACpC,CAED,KAAM,CAAAC,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE1D,IAAA,YAASQ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cACnDT,IAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BT,IAAA,CAACV,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,mDAAmD,CAC7DY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAE,CAAC,CACtB2C,OAAO,CAAE,CACP3C,OAAO,CAAE,CAAC,CACVG,UAAU,CAAE,CACVyC,eAAe,CAAE,GACnB,CACF,CACF,CAAE,CAAAxD,QAAA,CAED4C,KAAK,CAACR,GAAG,CAAC,CAACqB,IAAI,CAAEnB,KAAK,gBACrB7C,KAAA,CAACZ,MAAM,CAACsC,GAAG,EAETkC,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CAAE3C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAhB,QAAA,eAEFT,IAAA,QAAKQ,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEyD,IAAI,CAACZ,KAAK,CAAM,CAAC,cACvEtD,IAAA,QAAKQ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEyD,IAAI,CAACX,KAAK,CAAM,CAAC,GAP5CR,KAQK,CACb,CAAC,CACQ,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAhC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAyC,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE1D,IAAA,YAASQ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACrCT,IAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BP,KAAA,CAACZ,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,gEAAgE,CAC1EY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CAAE3C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAhB,QAAA,eAEFT,IAAA,OAAIQ,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxET,IAAA,MAAGQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6EAE5D,CAAG,CAAC,cACJP,KAAA,MACE2B,IAAI,CAAC,6BAA6B,CAClCC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBvB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,EACnE,YACW,cAAAT,IAAA,CAACH,YAAY,EAACW,SAAS,CAAC,MAAM,CAAE,CAAC,EAC1C,CAAC,EACM,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED;AACA,KAAM,CAAAmC,aAAa,CAAGwB,IAAA,EAAyB,IAAxB,CAAE1B,KAAK,CAAEG,QAAS,CAAC,CAAAuB,IAAA,CACxC,KAAM,CAAAX,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACExD,KAAA,CAACZ,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,+BAA+B,CACzCY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CAAE3C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAhB,QAAA,eAEFT,IAAA,OAAIQ,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAEgC,KAAK,CAAK,CAAC,cAClFzC,IAAA,MAAGQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEmC,QAAQ,CAAI,CAAC,EACzC,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAI,WAAW,CAAGoB,KAAA,EAAyC,IAAxC,CAAE5B,IAAI,CAAEC,KAAK,CAAEC,WAAW,CAAEK,KAAM,CAAC,CAAAqB,KAAA,CACtD,KAAM,CAAAZ,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACExD,KAAA,CAACZ,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,kFAAkF,CAC5FY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CACP3C,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbE,KAAK,CAAEoB,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAAtC,QAAA,eAEFT,IAAA,QAAKQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE+B,IAAI,CAAM,CAAC,cAClCxC,IAAA,OAAIQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEgC,KAAK,CAAK,CAAC,cACzEzC,IAAA,MAAGQ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEiC,WAAW,CAAI,CAAC,EACpC,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAU,WAAW,CAAGiB,KAAA,EAAyC,IAAxC,CAAE5B,KAAK,CAAEC,WAAW,CAAEQ,IAAI,CAAEH,KAAM,CAAC,CAAAsB,KAAA,CACtD,KAAM,CAAAb,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACExD,KAAA,CAACZ,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,iGAAiG,CAC3GY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CACP3C,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbE,KAAK,CAAEoB,KAAK,CAAG,GACjB,CACF,CACF,CAAE,CAAAtC,QAAA,eAEFT,IAAA,OAAIQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEgC,KAAK,CAAK,CAAC,cACzEzC,IAAA,MAAGQ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEiC,WAAW,CAAI,CAAC,cACnDxC,KAAA,CAACf,IAAI,EACHmF,EAAE,CAAEpB,IAAK,CACT1C,SAAS,CAAC,kGAAkG,CAAAC,QAAA,EAC7G,aACY,cAAAT,IAAA,CAACH,YAAY,EAACW,SAAS,CAAC,MAAM,CAAE,CAAC,EACxC,CAAC,EACG,CAAC,CAEjB,CAAC,CAED;AACA,KAAM,CAAAM,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA0C,QAAQ,CAAGjE,YAAY,CAAC,CAAC,CAC/B,KAAM,CAACkE,GAAG,CAAEC,MAAM,CAAC,CAAGlE,SAAS,CAAC,CAC9BmE,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GACb,CAAC,CAAC,CAEF3E,SAAS,CAAC,IAAM,CACd,GAAIyE,MAAM,CAAE,CACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC,CAC3B,CACF,CAAC,CAAE,CAACL,QAAQ,CAAEE,MAAM,CAAC,CAAC,CAEtB,mBACE1D,IAAA,YAASQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCT,IAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BP,KAAA,CAACZ,MAAM,CAACsC,GAAG,EACT6B,GAAG,CAAEA,GAAI,CACTjD,SAAS,CAAC,+BAA+B,CACzCY,OAAO,CAAC,QAAQ,CAChBG,OAAO,CAAEiC,QAAS,CAClBM,QAAQ,CAAE,CACRC,MAAM,CAAE,CAAE1C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAC,CAC7B0C,OAAO,CAAE,CAAE3C,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7D,CAAE,CAAAhB,QAAA,eAEFT,IAAA,OAAIQ,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cACjGT,IAAA,MAAGQ,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,0GAE1C,CAAG,CAAC,cACJT,IAAA,MACE6B,IAAI,CAAC,sDAAsD,CAC3DC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBvB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CACrE,eAED,CAAG,CAAC,EACM,CAAC,CACV,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}