{"ast": null, "code": "'use strict';\n\n/**\n * Dependencies\n */\nconst {\n  Client\n} = require('@sendgrid/client');\nconst {\n  classes: {\n    Mail\n  }\n} = require('@sendgrid/helpers');\n\n/**\n * Mail service class\n */\nclass MailService {\n  /**\n   * Constructor\n   */\n  constructor() {\n    // Set client, initialize substitution wrappers and secret rules filter.\n    this.setClient(new Client());\n    this.setSubstitutionWrappers('{{', '}}');\n    this.secretRules = [];\n  }\n\n  /**\n   * Set client\n   */\n  setClient(client) {\n    this.client = client;\n    return this;\n  }\n\n  /**\n   * SendGrid API key passthrough for convenience.\n   */\n  setApiKey(apiKey) {\n    this.client.setApiKey(apiKey);\n    return this;\n  }\n\n  /**\n   * Twilio Email Auth passthrough for convenience.\n   */\n  setTwilioEmailAuth(username, password) {\n    this.client.setTwilioEmailAuth(username, password);\n  }\n\n  /**\n   * Set client timeout\n   */\n  setTimeout(timeout) {\n    if (typeof timeout === 'undefined') {\n      return;\n    }\n    this.client.setDefaultRequest('timeout', timeout);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(left, right) {\n    if (typeof left === 'undefined' || typeof right === 'undefined') {\n      throw new Error('Must provide both left and right side wrappers');\n    }\n    if (!Array.isArray(this.substitutionWrappers)) {\n      this.substitutionWrappers = [];\n    }\n    this.substitutionWrappers[0] = left;\n    this.substitutionWrappers[1] = right;\n    return this;\n  }\n\n  /**\n   * Set secret rules for filtering the e-mail content\n   */\n  setSecretRules(rules) {\n    if (!(rules instanceof Array)) {\n      rules = [rules];\n    }\n    const tmpRules = rules.map(function (rule) {\n      const ruleType = typeof rule;\n      if (ruleType === 'string') {\n        return {\n          pattern: new RegExp(rule)\n        };\n      } else if (ruleType === 'object') {\n        // normalize rule object\n        if (rule instanceof RegExp) {\n          rule = {\n            pattern: rule\n          };\n        } else if (rule.hasOwnProperty('pattern') && typeof rule.pattern === 'string') {\n          rule.pattern = new RegExp(rule.pattern);\n        }\n        try {\n          // test if rule.pattern is a valid regex\n          rule.pattern.test('');\n          return rule;\n        } catch (err) {\n          // continue regardless of error\n        }\n      }\n    });\n    this.secretRules = tmpRules.filter(function (val) {\n      return val;\n    });\n  }\n\n  /**\n   * Check if the e-mail is safe to be sent\n   */\n  filterSecrets(body) {\n    if (typeof body === 'object' && !body.hasOwnProperty('content')) {\n      return;\n    }\n    const self = this;\n    body.content.forEach(function (data) {\n      self.secretRules.forEach(function (rule) {\n        if (rule.hasOwnProperty('pattern') && !rule.pattern.test(data.value)) {\n          return;\n        }\n        let message = `The pattern '${rule.pattern}'`;\n        if (rule.name) {\n          message += `identified by '${rule.name}'`;\n        }\n        message += ' was found in the Mail content!';\n        throw new Error(message);\n      });\n    });\n  }\n\n  /**\n   * Send email\n   */\n  send(data) {\n    let isMultiple = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let cb = arguments.length > 2 ? arguments[2] : undefined;\n    //Callback as second parameter\n    if (typeof isMultiple === 'function') {\n      cb = isMultiple;\n      isMultiple = false;\n    }\n\n    //Array? Send in parallel\n    if (Array.isArray(data)) {\n      //Create promise\n      const promise = Promise.all(data.map(item => {\n        return this.send(item, isMultiple);\n      }));\n\n      //Execute callback if provided\n      if (cb) {\n        promise.then(result => cb(null, result)).catch(error => cb(error, null));\n      }\n\n      //Return promise\n      return promise;\n    }\n\n    //Send mail\n    try {\n      //Append multiple flag to data if not set\n      if (typeof data.isMultiple === 'undefined') {\n        data.isMultiple = isMultiple;\n      }\n\n      //Append global substitution wrappers if not set in data\n      if (typeof data.substitutionWrappers === 'undefined') {\n        data.substitutionWrappers = this.substitutionWrappers;\n      }\n\n      //Create Mail instance from data and get JSON body for request\n      const mail = Mail.create(data);\n      const body = mail.toJSON();\n\n      //Filters the Mail body to avoid sensitive content leakage\n      this.filterSecrets(body);\n\n      //Create request\n      const request = {\n        method: 'POST',\n        url: '/v3/mail/send',\n        headers: mail.headers,\n        body\n      };\n\n      //Send\n      return this.client.request(request, cb);\n    } catch (error) {\n      //Pass to callback if provided\n      if (cb) {\n        // eslint-disable-next-line callback-return\n        cb(error, null);\n      }\n\n      //Reject promise\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Send multiple emails (shortcut)\n   */\n  sendMultiple(data, cb) {\n    return this.send(data, true, cb);\n  }\n}\n\n//Export class\nmodule.exports = MailService;", "map": {"version": 3, "names": ["Client", "require", "classes", "Mail", "MailService", "constructor", "setClient", "setSubstitutionWrappers", "secretRules", "client", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setTwilioEmailAuth", "username", "password", "setTimeout", "timeout", "setDefaultRequest", "left", "right", "Error", "Array", "isArray", "substitutionWrappers", "setSecretRules", "rules", "tmpRules", "map", "rule", "ruleType", "pattern", "RegExp", "hasOwnProperty", "test", "err", "filter", "val", "filterSecrets", "body", "self", "content", "for<PERSON>ach", "data", "value", "message", "name", "send", "isMultiple", "arguments", "length", "undefined", "cb", "promise", "Promise", "all", "item", "then", "result", "catch", "error", "mail", "create", "toJSON", "request", "method", "url", "headers", "reject", "sendMultiple", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/node_modules/@sendgrid/mail/src/classes/mail-service.js"], "sourcesContent": ["'use strict';\n\n/**\n * Dependencies\n */\nconst {Client} = require('@sendgrid/client');\nconst {classes: {Mail}} = require('@sendgrid/helpers');\n\n/**\n * Mail service class\n */\nclass MailService {\n\n  /**\n   * Constructor\n   */\n  constructor() {\n\n    // Set client, initialize substitution wrappers and secret rules filter.\n    this.setClient(new Client());\n    this.setSubstitutionWrappers('{{', '}}');\n    this.secretRules = [];\n  }\n\n  /**\n   * Set client\n   */\n  setClient(client) {\n    this.client = client;\n\n    return this;\n  }\n\n  /**\n   * SendGrid API key passthrough for convenience.\n   */\n  setApiKey(apiKey) {\n    this.client.setApiKey(apiKey);\n\n    return this;\n  }\n\n  /**\n   * Twilio Email Auth passthrough for convenience.\n   */\n  setTwilioEmailAuth(username, password) {\n    this.client.setTwilioEmailAuth(username, password);\n  }\n\n  /**\n   * Set client timeout\n   */\n  setTimeout(timeout) {\n    if (typeof timeout === 'undefined') {\n      return;\n    }\n\n    this.client.setDefaultRequest('timeout', timeout);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(left, right) {\n    if (typeof left === 'undefined' || typeof right === 'undefined') {\n      throw new Error('Must provide both left and right side wrappers');\n    }\n    if (!Array.isArray(this.substitutionWrappers)) {\n      this.substitutionWrappers = [];\n    }\n    this.substitutionWrappers[0] = left;\n    this.substitutionWrappers[1] = right;\n\n    return this;\n  }\n\n  /**\n   * Set secret rules for filtering the e-mail content\n   */\n  setSecretRules(rules) {\n    if (!(rules instanceof Array)) {\n      rules = [rules];\n    }\n\n    const tmpRules = rules.map(function (rule) {\n      const ruleType = typeof rule;\n\n      if (ruleType === 'string') {\n        return {\n          pattern: new RegExp(rule),\n        };\n      } else if (ruleType === 'object') {\n        // normalize rule object\n        if (rule instanceof RegExp) {\n          rule = {\n            pattern: rule,\n          };\n        } else if (rule.hasOwnProperty('pattern')\n          && (typeof rule.pattern === 'string')\n        ) {\n          rule.pattern = new RegExp(rule.pattern);\n        }\n\n        try {\n          // test if rule.pattern is a valid regex\n          rule.pattern.test('');\n          return rule;\n        } catch (err) {\n          // continue regardless of error\n        }\n      }\n    });\n\n    this.secretRules = tmpRules.filter(function (val) {\n      return val;\n    });\n  }\n\n  /**\n   * Check if the e-mail is safe to be sent\n   */\n  filterSecrets(body) {\n    if ((typeof body === 'object') && !body.hasOwnProperty('content')) {\n      return;\n    }\n\n    const self = this;\n\n    body.content.forEach(function (data) {\n      self.secretRules.forEach(function (rule) {\n        if (rule.hasOwnProperty('pattern')\n          && !rule.pattern.test(data.value)\n        ) {\n          return;\n        }\n\n        let message = `The pattern '${rule.pattern}'`;\n\n        if (rule.name) {\n          message += `identified by '${rule.name}'`;\n        }\n\n        message += ' was found in the Mail content!';\n\n        throw new Error(message);\n      });\n    });\n  }\n\n  /**\n   * Send email\n   */\n  send(data, isMultiple = false, cb) {\n\n    //Callback as second parameter\n    if (typeof isMultiple === 'function') {\n      cb = isMultiple;\n      isMultiple = false;\n    }\n\n    //Array? Send in parallel\n    if (Array.isArray(data)) {\n\n      //Create promise\n      const promise = Promise.all(data.map(item => {\n        return this.send(item, isMultiple);\n      }));\n\n      //Execute callback if provided\n      if (cb) {\n        promise\n          .then(result => cb(null, result))\n          .catch(error => cb(error, null));\n      }\n\n      //Return promise\n      return promise;\n    }\n\n    //Send mail\n    try {\n\n      //Append multiple flag to data if not set\n      if (typeof data.isMultiple === 'undefined') {\n        data.isMultiple = isMultiple;\n      }\n\n      //Append global substitution wrappers if not set in data\n      if (typeof data.substitutionWrappers === 'undefined') {\n        data.substitutionWrappers = this.substitutionWrappers;\n      }\n\n      //Create Mail instance from data and get JSON body for request\n      const mail = Mail.create(data);\n      const body = mail.toJSON();\n\n      //Filters the Mail body to avoid sensitive content leakage\n      this.filterSecrets(body);\n\n      //Create request\n      const request = {\n        method: 'POST',\n        url: '/v3/mail/send',\n        headers: mail.headers,\n        body,\n      };\n\n      //Send\n      return this.client.request(request, cb);\n    } catch (error) {\n\n      //Pass to callback if provided\n      if (cb) {\n        // eslint-disable-next-line callback-return\n        cb(error, null);\n      }\n\n      //Reject promise\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Send multiple emails (shortcut)\n   */\n  sendMultiple(data, cb) {\n    return this.send(data, true, cb);\n  }\n}\n\n//Export class\nmodule.exports = MailService;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,MAAM;EAACA;AAAM,CAAC,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC5C,MAAM;EAACC,OAAO,EAAE;IAACC;EAAI;AAAC,CAAC,GAAGF,OAAO,CAAC,mBAAmB,CAAC;;AAEtD;AACA;AACA;AACA,MAAMG,WAAW,CAAC;EAEhB;AACF;AACA;EACEC,WAAWA,CAAA,EAAG;IAEZ;IACA,IAAI,CAACC,SAAS,CAAC,IAAIN,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACO,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC;IACxC,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;;EAEA;AACF;AACA;EACEF,SAASA,CAACG,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACEC,SAASA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACF,MAAM,CAACC,SAAS,CAACC,MAAM,CAAC;IAE7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACEC,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACL,MAAM,CAACG,kBAAkB,CAACC,QAAQ,EAAEC,QAAQ,CAAC;EACpD;;EAEA;AACF;AACA;EACEC,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClC;IACF;IAEA,IAAI,CAACP,MAAM,CAACQ,iBAAiB,CAAC,SAAS,EAAED,OAAO,CAAC;EACnD;;EAEA;AACF;AACA;EACET,uBAAuBA,CAACW,IAAI,EAAEC,KAAK,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;MAC/D,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,oBAAoB,CAAC,EAAE;MAC7C,IAAI,CAACA,oBAAoB,GAAG,EAAE;IAChC;IACA,IAAI,CAACA,oBAAoB,CAAC,CAAC,CAAC,GAAGL,IAAI;IACnC,IAAI,CAACK,oBAAoB,CAAC,CAAC,CAAC,GAAGJ,KAAK;IAEpC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACEK,cAAcA,CAACC,KAAK,EAAE;IACpB,IAAI,EAAEA,KAAK,YAAYJ,KAAK,CAAC,EAAE;MAC7BI,KAAK,GAAG,CAACA,KAAK,CAAC;IACjB;IAEA,MAAMC,QAAQ,GAAGD,KAAK,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;MACzC,MAAMC,QAAQ,GAAG,OAAOD,IAAI;MAE5B,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO;UACLC,OAAO,EAAE,IAAIC,MAAM,CAACH,IAAI;QAC1B,CAAC;MACH,CAAC,MAAM,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QAChC;QACA,IAAID,IAAI,YAAYG,MAAM,EAAE;UAC1BH,IAAI,GAAG;YACLE,OAAO,EAAEF;UACX,CAAC;QACH,CAAC,MAAM,IAAIA,IAAI,CAACI,cAAc,CAAC,SAAS,CAAC,IACnC,OAAOJ,IAAI,CAACE,OAAO,KAAK,QAAS,EACrC;UACAF,IAAI,CAACE,OAAO,GAAG,IAAIC,MAAM,CAACH,IAAI,CAACE,OAAO,CAAC;QACzC;QAEA,IAAI;UACF;UACAF,IAAI,CAACE,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC;UACrB,OAAOL,IAAI;QACb,CAAC,CAAC,OAAOM,GAAG,EAAE;UACZ;QAAA;MAEJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAC1B,WAAW,GAAGkB,QAAQ,CAACS,MAAM,CAAC,UAAUC,GAAG,EAAE;MAChD,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEC,aAAaA,CAACC,IAAI,EAAE;IAClB,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAK,CAACA,IAAI,CAACN,cAAc,CAAC,SAAS,CAAC,EAAE;MACjE;IACF;IAEA,MAAMO,IAAI,GAAG,IAAI;IAEjBD,IAAI,CAACE,OAAO,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACnCH,IAAI,CAAC/B,WAAW,CAACiC,OAAO,CAAC,UAAUb,IAAI,EAAE;QACvC,IAAIA,IAAI,CAACI,cAAc,CAAC,SAAS,CAAC,IAC7B,CAACJ,IAAI,CAACE,OAAO,CAACG,IAAI,CAACS,IAAI,CAACC,KAAK,CAAC,EACjC;UACA;QACF;QAEA,IAAIC,OAAO,GAAG,gBAAgBhB,IAAI,CAACE,OAAO,GAAG;QAE7C,IAAIF,IAAI,CAACiB,IAAI,EAAE;UACbD,OAAO,IAAI,kBAAkBhB,IAAI,CAACiB,IAAI,GAAG;QAC3C;QAEAD,OAAO,IAAI,iCAAiC;QAE5C,MAAM,IAAIxB,KAAK,CAACwB,OAAO,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEE,IAAIA,CAACJ,IAAI,EAA0B;IAAA,IAAxBK,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAAA,IAAEG,EAAE,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAE/B;IACA,IAAI,OAAOH,UAAU,KAAK,UAAU,EAAE;MACpCI,EAAE,GAAGJ,UAAU;MACfA,UAAU,GAAG,KAAK;IACpB;;IAEA;IACA,IAAI1B,KAAK,CAACC,OAAO,CAACoB,IAAI,CAAC,EAAE;MAEvB;MACA,MAAMU,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACZ,IAAI,CAACf,GAAG,CAAC4B,IAAI,IAAI;QAC3C,OAAO,IAAI,CAACT,IAAI,CAACS,IAAI,EAAER,UAAU,CAAC;MACpC,CAAC,CAAC,CAAC;;MAEH;MACA,IAAII,EAAE,EAAE;QACNC,OAAO,CACJI,IAAI,CAACC,MAAM,IAAIN,EAAE,CAAC,IAAI,EAAEM,MAAM,CAAC,CAAC,CAChCC,KAAK,CAACC,KAAK,IAAIR,EAAE,CAACQ,KAAK,EAAE,IAAI,CAAC,CAAC;MACpC;;MAEA;MACA,OAAOP,OAAO;IAChB;;IAEA;IACA,IAAI;MAEF;MACA,IAAI,OAAOV,IAAI,CAACK,UAAU,KAAK,WAAW,EAAE;QAC1CL,IAAI,CAACK,UAAU,GAAGA,UAAU;MAC9B;;MAEA;MACA,IAAI,OAAOL,IAAI,CAACnB,oBAAoB,KAAK,WAAW,EAAE;QACpDmB,IAAI,CAACnB,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;MACvD;;MAEA;MACA,MAAMqC,IAAI,GAAGzD,IAAI,CAAC0D,MAAM,CAACnB,IAAI,CAAC;MAC9B,MAAMJ,IAAI,GAAGsB,IAAI,CAACE,MAAM,CAAC,CAAC;;MAE1B;MACA,IAAI,CAACzB,aAAa,CAACC,IAAI,CAAC;;MAExB;MACA,MAAMyB,OAAO,GAAG;QACdC,MAAM,EAAE,MAAM;QACdC,GAAG,EAAE,eAAe;QACpBC,OAAO,EAAEN,IAAI,CAACM,OAAO;QACrB5B;MACF,CAAC;;MAED;MACA,OAAO,IAAI,CAAC7B,MAAM,CAACsD,OAAO,CAACA,OAAO,EAAEZ,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAEd;MACA,IAAIR,EAAE,EAAE;QACN;QACAA,EAAE,CAACQ,KAAK,EAAE,IAAI,CAAC;MACjB;;MAEA;MACA,OAAON,OAAO,CAACc,MAAM,CAACR,KAAK,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;EACES,YAAYA,CAAC1B,IAAI,EAAES,EAAE,EAAE;IACrB,OAAO,IAAI,CAACL,IAAI,CAACJ,IAAI,EAAE,IAAI,EAAES,EAAE,CAAC;EAClC;AACF;;AAEA;AACAkB,MAAM,CAACC,OAAO,GAAGlE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}