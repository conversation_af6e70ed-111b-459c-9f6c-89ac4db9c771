{"ast": null, "code": "// Using the browser-compatible version of SendGrid\nconst SENDGRID_API_URL = 'https://api.sendgrid.com/v3/mail/send';\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  const data = {\n    personalizations: [{\n      to: [{\n        email\n      }],\n      dynamic_template_data: {\n        name: displayName || 'Trader',\n        verificationLink: verificationLink,\n        appName: 'BlazeTrade'\n      }\n    }],\n    from: {\n      email: '<EMAIL>'\n    },\n    template_id: 'd-c3eb32a8c66b4d47beff2b9c9513af62'\n  };\n  try {\n    const response = await fetch(SENDGRID_API_URL, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.REACT_APP_SENDGRID_API_KEY}`\n      },\n      body: JSON.stringify(data)\n    });\n    if (!response.ok) {\n      var _error$errors, _error$errors$;\n      const error = await response.json();\n      throw new Error(((_error$errors = error.errors) === null || _error$errors === void 0 ? void 0 : (_error$errors$ = _error$errors[0]) === null || _error$errors$ === void 0 ? void 0 : _error$errors$.message) || 'Failed to send verification email');\n    }\n    console.log('Verification email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  const data = {\n    personalizations: [{\n      to: [{\n        email\n      }],\n      dynamic_template_data: {\n        name: displayName || 'Trader',\n        loginTime: new Date().toLocaleString(),\n        appName: 'BlazeTrade'\n      }\n    }],\n    from: {\n      email: '<EMAIL>'\n    },\n    template_id: 'd-8c1e56e48c05424faf4a25dd9cf637b2'\n  };\n  try {\n    const response = await fetch(SENDGRID_API_URL, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.REACT_APP_SENDGRID_API_KEY}`\n      },\n      body: JSON.stringify(data)\n    });\n    if (!response.ok) {\n      var _error$errors2, _error$errors2$;\n      const error = await response.json();\n      throw new Error(((_error$errors2 = error.errors) === null || _error$errors2 === void 0 ? void 0 : (_error$errors2$ = _error$errors2[0]) === null || _error$errors2$ === void 0 ? void 0 : _error$errors2$.message) || 'Failed to send welcome email');\n    }\n    console.log('Welcome email sent to', email);\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["SENDGRID_API_URL", "sendVerificationEmail", "email", "displayName", "verificationLink", "data", "personalizations", "to", "dynamic_template_data", "name", "appName", "from", "template_id", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_SENDGRID_API_KEY", "body", "JSON", "stringify", "ok", "_error$errors", "_error$errors$", "error", "json", "Error", "errors", "message", "console", "log", "success", "sendWelcomeEmail", "loginTime", "Date", "toLocaleString", "_error$errors2", "_error$errors2$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/utils/emailService.js"], "sourcesContent": ["// Using the browser-compatible version of SendGrid\nconst SENDGRID_API_URL = 'https://api.sendgrid.com/v3/mail/send';\n\n// Function to send verification email\nexport const sendVerificationEmail = async (email, displayName, verificationLink) => {\n  const data = {\n    personalizations: [{\n      to: [{ email }],\n      dynamic_template_data: {\n        name: displayName || 'Trader',\n        verificationLink: verificationLink,\n        appName: 'BlazeTrade'\n      }\n    }],\n    from: { email: '<EMAIL>' },\n    template_id: 'd-c3eb32a8c66b4d47beff2b9c9513af62'\n  };\n\n  try {\n    const response = await fetch(SENDGRID_API_URL, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.REACT_APP_SENDGRID_API_KEY}`\n      },\n      body: JSON.stringify(data)\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.errors?.[0]?.message || 'Failed to send verification email');\n    }\n\n    console.log('Verification email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending verification email:', error);\n    throw error;\n  }\n};\n\n// Function to send welcome email\nexport const sendWelcomeEmail = async (email, displayName) => {\n  const data = {\n    personalizations: [{\n      to: [{ email }],\n      dynamic_template_data: {\n        name: displayName || 'Trader',\n        loginTime: new Date().toLocaleString(),\n        appName: 'BlazeTrade'\n      }\n    }],\n    from: { email: '<EMAIL>' },\n    template_id: 'd-8c1e56e48c05424faf4a25dd9cf637b2'\n  };\n\n  try {\n    const response = await fetch(SENDGRID_API_URL, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.REACT_APP_SENDGRID_API_KEY}`\n      },\n      body: JSON.stringify(data)\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.errors?.[0]?.message || 'Failed to send welcome email');\n    }\n\n    console.log('Welcome email sent to', email);\n    return { success: true };\n  } catch (error) {\n    console.error('Error sending welcome email:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA;AACA,MAAMA,gBAAgB,GAAG,uCAAuC;;AAEhE;AACA,OAAO,MAAMC,qBAAqB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,WAAW,EAAEC,gBAAgB,KAAK;EACnF,MAAMC,IAAI,GAAG;IACXC,gBAAgB,EAAE,CAAC;MACjBC,EAAE,EAAE,CAAC;QAAEL;MAAM,CAAC,CAAC;MACfM,qBAAqB,EAAE;QACrBC,IAAI,EAAEN,WAAW,IAAI,QAAQ;QAC7BC,gBAAgB,EAAEA,gBAAgB;QAClCM,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACFC,IAAI,EAAE;MAAET,KAAK,EAAE;IAA+B,CAAC;IAC/CU,WAAW,EAAE;EACf,CAAC;EAED,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACd,gBAAgB,EAAE;MAC7Ce,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,0BAA0B;MACnE,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI;IAC3B,CAAC,CAAC;IAEF,IAAI,CAACQ,QAAQ,CAACU,EAAE,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA;MAChB,MAAMC,KAAK,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;MACnC,MAAM,IAAIC,KAAK,CAAC,EAAAJ,aAAA,GAAAE,KAAK,CAACG,MAAM,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmBK,OAAO,KAAI,mCAAmC,CAAC;IACpF;IAEAC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE9B,KAAK,CAAC;IAChD,OAAO;MAAE+B,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,gBAAgB,GAAG,MAAAA,CAAOhC,KAAK,EAAEC,WAAW,KAAK;EAC5D,MAAME,IAAI,GAAG;IACXC,gBAAgB,EAAE,CAAC;MACjBC,EAAE,EAAE,CAAC;QAAEL;MAAM,CAAC,CAAC;MACfM,qBAAqB,EAAE;QACrBC,IAAI,EAAEN,WAAW,IAAI,QAAQ;QAC7BgC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QACtC3B,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACFC,IAAI,EAAE;MAAET,KAAK,EAAE;IAA+B,CAAC;IAC/CU,WAAW,EAAE;EACf,CAAC;EAED,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACd,gBAAgB,EAAE;MAC7Ce,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,0BAA0B;MACnE,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI;IAC3B,CAAC,CAAC;IAEF,IAAI,CAACQ,QAAQ,CAACU,EAAE,EAAE;MAAA,IAAAe,cAAA,EAAAC,eAAA;MAChB,MAAMb,KAAK,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;MACnC,MAAM,IAAIC,KAAK,CAAC,EAAAU,cAAA,GAAAZ,KAAK,CAACG,MAAM,cAAAS,cAAA,wBAAAC,eAAA,GAAZD,cAAA,CAAe,CAAC,CAAC,cAAAC,eAAA,uBAAjBA,eAAA,CAAmBT,OAAO,KAAI,8BAA8B,CAAC;IAC/E;IAEAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE9B,KAAK,CAAC;IAC3C,OAAO;MAAE+B,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}