{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Services from './pages/Services';\nimport Contact from './pages/Contact';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children\n}) => {\n  const token = localStorage.getItem('token');\n  return token ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 29\n  }, this);\n};\n_c = PrivateRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n        className: \"flex-grow\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/about\",\n            element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/services\",\n            element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/contact\",\n            element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/signup\",\n            element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chatbot, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"PrivateRoute\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "motion", "Home", "About", "Services", "Contact", "Signup", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Footer", "<PERSON><PERSON><PERSON>", "ScrollToTop", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "token", "localStorage", "getItem", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "className", "main", "initial", "opacity", "animate", "transition", "duration", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Pages\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Services from './pages/Services';\nimport Contact from './pages/Contact';\nimport Signup from './pages/Signup';\nimport Login from './pages/Login';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport Chatbot from './components/Chatbot';\nimport ScrollToTop from './components/ScrollToTop';\n\nconst PrivateRoute = ({ children }) => {\n  const token = localStorage.getItem('token');\n  return token ? children : <Navigate to=\"/login\" />;\n};\n\nfunction App() {\n  return (\n    <Router>\n      <ScrollToTop />\n      <div className=\"flex flex-col min-h-screen\">\n        <Navbar />\n        <motion.main \n          className=\"flex-grow\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          <Routes>\n            <Route path=\"/\" element={<PrivateRoute><Home /></PrivateRoute>} />\n            <Route path=\"/about\" element={<About />} />\n            <Route path=\"/services\" element={<Services />} />\n            <Route path=\"/contact\" element={<Contact />} />\n            <Route path=\"/signup\" element={<Signup />} />\n            <Route path=\"/login\" element={<Login />} />\n          </Routes>\n        </motion.main>\n        <Chatbot />\n        <Footer />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;;AAEjC;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,GAAGD,QAAQ,gBAAGF,OAAA,CAACb,QAAQ;IAACmB,EAAE,EAAC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpD,CAAC;AAACC,EAAA,GAHIV,YAAY;AAKlB,SAASW,GAAGA,CAAA,EAAG;EACb,oBACEZ,OAAA,CAAChB,MAAM;IAAAkB,QAAA,gBACLF,OAAA,CAACF,WAAW;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfV,OAAA;MAAKa,SAAS,EAAC,4BAA4B;MAAAX,QAAA,gBACzCF,OAAA,CAACL,MAAM;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVV,OAAA,CAACZ,MAAM,CAAC0B,IAAI;QACVD,SAAS,EAAC,WAAW;QACrBE,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAjB,QAAA,eAE9BF,OAAA,CAACf,MAAM;UAAAiB,QAAA,gBACLF,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAErB,OAAA,CAACC,YAAY;cAAAC,QAAA,eAACF,OAAA,CAACX,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClEV,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAErB,OAAA,CAACV,KAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CV,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,WAAW;YAACC,OAAO,eAAErB,OAAA,CAACT,QAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDV,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,UAAU;YAACC,OAAO,eAAErB,OAAA,CAACR,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CV,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,SAAS;YAACC,OAAO,eAAErB,OAAA,CAACP,MAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CV,OAAA,CAACd,KAAK;YAACkC,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAErB,OAAA,CAACN,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdV,OAAA,CAACH,OAAO;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXV,OAAA,CAACJ,MAAM;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACY,GAAA,GA1BQV,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}