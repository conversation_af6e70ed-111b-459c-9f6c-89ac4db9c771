{"ast": null, "code": "import React,{useState}from'react';import Navbar from'../components/Navbar';import{useForm}from'react-hook-form';import{signInWithEmailAndPassword}from'firebase/auth';import{auth}from'../firebase';import{Link,useNavigate}from'react-router-dom';import{sendWelcomeEmail}from'../utils/emailService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const{register,handleSubmit,formState:{errors}}=useForm();const navigate=useNavigate();const[serverError,setServerError]=useState('');const onSubmit=async data=>{setServerError('');// Clear previous errors\ntry{// Sign in with email and password using Firebase\nconst userCredential=await signInWithEmailAndPassword(auth,data.username,data.password);const user=userCredential.user;// Check if email is verified\nif(!user.emailVerified){// Sign out the user\nawait auth.signOut();// Show error message to verify email first\nsetServerError('Please verify your email before logging in. Check your inbox for the verification email.');return;}// Store the user's ID token in localStorage for session management\nconst token=await user.getIdToken();localStorage.setItem('token',token);// Send welcome email\ntry{await sendWelcomeEmail(user.email,user.displayName||'Trader');}catch(emailError){console.error('Error sending welcome email:',emailError);// Don't block login if email fails\n}// Navigate to the dashboard on successful login\nnavigate('/dashboard');}catch(error){let friendlyMessage='An unexpected error occurred. Please try again.';// Provide user-friendly error messages\nswitch(error.code){case'auth/user-not-found':case'auth/wrong-password':friendlyMessage='Incorrect username or password. Please try again.';break;case'auth/too-many-requests':friendlyMessage='Too many failed login attempts. Please try again later or reset your password.';break;case'auth/user-disabled':friendlyMessage='This account has been disabled. Please contact support.';break;default:console.error('Firebase Login Error:',error);}setServerError(friendlyMessage);}};const renderForm=()=>{return/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit(onSubmit),className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",...register('username',{required:'Email is required',pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,message:'Please enter a valid email address'}}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Enter your email\"}),errors.username&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.username.message})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-400\",children:\"Password\"}),/*#__PURE__*/_jsx(Link,{to:\"/forgot-password\",className:\"text-sm text-blue-400 hover:text-blue-300 hover:underline\",children:\"Forgot Password?\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",...register('password',{required:'Password is required'}),className:\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\",placeholder:\"Enter password\"}),errors.password&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-red-500\",children:errors.password.message})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\",children:\"Log In\"})})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-900 text-white flex flex-col\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold mb-4\",children:\"Log In\"}),serverError&&/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\",children:serverError}),renderForm(),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-6 text-sm text-center text-gray-400\",children:[\"Don't have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/signup\",className:\"font-medium text-blue-400 hover:text-blue-300\",children:\"Sign up\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex flex-col items-center justify-center\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/blazetrade-logo.png\",alt:\"BlazeTrade Logo\",className:\"w-48 h-48\"})})]})})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "useForm", "signInWithEmailAndPassword", "auth", "Link", "useNavigate", "sendWelcomeEmail", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "register", "handleSubmit", "formState", "errors", "navigate", "serverError", "setServerError", "onSubmit", "data", "userCredential", "username", "password", "user", "emailVerified", "signOut", "token", "getIdToken", "localStorage", "setItem", "email", "displayName", "emailError", "console", "error", "friendlyMessage", "code", "renderForm", "className", "children", "type", "required", "pattern", "value", "message", "placeholder", "to", "src", "alt"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Navbar from '../components/Navbar';\nimport { useForm } from 'react-hook-form';\nimport { signInWithEmailAndPassword } from 'firebase/auth';\nimport { auth } from '../firebase';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { sendWelcomeEmail } from '../utils/emailService';\n\nconst Login = () => {\n  const { register, handleSubmit, formState: { errors } } = useForm();\n  const navigate = useNavigate();\n  const [serverError, setServerError] = useState('');\n\n  const onSubmit = async (data) => {\n    setServerError(''); // Clear previous errors\n    try {\n      // Sign in with email and password using Firebase\n      const userCredential = await signInWithEmailAndPassword(auth, data.username, data.password);\n      const user = userCredential.user;\n      \n      // Check if email is verified\n      if (!user.emailVerified) {\n        // Sign out the user\n        await auth.signOut();\n        \n        // Show error message to verify email first\n        setServerError('Please verify your email before logging in. Check your inbox for the verification email.');\n        return;\n      }\n      \n      // Store the user's ID token in localStorage for session management\n      const token = await user.getIdToken();\n      localStorage.setItem('token', token);\n      \n      // Send welcome email\n      try {\n        await sendWelcomeEmail(user.email, user.displayName || 'Trader');\n      } catch (emailError) {\n        console.error('Error sending welcome email:', emailError);\n        // Don't block login if email fails\n      }\n      \n      // Navigate to the dashboard on successful login\n      navigate('/dashboard');\n    } catch (error) {\n      let friendlyMessage = 'An unexpected error occurred. Please try again.';\n      \n      // Provide user-friendly error messages\n      switch (error.code) {\n        case 'auth/user-not-found':\n        case 'auth/wrong-password':\n          friendlyMessage = 'Incorrect username or password. Please try again.';\n          break;\n        case 'auth/too-many-requests':\n          friendlyMessage = 'Too many failed login attempts. Please try again later or reset your password.';\n          break;\n        case 'auth/user-disabled':\n          friendlyMessage = 'This account has been disabled. Please contact support.';\n          break;\n        default:\n          console.error('Firebase Login Error:', error);\n      }\n      \n      setServerError(friendlyMessage);\n    }\n  };\n\n  const renderForm = () => {\n    return (\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-400\">Email</label>\n          <input\n            type=\"email\"\n            {...register('username', { \n              required: 'Email is required',\n              pattern: {\n                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                message: 'Please enter a valid email address'\n              }\n            })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter your email\"\n          />\n          {errors.username && <p className=\"mt-2 text-sm text-red-500\">{errors.username.message}</p>}\n        </div>\n        <div>\n          <div className=\"flex justify-between items-center\">\n            <label className=\"block text-sm font-medium text-gray-400\">Password</label>\n            <Link to=\"/forgot-password\" className=\"text-sm text-blue-400 hover:text-blue-300 hover:underline\">\n              Forgot Password?\n            </Link>\n          </div>\n          <input\n            type=\"password\"\n            {...register('password', { required: 'Password is required' })}\n            className=\"w-full px-4 py-3 mt-1 bg-blue-900 border border-blue-800 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 text-white\"\n            placeholder=\"Enter password\"\n          />\n          {errors.password && <p className=\"mt-2 text-sm text-red-500\">{errors.password.message}</p>}\n        </div>\n        <div>\n          <button\n            type=\"submit\"\n            className=\"w-full px-4 py-3 font-bold text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-blue-950 transition duration-300\"\n          >\n            Log In\n          </button>\n        </div>\n      </form>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white flex flex-col\">\n      <Navbar />\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-6xl mx-auto grid md:grid-cols-2 gap-16 items-center\">\n          \n          {/* Form Section */}\n          <div className=\"w-full max-w-md\">\n            <h1 className=\"text-4xl font-bold mb-4\">Log In</h1>\n            {serverError && <p className=\"mb-4 text-sm text-center text-red-500 bg-red-500/10 p-3 rounded-lg\">{serverError}</p>}\n            {renderForm()}\n            <p className=\"mt-6 text-sm text-center text-gray-400\">\n              Don't have an account?{' '}\n              <Link to=\"/signup\" className=\"font-medium text-blue-400 hover:text-blue-300\">\n                Sign up\n              </Link>\n            </p>\n          </div>\n\n          {/* Logo Section */}\n          <div className=\"hidden md:flex flex-col items-center justify-center\">\n              <img src=\"/blazetrade-logo.png\" alt=\"BlazeTrade Logo\" className=\"w-48 h-48\"/>\n          </div>\n        </div>\n\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,0BAA0B,KAAQ,eAAe,CAC1D,OAASC,IAAI,KAAQ,aAAa,CAClC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,gBAAgB,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,SAAS,CAAE,CAAEC,MAAO,CAAE,CAAC,CAAGd,OAAO,CAAC,CAAC,CACnE,KAAM,CAAAe,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACY,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAAoB,QAAQ,CAAG,KAAO,CAAAC,IAAI,EAAK,CAC/BF,cAAc,CAAC,EAAE,CAAC,CAAE;AACpB,GAAI,CACF;AACA,KAAM,CAAAG,cAAc,CAAG,KAAM,CAAAnB,0BAA0B,CAACC,IAAI,CAAEiB,IAAI,CAACE,QAAQ,CAAEF,IAAI,CAACG,QAAQ,CAAC,CAC3F,KAAM,CAAAC,IAAI,CAAGH,cAAc,CAACG,IAAI,CAEhC;AACA,GAAI,CAACA,IAAI,CAACC,aAAa,CAAE,CACvB;AACA,KAAM,CAAAtB,IAAI,CAACuB,OAAO,CAAC,CAAC,CAEpB;AACAR,cAAc,CAAC,0FAA0F,CAAC,CAC1G,OACF,CAEA;AACA,KAAM,CAAAS,KAAK,CAAG,KAAM,CAAAH,IAAI,CAACI,UAAU,CAAC,CAAC,CACrCC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAEH,KAAK,CAAC,CAEpC;AACA,GAAI,CACF,KAAM,CAAArB,gBAAgB,CAACkB,IAAI,CAACO,KAAK,CAAEP,IAAI,CAACQ,WAAW,EAAI,QAAQ,CAAC,CAClE,CAAE,MAAOC,UAAU,CAAE,CACnBC,OAAO,CAACC,KAAK,CAAC,8BAA8B,CAAEF,UAAU,CAAC,CACzD;AACF,CAEA;AACAjB,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOmB,KAAK,CAAE,CACd,GAAI,CAAAC,eAAe,CAAG,iDAAiD,CAEvE;AACA,OAAQD,KAAK,CAACE,IAAI,EAChB,IAAK,qBAAqB,CAC1B,IAAK,qBAAqB,CACxBD,eAAe,CAAG,mDAAmD,CACrE,MACF,IAAK,wBAAwB,CAC3BA,eAAe,CAAG,gFAAgF,CAClG,MACF,IAAK,oBAAoB,CACvBA,eAAe,CAAG,yDAAyD,CAC3E,MACF,QACEF,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CACjD,CAEAjB,cAAc,CAACkB,eAAe,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACE5B,KAAA,SAAMS,QAAQ,CAAEN,YAAY,CAACM,QAAQ,CAAE,CAACoB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC3D9B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,UAAO+B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEhC,IAAA,UACEiC,IAAI,CAAC,OAAO,IACR7B,QAAQ,CAAC,UAAU,CAAE,CACvB8B,QAAQ,CAAE,mBAAmB,CAC7BC,OAAO,CAAE,CACPC,KAAK,CAAE,0CAA0C,CACjDC,OAAO,CAAE,oCACX,CACF,CAAC,CAAC,CACFN,SAAS,CAAC,oIAAoI,CAC9IO,WAAW,CAAC,kBAAkB,CAC/B,CAAC,CACD/B,MAAM,CAACO,QAAQ,eAAId,IAAA,MAAG+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEzB,MAAM,CAACO,QAAQ,CAACuB,OAAO,CAAI,CAAC,EACvF,CAAC,cACNnC,KAAA,QAAA8B,QAAA,eACE9B,KAAA,QAAK6B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhC,IAAA,UAAO+B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EhC,IAAA,CAACJ,IAAI,EAAC2C,EAAE,CAAC,kBAAkB,CAACR,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,kBAElG,CAAM,CAAC,EACJ,CAAC,cACNhC,IAAA,UACEiC,IAAI,CAAC,UAAU,IACX7B,QAAQ,CAAC,UAAU,CAAE,CAAE8B,QAAQ,CAAE,sBAAuB,CAAC,CAAC,CAC9DH,SAAS,CAAC,oIAAoI,CAC9IO,WAAW,CAAC,gBAAgB,CAC7B,CAAC,CACD/B,MAAM,CAACQ,QAAQ,eAAIf,IAAA,MAAG+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEzB,MAAM,CAACQ,QAAQ,CAACsB,OAAO,CAAI,CAAC,EACvF,CAAC,cACNrC,IAAA,QAAAgC,QAAA,cACEhC,IAAA,WACEiC,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,2MAA2M,CAAAC,QAAA,CACtN,QAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,CAEX,CAAC,CAED,mBACE9B,KAAA,QAAK6B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhC,IAAA,CAACR,MAAM,GAAE,CAAC,cACVQ,IAAA,QAAK+B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D9B,KAAA,QAAK6B,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAG/E9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhC,IAAA,OAAI+B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,CAClDvB,WAAW,eAAIT,IAAA,MAAG+B,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAEvB,WAAW,CAAI,CAAC,CAClHqB,UAAU,CAAC,CAAC,cACb5B,KAAA,MAAG6B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,wBAC9B,CAAC,GAAG,cAC1BhC,IAAA,CAACJ,IAAI,EAAC2C,EAAE,CAAC,SAAS,CAACR,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,SAE7E,CAAM,CAAC,EACN,CAAC,EACD,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAChEhC,IAAA,QAAKwC,GAAG,CAAC,sBAAsB,CAACC,GAAG,CAAC,iBAAiB,CAACV,SAAS,CAAC,WAAW,CAAC,CAAC,CAC5E,CAAC,EACH,CAAC,CAEH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}