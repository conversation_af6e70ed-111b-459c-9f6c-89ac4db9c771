{"ast": null, "code": "import { useRef, useEffect } from 'react';\nimport { useInstantLayoutTransition } from '../projection/use-instant-layout-transition.mjs';\nimport { useForceUpdate } from './use-force-update.mjs';\nimport { instantAnimationState } from './use-instant-transition-state.mjs';\nimport { frame } from '../frameloop/frame.mjs';\nfunction useInstantTransition() {\n  const [forceUpdate, forcedRenderCount] = useForceUpdate();\n  const startInstantLayoutTransition = useInstantLayoutTransition();\n  const unlockOnFrameRef = useRef();\n  useEffect(() => {\n    /**\n     * Unblock after two animation frames, otherwise this will unblock too soon.\n     */\n    frame.postRender(() => frame.postRender(() => {\n      /**\n       * If the callback has been called again after the effect\n       * triggered this 2 frame delay, don't unblock animations. This\n       * prevents the previous effect from unblocking the current\n       * instant transition too soon. This becomes more likely when\n       * used in conjunction with React.startTransition().\n       */\n      if (forcedRenderCount !== unlockOnFrameRef.current) return;\n      instantAnimationState.current = false;\n    }));\n  }, [forcedRenderCount]);\n  return callback => {\n    startInstantLayoutTransition(() => {\n      instantAnimationState.current = true;\n      forceUpdate();\n      callback();\n      unlockOnFrameRef.current = forcedRenderCount + 1;\n    });\n  };\n}\nfunction disableInstantTransitions() {\n  instantAnimationState.current = false;\n}\nexport { disableInstantTransitions, useInstantTransition };", "map": {"version": 3, "names": ["useRef", "useEffect", "useInstantLayoutTransition", "useForceUpdate", "instantAnimationState", "frame", "useInstantTransition", "forceUpdate", "forcedRenderCount", "startInstantLayoutTransition", "unlockOnFrameRef", "postRender", "current", "callback", "disableInstantTransitions"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/utils/use-instant-transition.mjs"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nimport { useInstantLayoutTransition } from '../projection/use-instant-layout-transition.mjs';\nimport { useForceUpdate } from './use-force-update.mjs';\nimport { instantAnimationState } from './use-instant-transition-state.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction useInstantTransition() {\n    const [forceUpdate, forcedRenderCount] = useForceUpdate();\n    const startInstantLayoutTransition = useInstantLayoutTransition();\n    const unlockOnFrameRef = useRef();\n    useEffect(() => {\n        /**\n         * Unblock after two animation frames, otherwise this will unblock too soon.\n         */\n        frame.postRender(() => frame.postRender(() => {\n            /**\n             * If the callback has been called again after the effect\n             * triggered this 2 frame delay, don't unblock animations. This\n             * prevents the previous effect from unblocking the current\n             * instant transition too soon. This becomes more likely when\n             * used in conjunction with React.startTransition().\n             */\n            if (forcedRenderCount !== unlockOnFrameRef.current)\n                return;\n            instantAnimationState.current = false;\n        }));\n    }, [forcedRenderCount]);\n    return (callback) => {\n        startInstantLayoutTransition(() => {\n            instantAnimationState.current = true;\n            forceUpdate();\n            callback();\n            unlockOnFrameRef.current = forcedRenderCount + 1;\n        });\n    };\n}\nfunction disableInstantTransitions() {\n    instantAnimationState.current = false;\n}\n\nexport { disableInstantTransitions, useInstantTransition };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,0BAA0B,QAAQ,iDAAiD;AAC5F,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,KAAK,QAAQ,wBAAwB;AAE9C,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAM,CAACC,WAAW,EAAEC,iBAAiB,CAAC,GAAGL,cAAc,CAAC,CAAC;EACzD,MAAMM,4BAA4B,GAAGP,0BAA0B,CAAC,CAAC;EACjE,MAAMQ,gBAAgB,GAAGV,MAAM,CAAC,CAAC;EACjCC,SAAS,CAAC,MAAM;IACZ;AACR;AACA;IACQI,KAAK,CAACM,UAAU,CAAC,MAAMN,KAAK,CAACM,UAAU,CAAC,MAAM;MAC1C;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIH,iBAAiB,KAAKE,gBAAgB,CAACE,OAAO,EAC9C;MACJR,qBAAqB,CAACQ,OAAO,GAAG,KAAK;IACzC,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,CAACJ,iBAAiB,CAAC,CAAC;EACvB,OAAQK,QAAQ,IAAK;IACjBJ,4BAA4B,CAAC,MAAM;MAC/BL,qBAAqB,CAACQ,OAAO,GAAG,IAAI;MACpCL,WAAW,CAAC,CAAC;MACbM,QAAQ,CAAC,CAAC;MACVH,gBAAgB,CAACE,OAAO,GAAGJ,iBAAiB,GAAG,CAAC;IACpD,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASM,yBAAyBA,CAAA,EAAG;EACjCV,qBAAqB,CAACQ,OAAO,GAAG,KAAK;AACzC;AAEA,SAASE,yBAAyB,EAAER,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}