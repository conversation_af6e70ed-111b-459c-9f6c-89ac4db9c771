{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { auth } from '../firebase';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaBitcoin, FaChartLine, FaShieldAlt, FaUserTie, FaArrowRight } from 'react-icons/fa';\nimport bitcoinHero from '../assets/images/bitcoin-icon.png';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, currentUser => {\n      if (currentUser) {\n        setUser(currentUser);\n      } else {\n        setUser(null);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pt-16\",\n    children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-custom\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-white\",\n            children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold\",\n              children: user.displayName ? user.displayName.split(' ')[0] : 'Trader'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 31\n            }, this), \"! \\uD83D\\uDC4B\", user.emailVerified ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\",\n              children: \"\\u2713 Email Verified\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\",\n              children: \"Please verify your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/dashboard\",\n            className: \"text-sm bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors\",\n            children: \"Go to Dashboard \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {\n      user: user\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InstagramCTA, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CTASection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatsSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ServicesSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n\n// Hero Section Component\n_s(Home, \"5s2qRsV95gTJBmaaTh11GoxYeGE=\");\n_c = Home;\nconst HeroSection = ({\n  user\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative bg-primary-dark text-white overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:w-1/2 mb-10 md:mb-0\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\"Welcome back,\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400\",\n              children: user.displayName ? user.displayName.split(' ')[0] : 'Trader'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"! \\uD83D\\uDC4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : 'Your Trusted Bitcoin Exchange & Trading Partner'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          className: \"text-lg md:text-xl text-gray-300 mb-8\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          children: user ? 'Track your portfolio, discover new opportunities, and trade with confidence on our secure platform.' : 'Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. We make Bitcoin trading accessible for everyone.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"flex flex-wrap gap-4\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.4\n          },\n          children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/dashboard\",\n              className: \"btn-primary inline-flex items-center text-lg px-8 py-3\",\n              children: [\"Go to Dashboard \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                className: \"ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://wa.me/2348163309355\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\",\n              children: \"Get Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"btn-primary inline-flex items-center text-lg px-8 py-3\",\n              children: [\"Get Started \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                className: \"ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 31\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://wa.me/2348163309355\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"md:w-1/2 flex justify-center\",\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.7\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotateY: [0, 360]\n            },\n            transition: {\n              duration: 20,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"relative z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: bitcoinHero,\n              alt: \"Bitcoin\",\n              className: \"w-64 h-64 md:w-80 md:h-80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n\n// Features Section Component with Scroll Animation\n_c2 = HeroSection;\nconst FeaturesSection = () => {\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(FaBitcoin, {\n      className: \"text-4xl text-yellow-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this),\n    title: \"Secure Bitcoin Exchange\",\n    description: \"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {\n      className: \"text-4xl text-accent\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }, this),\n    title: \"Advanced Trading Tools\",\n    description: \"Access professional trading tools and real-time market data for informed decisions.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n      className: \"text-4xl text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this),\n    title: \"Maximum Security\",\n    description: \"Your assets are protected with industry-leading security measures and encryption.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaUserTie, {\n      className: \"text-4xl text-purple-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 13\n    }, this),\n    title: \"Expert Support\",\n    description: \"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        title: \"Why Choose BlazeTrade\",\n        subtitle: \"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCard, {\n          icon: feature.icon,\n          title: feature.title,\n          description: feature.description,\n          index: index\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n};\n\n// Services Section Component\n_c3 = FeaturesSection;\nconst ServicesSection = () => {\n  const services = [{\n    title: \"Bitcoin Exchange\",\n    description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",\n    link: \"/services#bitcoin-exchange\"\n  }, {\n    title: \"Crypto Trading\",\n    description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",\n    link: \"/services#crypto-trading\"\n  }, {\n    title: \"Market Analysis\",\n    description: \"Get detailed market insights and analysis to make informed trading decisions.\",\n    link: \"/services#market-analysis\"\n  }, {\n    title: \"Portfolio Management\",\n    description: \"Professional management of your cryptocurrency portfolio for optimal returns.\",\n    link: \"/services#portfolio-management\"\n  }, {\n    title: \"Security Solutions\",\n    description: \"Advanced security solutions to protect your digital assets and investments.\",\n    link: \"/services#security-solutions\"\n  }, {\n    title: \"Consulting Services\",\n    description: \"Expert consulting services for individuals and businesses entering the crypto space.\",\n    link: \"/services#consulting\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        title: \"Our Services\",\n        subtitle: \"Comprehensive cryptocurrency services tailored to your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(ServiceCard, {\n          title: service.title,\n          description: service.description,\n          link: service.link,\n          index: index\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n\n// Stats Section Component\n_c4 = ServicesSection;\nconst StatsSection = () => {\n  _s2();\n  const stats = [{\n    value: \"10K+\",\n    label: \"Active Users\"\n  }, {\n    value: \"$250M+\",\n    label: \"Monthly Volume\"\n  }, {\n    value: \"99.9%\",\n    label: \"Uptime\"\n  }, {\n    value: \"24/7\",\n    label: \"Support\"\n  }];\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-16 bg-primary-dark text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0\n          },\n          visible: {\n            opacity: 1,\n            transition: {\n              staggerChildren: 0.2\n            }\n          }\n        },\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: {\n            hidden: {\n              opacity: 0,\n              y: 20\n            },\n            visible: {\n              opacity: 1,\n              y: 0,\n              transition: {\n                duration: 0.6\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl md:text-5xl font-bold mb-2\",\n            children: stat.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300\",\n            children: stat.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n};\n\n// CTA Section Component\n_s2(StatsSection, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c5 = StatsSection;\nconst CTASection = () => {\n  _s3();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0,\n            y: 20\n          },\n          visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n              duration: 0.6\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4\",\n          children: \"Ready to trade?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\",\n          children: \"Hit the link below to start trading your cryptocurrencies with Blaze Trade.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://wa.me/2348163309355\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center btn-primary text-lg px-8 py-3\",\n          children: [\"Trade Now \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 374,\n    columnNumber: 5\n  }, this);\n};\n\n// Reusable Section Header Component\n_s3(CTASection, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c6 = CTASection;\nconst SectionHeader = ({\n  title,\n  subtitle\n}) => {\n  _s4();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"text-center max-w-3xl mx-auto\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.6\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-lg text-gray-600\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 419,\n    columnNumber: 5\n  }, this);\n};\n\n// Feature Card Component with Animation\n_s4(SectionHeader, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c7 = SectionHeader;\nconst FeatureCard = ({\n  icon,\n  title,\n  description,\n  index\n}) => {\n  _s5();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-semibold mb-2 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 450,\n    columnNumber: 5\n  }, this);\n};\n\n// Service Card Component with Animation\n_s5(FeatureCard, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c8 = FeatureCard;\nconst ServiceCard = ({\n  title,\n  description,\n  link,\n  index\n}) => {\n  _s6();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    ref: ref,\n    className: \"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\",\n    initial: \"hidden\",\n    animate: controls,\n    variants: {\n      hidden: {\n        opacity: 0,\n        y: 20\n      },\n      visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-semibold mb-3 text-primary-dark\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 mb-4\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: link,\n      className: \"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\",\n      children: [\"Learn More \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n        className: \"ml-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 20\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 489,\n    columnNumber: 5\n  }, this);\n};\n\n// Instagram CTA Section\n_s6(ServiceCard, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c9 = ServiceCard;\nconst InstagramCTA = () => {\n  _s7();\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        className: \"text-center max-w-3xl mx-auto\",\n        initial: \"hidden\",\n        animate: controls,\n        variants: {\n          hidden: {\n            opacity: 0,\n            y: 20\n          },\n          visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n              duration: 0.6\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\",\n          children: \"Follow Us on Instagram\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-8\",\n          children: \"Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center btn-secondary text-lg px-8 py-3\",\n          children: \"@blaze__trade\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 533,\n    columnNumber: 5\n  }, this);\n};\n_s7(InstagramCTA, \"2NEUvrxzw09dl1eC0Q0pbm0pOIE=\", false, function () {\n  return [useAnimation, useInView];\n});\n_c0 = InstagramCTA;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"Home\");\n$RefreshReg$(_c2, \"HeroSection\");\n$RefreshReg$(_c3, \"FeaturesSection\");\n$RefreshReg$(_c4, \"ServicesSection\");\n$RefreshReg$(_c5, \"StatsSection\");\n$RefreshReg$(_c6, \"CTASection\");\n$RefreshReg$(_c7, \"SectionHeader\");\n$RefreshReg$(_c8, \"FeatureCard\");\n$RefreshReg$(_c9, \"ServiceCard\");\n$RefreshReg$(_c0, \"InstagramCTA\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "auth", "onAuthStateChanged", "motion", "useAnimation", "useInView", "FaBitcoin", "FaChartLine", "FaShieldAlt", "FaUserTie", "FaArrowRight", "bitcoinHero", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "user", "setUser", "unsubscribe", "currentUser", "className", "children", "displayName", "split", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "emailVerified", "to", "HeroSection", "InstagramCTA", "CTASection", "FeaturesSection", "StatsSection", "ServicesSection", "_c", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "href", "target", "rel", "scale", "rotateY", "repeat", "Infinity", "ease", "src", "alt", "_c2", "features", "icon", "title", "description", "SectionHeader", "subtitle", "map", "feature", "index", "FeatureCard", "_c3", "services", "link", "service", "ServiceCard", "_c4", "_s2", "stats", "value", "label", "controls", "ref", "inView", "triggerOnce", "threshold", "start", "variants", "hidden", "visible", "stagger<PERSON><PERSON><PERSON><PERSON>", "stat", "_c5", "_s3", "_c6", "_s4", "_c7", "_s5", "_c8", "_s6", "_c9", "_s7", "_c0", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/Home.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { auth } from '../firebase';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { motion, useAnimation } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FaBitcoin, FaChartLine, FaShieldAlt, FaUserTie, FaArrowRight } from 'react-icons/fa';\nimport bitcoinHero from '../assets/images/bitcoin-icon.png';\n\nconst Home = () => {\n  const [user, setUser] = useState(null);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\n      if (currentUser) {\n        setUser(currentUser);\n      } else {\n        setUser(null);\n      }\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  return (\n    <div className=\"pt-16\">\n      {/* Welcome Message */}\n      {user && (\n        <div className=\"bg-gradient-to-r from-blue-900/80 to-blue-800/80 py-4 px-6 mb-8\">\n          <div className=\"container-custom\">\n            <div className=\"flex items-center justify-between\">\n              <p className=\"text-lg text-white\">\n                Welcome back, <span className=\"font-bold\">\n                  {user.displayName ? user.displayName.split(' ')[0] : 'Trader'}\n                </span>! 👋\n                {user.emailVerified ? (\n                  <span className=\"ml-3 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full\">\n                    ✓ Email Verified\n                  </span>\n                ) : (\n                  <span className=\"ml-3 px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full\">\n                    Please verify your email\n                  </span>\n                )}\n              </p>\n              <Link \n                to=\"/dashboard\" \n                className=\"text-sm bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors\"\n              >\n                Go to Dashboard →\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {/* Hero Section */}\n      <HeroSection user={user} />\n\n      {/* Instagram CTA Section */}\n      <InstagramCTA />\n\n      {/* CTA Section */}\n      <CTASection />\n      \n      {/* Features Section */}\n      <FeaturesSection />\n      \n      {/* Stats Section */}\n      <StatsSection />\n\n      {/* Services Section */}\n      <ServicesSection />\n    </div>\n  );\n};\n\n// Hero Section Component\nconst HeroSection = ({ user }) => {\n  return (\n    <section className=\"relative bg-primary-dark text-white overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -right-10 -top-10 w-64 h-64 bg-primary-light rounded-full opacity-20 blur-3xl\"></div>\n        <div className=\"absolute left-1/4 bottom-0 w-96 h-96 bg-accent rounded-full opacity-10 blur-3xl\"></div>\n      </div>\n      \n      <div className=\"container-custom relative z-10 py-20 md:py-32 flex flex-col md:flex-row items-center\">\n        {/* Hero Content */}\n        <div className=\"md:w-1/2 mb-10 md:mb-0\">\n          <motion.h1 \n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            {user ? (\n              <>\n                Welcome back,{' '}\n                <span className=\"text-blue-400\">\n                  {user.displayName ? user.displayName.split(' ')[0] : 'Trader'}\n                </span>\n                <span className=\"text-2xl\">! 👋</span>\n              </>\n            ) : (\n              'Your Trusted Bitcoin Exchange & Trading Partner'\n            )}\n          </motion.h1>\n          <motion.p \n            className=\"text-lg md:text-xl text-gray-300 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            {user ? (\n              'Track your portfolio, discover new opportunities, and trade with confidence on our secure platform.'\n            ) : (\n              'Experience secure, professional, and reliable cryptocurrency trading with BlazeTrade. We make Bitcoin trading accessible for everyone.'\n            )}\n          </motion.p>\n          <motion.div \n            className=\"flex flex-wrap gap-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            {user ? (\n              <>\n                <Link \n                  to=\"/dashboard\"\n                  className=\"btn-primary inline-flex items-center text-lg px-8 py-3\"\n                >\n                  Go to Dashboard <FaArrowRight className=\"ml-2\" />\n                </Link>\n                <a \n                  href=\"https://wa.me/2348163309355\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\"\n                >\n                  Get Support\n                </a>\n              </>\n            ) : (\n              <>\n                <Link \n                  to=\"/signup\"\n                  className=\"btn-primary inline-flex items-center text-lg px-8 py-3\"\n                >\n                  Get Started <FaArrowRight className=\"ml-2\" />\n                </Link>\n                <a \n                  href=\"https://wa.me/2348163309355\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"bg-white/10 hover:bg-white/20 text-white inline-flex items-center text-lg px-8 py-3 rounded-lg transition-colors\"\n                >\n                  Contact Us\n                </a>\n              </>\n            )}\n          </motion.div>\n        </div>\n        \n        {/* Hero Image */}\n        <motion.div \n          className=\"md:w-1/2 flex justify-center\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.7 }}\n        >\n          <div className=\"relative\">\n            <div className=\"w-64 h-64 md:w-80 md:h-80 bg-accent rounded-full opacity-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 blur-xl\"></div>\n            <motion.div\n              animate={{ \n                rotateY: [0, 360],\n              }}\n              transition={{ \n                duration: 20,\n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              className=\"relative z-10\"\n            >\n              <img src={bitcoinHero} alt=\"Bitcoin\" className=\"w-64 h-64 md:w-80 md:h-80\" />\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Features Section Component with Scroll Animation\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: <FaBitcoin className=\"text-4xl text-yellow-500\" />,\n      title: \"Secure Bitcoin Exchange\",\n      description: \"Trade Bitcoin and other cryptocurrencies on our secure and reliable exchange platform.\"\n    },\n    {\n      icon: <FaChartLine className=\"text-4xl text-accent\" />,\n      title: \"Advanced Trading Tools\",\n      description: \"Access professional trading tools and real-time market data for informed decisions.\"\n    },\n    {\n      icon: <FaShieldAlt className=\"text-4xl text-green-500\" />,\n      title: \"Maximum Security\",\n      description: \"Your assets are protected with industry-leading security measures and encryption.\"\n    },\n    {\n      icon: <FaUserTie className=\"text-4xl text-purple-500\" />,\n      title: \"Expert Support\",\n      description: \"Our team of cryptocurrency experts is available 24/7 to assist you with any questions.\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Why Choose BlazeTrade\" \n          subtitle=\"We provide the best cryptocurrency trading experience with security, reliability, and professional service.\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12\">\n          {features.map((feature, index) => (\n            <FeatureCard \n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Services Section Component\nconst ServicesSection = () => {\n  const services = [\n    {\n      title: \"Bitcoin Exchange\",\n      description: \"Buy and sell Bitcoin and other cryptocurrencies with competitive rates and low fees.\",\n      link: \"/services#bitcoin-exchange\"\n    },\n    {\n      title: \"Crypto Trading\",\n      description: \"Trade cryptocurrencies with advanced tools, charts, and market analysis.\",\n      link: \"/services#crypto-trading\"\n    },\n    {\n      title: \"Market Analysis\",\n      description: \"Get detailed market insights and analysis to make informed trading decisions.\",\n      link: \"/services#market-analysis\"\n    },\n    {\n      title: \"Portfolio Management\",\n      description: \"Professional management of your cryptocurrency portfolio for optimal returns.\",\n      link: \"/services#portfolio-management\"\n    },\n    {\n      title: \"Security Solutions\",\n      description: \"Advanced security solutions to protect your digital assets and investments.\",\n      link: \"/services#security-solutions\"\n    },\n    {\n      title: \"Consulting Services\",\n      description: \"Expert consulting services for individuals and businesses entering the crypto space.\",\n      link: \"/services#consulting\"\n    }\n  ];\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <SectionHeader \n          title=\"Our Services\" \n          subtitle=\"Comprehensive cryptocurrency services tailored to your needs\"\n        />\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\">\n          {services.map((service, index) => (\n            <ServiceCard \n              key={index}\n              title={service.title}\n              description={service.description}\n              link={service.link}\n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Stats Section Component\nconst StatsSection = () => {\n  const stats = [\n    { value: \"10K+\", label: \"Active Users\" },\n    { value: \"$250M+\", label: \"Monthly Volume\" },\n    { value: \"99.9%\", label: \"Uptime\" },\n    { value: \"24/7\", label: \"Support\" }\n  ];\n\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"py-16 bg-primary-dark text-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0 },\n            visible: {\n              opacity: 1,\n              transition: {\n                staggerChildren: 0.2\n              }\n            }\n          }}\n        >\n          {stats.map((stat, index) => (\n            <motion.div \n              key={index}\n              variants={{\n                hidden: { opacity: 0, y: 20 },\n                visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n              }}\n            >\n              <div className=\"text-4xl md:text-5xl font-bold mb-2\">{stat.value}</div>\n              <div className=\"text-gray-300\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// CTA Section Component\nconst CTASection = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-gray-50\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"bg-primary-dark rounded-2xl p-8 md:p-12 text-white text-center\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Ready to trade?</h2>\n          <p className=\"text-lg text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Hit the link below to start trading your cryptocurrencies with Blaze Trade.\n          </p>\n          <a \n            href=\"https://wa.me/2348163309355\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-primary text-lg px-8 py-3\"\n          >\n            Trade Now <FaArrowRight className=\"ml-2\" />\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Reusable Section Header Component\nconst SectionHeader = ({ title, subtitle }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"text-center max-w-3xl mx-auto\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n      }}\n    >\n      <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">{title}</h2>\n      <p className=\"text-lg text-gray-600\">{subtitle}</p>\n    </motion.div>\n  );\n};\n\n// Feature Card Component with Animation\nconst FeatureCard = ({ icon, title, description, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <div className=\"mb-4\">{icon}</div>\n      <h3 className=\"text-xl font-semibold mb-2 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600\">{description}</p>\n    </motion.div>\n  );\n};\n\n// Service Card Component with Animation\nconst ServiceCard = ({ title, description, link, index }) => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <motion.div \n      ref={ref}\n      className=\"bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow duration-300 border border-gray-100\"\n      initial=\"hidden\"\n      animate={controls}\n      variants={{\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n          opacity: 1, \n          y: 0, \n          transition: { \n            duration: 0.5,\n            delay: index * 0.1\n          } \n        }\n      }}\n    >\n      <h3 className=\"text-xl font-semibold mb-3 text-primary-dark\">{title}</h3>\n      <p className=\"text-gray-600 mb-4\">{description}</p>\n      <Link \n        to={link} \n        className=\"text-accent hover:text-primary-dark flex items-center font-medium transition-colors duration-300\"\n      >\n        Learn More <FaArrowRight className=\"ml-2\" />\n      </Link>\n    </motion.div>\n  );\n};\n\n// Instagram CTA Section\nconst InstagramCTA = () => {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2\n  });\n\n  useEffect(() => {\n    if (inView) {\n      controls.start(\"visible\");\n    }\n  }, [controls, inView]);\n\n  return (\n    <section className=\"section bg-white\">\n      <div className=\"container-custom\">\n        <motion.div \n          ref={ref}\n          className=\"text-center max-w-3xl mx-auto\"\n          initial=\"hidden\"\n          animate={controls}\n          variants={{\n            hidden: { opacity: 0, y: 20 },\n            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }\n          }}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-primary-dark\">Follow Us on Instagram</h2>\n          <p className=\"text-lg text-gray-600 mb-8\">\n            Stay up-to-date with the latest news, offers, and trading tips by following our official Instagram page.\n          </p>\n          <a \n            href=\"https://instagram.com/blaze__trade?igsh=M3Y4cmhoNXRO\" \n            target=\"_blank\" \n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center btn-secondary text-lg px-8 py-3\"\n          >\n            @blaze__trade\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Home;"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACpD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAC7F,OAAOC,WAAW,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMsB,WAAW,GAAGlB,kBAAkB,CAACD,IAAI,EAAGoB,WAAW,IAAK;MAC5D,IAAIA,WAAW,EAAE;QACfF,OAAO,CAACE,WAAW,CAAC;MACtB,CAAC,MAAM;QACLF,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC,CAAC;IAEF,OAAO,MAAMC,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEP,OAAA;IAAKS,SAAS,EAAC,OAAO;IAAAC,QAAA,GAEnBL,IAAI,iBACHL,OAAA;MAAKS,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EV,OAAA;QAAKS,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BV,OAAA;UAAKS,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDV,OAAA;YAAGS,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,gBAClB,eAAAV,OAAA;cAAMS,SAAS,EAAC,WAAW;cAAAC,QAAA,EACtCL,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACM,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,kBACP,EAACX,IAAI,CAACY,aAAa,gBACjBjB,OAAA;cAAMS,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAAC;YAErF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEPhB,OAAA;cAAMS,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACJhB,OAAA,CAACb,IAAI;YACH+B,EAAE,EAAC,YAAY;YACfT,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhB,OAAA,CAACmB,WAAW;MAACd,IAAI,EAAEA;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG3BhB,OAAA,CAACoB,YAAY;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhBhB,OAAA,CAACqB,UAAU;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdhB,OAAA,CAACsB,eAAe;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBhB,OAAA,CAACuB,YAAY;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhBhB,OAAA,CAACwB,eAAe;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEV,CAAC;;AAED;AAAAZ,EAAA,CApEMD,IAAI;AAAAsB,EAAA,GAAJtB,IAAI;AAqEV,MAAMgB,WAAW,GAAGA,CAAC;EAAEd;AAAK,CAAC,KAAK;EAChC,oBACEL,OAAA;IAASS,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAEtEV,OAAA;MAAKS,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CV,OAAA;QAAKS,SAAS,EAAC;MAAwF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9GhB,OAAA;QAAKS,SAAS,EAAC;MAAiF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CAAC,eAENhB,OAAA;MAAKS,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBAEnGV,OAAA;QAAKS,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCV,OAAA,CAACV,MAAM,CAACoC,EAAE;UACRjB,SAAS,EAAC,iDAAiD;UAC3DkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAtB,QAAA,EAE7BL,IAAI,gBACHL,OAAA,CAAAE,SAAA;YAAAQ,QAAA,GAAE,eACa,EAAC,GAAG,eACjBV,OAAA;cAAMS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC5BL,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACM,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACPhB,OAAA;cAAMS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACtC,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACZhB,OAAA,CAACV,MAAM,CAAC2C,CAAC;UACPxB,SAAS,EAAC,uCAAuC;UACjDkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,EAEzCL,IAAI,GACH,qGAAqG,GAErG;QACD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACXhB,OAAA,CAACV,MAAM,CAAC6C,GAAG;UACT1B,SAAS,EAAC,sBAAsB;UAChCkB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,EAEzCL,IAAI,gBACHL,OAAA,CAAAE,SAAA;YAAAQ,QAAA,gBACEV,OAAA,CAACb,IAAI;cACH+B,EAAE,EAAC,YAAY;cACfT,SAAS,EAAC,wDAAwD;cAAAC,QAAA,GACnE,kBACiB,eAAAV,OAAA,CAACH,YAAY;gBAACY,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACPhB,OAAA;cACEoC,IAAI,EAAC,6BAA6B;cAClCC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB7B,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CAAC,gBAEHhB,OAAA,CAAAE,SAAA;YAAAQ,QAAA,gBACEV,OAAA,CAACb,IAAI;cACH+B,EAAE,EAAC,SAAS;cACZT,SAAS,EAAC,wDAAwD;cAAAC,QAAA,GACnE,cACa,eAAAV,OAAA,CAACH,YAAY;gBAACY,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACPhB,OAAA;cACEoC,IAAI,EAAC,6BAA6B;cAClCC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB7B,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhB,OAAA,CAACV,MAAM,CAAC6C,GAAG;QACT1B,SAAS,EAAC,8BAA8B;QACxCkB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEW,KAAK,EAAE;QAAI,CAAE;QACpCT,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEW,KAAK,EAAE;QAAE,CAAE;QAClCR,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAtB,QAAA,eAE9BV,OAAA;UAAKS,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBV,OAAA;YAAKS,SAAS,EAAC;UAA2I;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjKhB,OAAA,CAACV,MAAM,CAAC6C,GAAG;YACTL,OAAO,EAAE;cACPU,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG;YAClB,CAAE;YACFT,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZS,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR,CAAE;YACFlC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAEzBV,OAAA;cAAK4C,GAAG,EAAE9C,WAAY;cAAC+C,GAAG,EAAC,SAAS;cAACpC,SAAS,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA8B,GAAA,GAnHM3B,WAAW;AAoHjB,MAAMG,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMyB,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEhD,OAAA,CAACP,SAAS;MAACgB,SAAS,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDiC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAEhD,OAAA,CAACN,WAAW;MAACe,SAAS,EAAC;IAAsB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtDiC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAEhD,OAAA,CAACL,WAAW;MAACc,SAAS,EAAC;IAAyB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDiC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,eAAEhD,OAAA,CAACJ,SAAS;MAACa,SAAS,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDiC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACElD,OAAA;IAASS,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrCV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BV,OAAA,CAACmD,aAAa;QACZF,KAAK,EAAC,uBAAuB;QAC7BG,QAAQ,EAAC;MAA6G;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC,eAEFhB,OAAA;QAAKS,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEqC,QAAQ,CAACM,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BvD,OAAA,CAACwD,WAAW;UAEVR,IAAI,EAAEM,OAAO,CAACN,IAAK;UACnBC,KAAK,EAAEK,OAAO,CAACL,KAAM;UACrBC,WAAW,EAAEI,OAAO,CAACJ,WAAY;UACjCK,KAAK,EAAEA;QAAM,GAJRA,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAAyC,GAAA,GAhDMnC,eAAe;AAiDrB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMkC,QAAQ,GAAG,CACf;IACET,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,sFAAsF;IACnGS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0EAA0E;IACvFS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,+EAA+E;IAC5FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,+EAA+E;IAC5FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,6EAA6E;IAC1FS,IAAI,EAAE;EACR,CAAC,EACD;IACEV,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,sFAAsF;IACnGS,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACE3D,OAAA;IAASS,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BV,OAAA,CAACmD,aAAa;QACZF,KAAK,EAAC,cAAc;QACpBG,QAAQ,EAAC;MAA8D;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAEFhB,OAAA;QAAKS,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEgD,QAAQ,CAACL,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3BvD,OAAA,CAAC6D,WAAW;UAEVZ,KAAK,EAAEW,OAAO,CAACX,KAAM;UACrBC,WAAW,EAAEU,OAAO,CAACV,WAAY;UACjCS,IAAI,EAAEC,OAAO,CAACD,IAAK;UACnBJ,KAAK,EAAEA;QAAM,GAJRA,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA8C,GAAA,GA1DMtC,eAAe;AA2DrB,MAAMD,YAAY,GAAGA,CAAA,KAAM;EAAAwC,GAAA;EACzB,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAe,CAAC,EACxC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC5C;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,CACpC;EAED,MAAMC,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA;IAASS,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eACnDV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BV,OAAA,CAACV,MAAM,CAAC6C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,mDAAmD;QAC7DkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE;UAAE,CAAC;UACtB+C,OAAO,EAAE;YACP/C,OAAO,EAAE,CAAC;YACVG,UAAU,EAAE;cACV6C,eAAe,EAAE;YACnB;UACF;QACF,CAAE;QAAAlE,QAAA,EAEDsD,KAAK,CAACX,GAAG,CAAC,CAACwB,IAAI,EAAEtB,KAAK,kBACrBvD,OAAA,CAACV,MAAM,CAAC6C,GAAG;UAETsC,QAAQ,EAAE;YACRC,MAAM,EAAE;cAAE9C,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAC;YAC7B8C,OAAO,EAAE;cAAE/C,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;cAAEE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI;YAAE;UAC7D,CAAE;UAAAtB,QAAA,gBAEFV,OAAA;YAAKS,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAEmE,IAAI,CAACZ;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEhB,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEmE,IAAI,CAACX;UAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAP5CuC,KAAK;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA+C,GAAA,CAxDMxC,YAAY;EAAA,QAQChC,YAAY,EACPC,SAAS;AAAA;AAAAsF,GAAA,GAT3BvD,YAAY;AAyDlB,MAAMF,UAAU,GAAGA,CAAA,KAAM;EAAA0D,GAAA;EACvB,MAAMZ,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA;IAASS,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACrCV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BV,OAAA,CAACV,MAAM,CAAC6C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,gEAAgE;QAC1EkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAC;UAC7B8C,OAAO,EAAE;YAAE/C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI;UAAE;QAC7D,CAAE;QAAAtB,QAAA,gBAEFV,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEhB,OAAA;UAAGS,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhB,OAAA;UACEoC,IAAI,EAAC,6BAA6B;UAClCC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,wDAAwD;UAAAC,QAAA,GACnE,YACW,eAAAV,OAAA,CAACH,YAAY;YAACY,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAA+D,GAAA,CA5CM1D,UAAU;EAAA,QACG9B,YAAY,EACPC,SAAS;AAAA;AAAAwF,GAAA,GAF3B3D,UAAU;AA6ChB,MAAM8B,aAAa,GAAGA,CAAC;EAAEF,KAAK;EAAEG;AAAS,CAAC,KAAK;EAAA6B,GAAA;EAC7C,MAAMd,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA,CAACV,MAAM,CAAC6C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,+BAA+B;IACzCkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QAAE/C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;IAC7D,CAAE;IAAAtB,QAAA,gBAEFV,OAAA;MAAIS,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAClFhB,OAAA;MAAGS,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAE0C;IAAQ;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC;AAEjB,CAAC;;AAED;AAAAiE,GAAA,CA9BM9B,aAAa;EAAA,QACA5D,YAAY,EACPC,SAAS;AAAA;AAAA0F,GAAA,GAF3B/B,aAAa;AA+BnB,MAAMK,WAAW,GAAGA,CAAC;EAAER,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEK;AAAM,CAAC,KAAK;EAAA4B,GAAA;EAC3D,MAAMhB,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA,CAACV,MAAM,CAAC6C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,kFAAkF;IAC5FkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QACP/C,OAAO,EAAE,CAAC;QACVC,CAAC,EAAE,CAAC;QACJE,UAAU,EAAE;UACVC,QAAQ,EAAE,GAAG;UACbE,KAAK,EAAEqB,KAAK,GAAG;QACjB;MACF;IACF,CAAE;IAAA7C,QAAA,gBAEFV,OAAA;MAAKS,SAAS,EAAC,MAAM;MAAAC,QAAA,EAAEsC;IAAI;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAClChB,OAAA;MAAIS,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACzEhB,OAAA;MAAGS,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEwC;IAAW;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpC,CAAC;AAEjB,CAAC;;AAED;AAAAmE,GAAA,CAtCM3B,WAAW;EAAA,QACEjE,YAAY,EACPC,SAAS;AAAA;AAAA4F,GAAA,GAF3B5B,WAAW;AAuCjB,MAAMK,WAAW,GAAGA,CAAC;EAAEZ,KAAK;EAAEC,WAAW;EAAES,IAAI;EAAEJ;AAAM,CAAC,KAAK;EAAA8B,GAAA;EAC3D,MAAMlB,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA,CAACV,MAAM,CAAC6C,GAAG;IACTiC,GAAG,EAAEA,GAAI;IACT3D,SAAS,EAAC,iGAAiG;IAC3GkB,OAAO,EAAC,QAAQ;IAChBG,OAAO,EAAEqC,QAAS;IAClBM,QAAQ,EAAE;MACRC,MAAM,EAAE;QAAE9C,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC;MAC7B8C,OAAO,EAAE;QACP/C,OAAO,EAAE,CAAC;QACVC,CAAC,EAAE,CAAC;QACJE,UAAU,EAAE;UACVC,QAAQ,EAAE,GAAG;UACbE,KAAK,EAAEqB,KAAK,GAAG;QACjB;MACF;IACF,CAAE;IAAA7C,QAAA,gBAEFV,OAAA;MAAIS,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAAEuC;IAAK;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACzEhB,OAAA;MAAGS,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAEwC;IAAW;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnDhB,OAAA,CAACb,IAAI;MACH+B,EAAE,EAAEyC,IAAK;MACTlD,SAAS,EAAC,kGAAkG;MAAAC,QAAA,GAC7G,aACY,eAAAV,OAAA,CAACH,YAAY;QAACY,SAAS,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;;AAED;AAAAqE,GAAA,CA3CMxB,WAAW;EAAA,QACEtE,YAAY,EACPC,SAAS;AAAA;AAAA8F,GAAA,GAF3BzB,WAAW;AA4CjB,MAAMzC,YAAY,GAAGA,CAAA,KAAM;EAAAmE,GAAA;EACzB,MAAMpB,QAAQ,GAAG5E,YAAY,CAAC,CAAC;EAC/B,MAAM,CAAC6E,GAAG,EAAEC,MAAM,CAAC,GAAG7E,SAAS,CAAC;IAC9B8E,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtF,SAAS,CAAC,MAAM;IACd,IAAIoF,MAAM,EAAE;MACVF,QAAQ,CAACK,KAAK,CAAC,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAEtB,oBACErE,OAAA;IAASS,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BV,OAAA,CAACV,MAAM,CAAC6C,GAAG;QACTiC,GAAG,EAAEA,GAAI;QACT3D,SAAS,EAAC,+BAA+B;QACzCkB,OAAO,EAAC,QAAQ;QAChBG,OAAO,EAAEqC,QAAS;QAClBM,QAAQ,EAAE;UACRC,MAAM,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAC;UAC7B8C,OAAO,EAAE;YAAE/C,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI;UAAE;QAC7D,CAAE;QAAAtB,QAAA,gBAEFV,OAAA;UAAIS,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjGhB,OAAA;UAAGS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhB,OAAA;UACEoC,IAAI,EAAC,sDAAsD;UAC3DC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACrE;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACuE,GAAA,CA1CInE,YAAY;EAAA,QACC7B,YAAY,EACPC,SAAS;AAAA;AAAAgG,GAAA,GAF3BpE,YAAY;AA4ClB,eAAejB,IAAI;AAAC,IAAAsB,EAAA,EAAAqB,GAAA,EAAAW,GAAA,EAAAK,GAAA,EAAAgB,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAhE,EAAA;AAAAgE,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}