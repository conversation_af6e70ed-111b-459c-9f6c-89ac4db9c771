{"ast": null, "code": "// Import the functions you need from the SDKs you need\nimport{initializeApp}from\"firebase/app\";import{getAuth}from\"firebase/auth\";// TODO: Add SDKs for Firebase products that you want to use\n// https://firebase.google.com/docs/web/setup#available-libraries\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig={apiKey:\"AIzaSyCPSlCfn-WQL3z9_0Q_2LtoJVk5_iB2BqA\",authDomain:\"blazetrade-app.firebaseapp.com\",projectId:\"blazetrade-app\",storageBucket:\"blazetrade-app.firebasestorage.app\",messagingSenderId:\"517986886906\",appId:\"1:517986886906:web:400d4aaba033f0d68fd543\",measurementId:\"G-HCPMXY78RZ\"};// Initialize Firebase\nconst app=initializeApp(firebaseConfig);// Initialize Firebase Authentication and get a reference to the service\nexport const auth=getAuth(app);", "map": {"version": 3, "names": ["initializeApp", "getAuth", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/firebase.js"], "sourcesContent": ["// Import the functions you need from the SDKs you need\nimport { initializeApp } from \"firebase/app\";\nimport { getAuth } from \"firebase/auth\";\n// TODO: Add SDKs for Firebase products that you want to use\n// https://firebase.google.com/docs/web/setup#available-libraries\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n  apiKey: \"AIzaSyCPSlCfn-WQL3z9_0Q_2LtoJVk5_iB2BqA\",\n  authDomain: \"blazetrade-app.firebaseapp.com\",\n  projectId: \"blazetrade-app\",\n  storageBucket: \"blazetrade-app.firebasestorage.app\",\n  messagingSenderId: \"517986886906\",\n  appId: \"1:517986886906:web:400d4aaba033f0d68fd543\",\n  measurementId: \"G-HCPMXY78RZ\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n// Initialize Firebase Authentication and get a reference to the service\nexport const auth = getAuth(app);"], "mappings": "AAAA;AACA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,KAAQ,eAAe,CACvC;AACA;AAEA;AACA;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBC,MAAM,CAAE,yCAAyC,CACjDC,UAAU,CAAE,gCAAgC,CAC5CC,SAAS,CAAE,gBAAgB,CAC3BC,aAAa,CAAE,oCAAoC,CACnDC,iBAAiB,CAAE,cAAc,CACjCC,KAAK,CAAE,2CAA2C,CAClDC,aAAa,CAAE,cACjB,CAAC,CAED;AACA,KAAM,CAAAC,GAAG,CAAGV,aAAa,CAACE,cAAc,CAAC,CACzC;AACA,MAAO,MAAM,CAAAS,IAAI,CAAGV,OAAO,CAACS,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}