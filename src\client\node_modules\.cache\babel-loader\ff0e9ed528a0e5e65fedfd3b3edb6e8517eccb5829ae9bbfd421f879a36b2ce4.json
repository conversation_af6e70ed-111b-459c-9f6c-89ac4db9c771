{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blazetrad-app\\\\src\\\\client\\\\src\\\\pages\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleJoin = e => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-black text-white min-h-screen font-sans\",\n    children: /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container-custom pt-32 pb-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"grid md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-left\",\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-6xl md:text-7xl font-bold leading-tight\",\n            children: [\"Trade Crypto\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-400\",\n              children: \"Like a Pro.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-lg text-gray-400\",\n            children: \"Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleJoin,\n            className: \"mt-8 flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              placeholder: \"Email/Phone Number\",\n              className: \"flex-grow bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"bg-green-500 hover:bg-green-600 text-black font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\",\n              children: [\"Join Us\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"hidden md:block\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1639754391392-164dd2d4694b?q=80&w=1854&auto=format&fit=crop\",\n            alt: \"Crypto Abstract Art\",\n            className: \"rounded-2xl shadow-2xl shadow-green-500/20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-24\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-900 border border-gray-800 rounded-xl p-8 hover:border-green-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Support New Listings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-gray-400\",\n              children: \"Be the first to trade promising new assets. Let's get them listed on BlazeTrade!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              className: \"mt-4 inline-block text-green-400 font-semibold hover:text-green-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-900 border border-gray-800 rounded-xl p-8 hover:border-green-500/50 transition duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold\",\n              children: \"Exclusive Referral Bonuses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-gray-400\",\n              children: \"Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/signup\",\n              className: \"mt-4 inline-block text-green-400 font-semibold hover:text-green-300\",\n              children: \"Join Now \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "motion", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "navigate", "handleJoin", "e", "preventDefault", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "scale", "delay", "src", "alt", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/src/pages/LandingPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\nconst LandingPage = () => {\n  const navigate = useNavigate();\n\n  const handleJoin = (e) => {\n    e.preventDefault();\n    navigate('/signup');\n  };\n\n  return (\n    <div className=\"bg-black text-white min-h-screen font-sans\">\n      <main className=\"container-custom pt-32 pb-16\">\n        {/* Hero Section */}\n        <section className=\"grid md:grid-cols-2 gap-8 items-center\">\n          <motion.div \n            className=\"text-left\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-6xl md:text-7xl font-bold leading-tight\">\n              Trade Crypto\n              <br />\n              <span className=\"text-green-400\">Like a Pro.</span>\n            </h1>\n            <p className=\"mt-6 text-lg text-gray-400\">\n              Join the next generation of trading. BlazeTrade offers institutional-grade tools in a simple, intuitive package.\n            </p>\n            <form onSubmit={handleJoin} className=\"mt-8 flex flex-col sm:flex-row gap-4\">\n              <input \n                type=\"email\" \n                placeholder=\"Email/Phone Number\" \n                className=\"flex-grow bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-green-400\"\n              />\n              <button type=\"submit\" className=\"bg-green-500 hover:bg-green-600 text-black font-bold rounded-lg px-8 py-3 transition duration-300 flex items-center justify-center gap-2\">\n                Join Us\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n            </form>\n          </motion.div>\n          <motion.div \n            className=\"hidden md:block\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <img src=\"https://images.unsplash.com/photo-1639754391392-164dd2d4694b?q=80&w=1854&auto=format&fit=crop\" alt=\"Crypto Abstract Art\" className=\"rounded-2xl shadow-2xl shadow-green-500/20\"/>\n          </motion.div>\n        </section>\n\n        {/* Promo Cards Section */}\n        <section className=\"mt-24\">\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-gray-900 border border-gray-800 rounded-xl p-8 hover:border-green-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Support New Listings</h3>\n              <p className=\"mt-2 text-gray-400\">Be the first to trade promising new assets. Let's get them listed on BlazeTrade!</p>\n              <Link to=\"/services\" className=\"mt-4 inline-block text-green-400 font-semibold hover:text-green-300\">Join Now &rarr;</Link>\n            </div>\n            <div className=\"bg-gray-900 border border-gray-800 rounded-xl p-8 hover:border-green-500/50 transition duration-300\">\n              <h3 className=\"text-2xl font-bold\">Exclusive Referral Bonuses</h3>\n              <p className=\"mt-2 text-gray-400\">Receive an exclusive merchandise package and enjoy top-tier referral bonuses when you invite friends.</p>\n              <Link to=\"/signup\" className=\"mt-4 inline-block text-green-400 font-semibold hover:text-green-300\">Join Now &rarr;</Link>\n            </div>\n          </div>\n        </section>\n      </main>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9B,MAAMO,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,oBACEH,OAAA;IAAKO,SAAS,EAAC,4CAA4C;IAAAC,QAAA,eACzDR,OAAA;MAAMO,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE5CR,OAAA;QAASO,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACzDR,OAAA,CAACF,MAAM,CAACW,GAAG;UACTF,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9BR,OAAA;YAAIO,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,cAE3D,eAAAR,OAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnB,OAAA;cAAMO,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACLnB,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnB,OAAA;YAAMoB,QAAQ,EAAEhB,UAAW;YAACG,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAC1ER,OAAA;cACEqB,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,oBAAoB;cAChCf,SAAS,EAAC;YAAwH;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnI,CAAC,eACFnB,OAAA;cAAQqB,IAAI,EAAC,QAAQ;cAACd,SAAS,EAAC,0IAA0I;cAAAC,QAAA,GAAC,SAEzK,eAAAR,OAAA;gBAAKuB,KAAK,EAAC,4BAA4B;gBAAChB,SAAS,EAAC,SAAS;gBAACiB,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAjB,QAAA,eACjGR,OAAA;kBAAM0B,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,0IAA0I;kBAACC,QAAQ,EAAC;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACbnB,OAAA,CAACF,MAAM,CAACW,GAAG;UACTF,SAAS,EAAC,iBAAiB;UAC3BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEkB,KAAK,EAAE;UAAI,CAAE;UACpChB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEkB,KAAK,EAAE;UAAE,CAAE;UAClCf,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEe,KAAK,EAAE;UAAI,CAAE;UAAAtB,QAAA,eAE1CR,OAAA;YAAK+B,GAAG,EAAC,+FAA+F;YAACC,GAAG,EAAC,qBAAqB;YAACzB,SAAS,EAAC;UAA4C;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGVnB,OAAA;QAASO,SAAS,EAAC,OAAO;QAAAC,QAAA,eACxBR,OAAA;UAAKO,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCR,OAAA;YAAKO,SAAS,EAAC,qGAAqG;YAAAC,QAAA,gBAClHR,OAAA;cAAIO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DnB,OAAA;cAAGO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAgF;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtHnB,OAAA,CAACJ,IAAI;cAACqC,EAAE,EAAC,WAAW;cAAC1B,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAAC;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH,CAAC,eACNnB,OAAA;YAAKO,SAAS,EAAC,qGAAqG;YAAAC,QAAA,gBAClHR,OAAA;cAAIO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAA0B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEnB,OAAA;cAAGO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAqG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3InB,OAAA,CAACJ,IAAI;cAACqC,EAAE,EAAC,SAAS;cAAC1B,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAAC;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjB,EAAA,CArEID,WAAW;EAAA,QACEJ,WAAW;AAAA;AAAAqC,EAAA,GADxBjC,WAAW;AAuEjB,eAAeA,WAAW;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}