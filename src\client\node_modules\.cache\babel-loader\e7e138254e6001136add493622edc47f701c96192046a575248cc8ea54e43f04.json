{"ast": null, "code": "import { calcInset } from './inset.mjs';\nimport { ScrollOffset } from './presets.mjs';\nimport { resolveOffset } from './offset.mjs';\nimport { interpolate } from '../../../../utils/interpolate.mjs';\nimport { defaultOffset } from '../../../../utils/offsets/default.mjs';\nconst point = {\n  x: 0,\n  y: 0\n};\nfunction getTargetSize(target) {\n  return \"getBBox\" in target && target.tagName !== \"svg\" ? target.getBBox() : {\n    width: target.clientWidth,\n    height: target.clientHeight\n  };\n}\nfunction resolveOffsets(container, info, options) {\n  let {\n    offset: offsetDefinition = ScrollOffset.All\n  } = options;\n  const {\n    target = container,\n    axis = \"y\"\n  } = options;\n  const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n  const inset = target !== container ? calcInset(target, container) : point;\n  /**\n   * Measure the target and container. If they're the same thing then we\n   * use the container's scrollWidth/Height as the target, from there\n   * all other calculations can remain the same.\n   */\n  const targetSize = target === container ? {\n    width: container.scrollWidth,\n    height: container.scrollHeight\n  } : getTargetSize(target);\n  const containerSize = {\n    width: container.clientWidth,\n    height: container.clientHeight\n  };\n  /**\n   * Reset the length of the resolved offset array rather than creating a new one.\n   * TODO: More reusable data structures for targetSize/containerSize would also be good.\n   */\n  info[axis].offset.length = 0;\n  /**\n   * Populate the offset array by resolving the user's offset definition into\n   * a list of pixel scroll offets.\n   */\n  let hasChanged = !info[axis].interpolate;\n  const numOffsets = offsetDefinition.length;\n  for (let i = 0; i < numOffsets; i++) {\n    const offset = resolveOffset(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n    if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n      hasChanged = true;\n    }\n    info[axis].offset[i] = offset;\n  }\n  /**\n   * If the pixel scroll offsets have changed, create a new interpolator function\n   * to map scroll value into a progress.\n   */\n  if (hasChanged) {\n    info[axis].interpolate = interpolate(info[axis].offset, defaultOffset(offsetDefinition));\n    info[axis].interpolatorOffsets = [...info[axis].offset];\n  }\n  info[axis].progress = info[axis].interpolate(info[axis].current);\n}\nexport { resolveOffsets };", "map": {"version": 3, "names": ["calcInset", "ScrollOffset", "resolveOffset", "interpolate", "defaultOffset", "point", "x", "y", "getTargetSize", "target", "tagName", "getBBox", "width", "clientWidth", "height", "clientHeight", "resolveOffsets", "container", "info", "options", "offset", "offsetDefinition", "All", "axis", "lengthLabel", "inset", "targetSize", "scrollWidth", "scrollHeight", "containerSize", "length", "has<PERSON><PERSON>ed", "numOffsets", "i", "interpolatorOffsets", "progress", "current"], "sources": ["C:/Users/<USER>/Desktop/blazetrad-app/src/client/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs"], "sourcesContent": ["import { calcInset } from './inset.mjs';\nimport { ScrollOffset } from './presets.mjs';\nimport { resolveOffset } from './offset.mjs';\nimport { interpolate } from '../../../../utils/interpolate.mjs';\nimport { defaultOffset } from '../../../../utils/offsets/default.mjs';\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    let { offset: offsetDefinition = ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? calcInset(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = resolveOffset(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = interpolate(info[axis].offset, defaultOffset(offsetDefinition));\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = info[axis].interpolate(info[axis].current);\n}\n\nexport { resolveOffsets };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,aAAa,QAAQ,uCAAuC;AAErE,MAAMC,KAAK,GAAG;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAE,CAAC;AAC5B,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC3B,OAAO,SAAS,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,KAAK,KAAK,GAChDD,MAAM,CAACE,OAAO,CAAC,CAAC,GAChB;IAAEC,KAAK,EAAEH,MAAM,CAACI,WAAW;IAAEC,MAAM,EAAEL,MAAM,CAACM;EAAa,CAAC;AACpE;AACA,SAASC,cAAcA,CAACC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAI;IAAEC,MAAM,EAAEC,gBAAgB,GAAGpB,YAAY,CAACqB;EAAI,CAAC,GAAGH,OAAO;EAC7D,MAAM;IAAEV,MAAM,GAAGQ,SAAS;IAAEM,IAAI,GAAG;EAAI,CAAC,GAAGJ,OAAO;EAClD,MAAMK,WAAW,GAAGD,IAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;EACrD,MAAME,KAAK,GAAGhB,MAAM,KAAKQ,SAAS,GAAGjB,SAAS,CAACS,MAAM,EAAEQ,SAAS,CAAC,GAAGZ,KAAK;EACzE;AACJ;AACA;AACA;AACA;EACI,MAAMqB,UAAU,GAAGjB,MAAM,KAAKQ,SAAS,GACjC;IAAEL,KAAK,EAAEK,SAAS,CAACU,WAAW;IAAEb,MAAM,EAAEG,SAAS,CAACW;EAAa,CAAC,GAChEpB,aAAa,CAACC,MAAM,CAAC;EAC3B,MAAMoB,aAAa,GAAG;IAClBjB,KAAK,EAAEK,SAAS,CAACJ,WAAW;IAC5BC,MAAM,EAAEG,SAAS,CAACF;EACtB,CAAC;EACD;AACJ;AACA;AACA;EACIG,IAAI,CAACK,IAAI,CAAC,CAACH,MAAM,CAACU,MAAM,GAAG,CAAC;EAC5B;AACJ;AACA;AACA;EACI,IAAIC,UAAU,GAAG,CAACb,IAAI,CAACK,IAAI,CAAC,CAACpB,WAAW;EACxC,MAAM6B,UAAU,GAAGX,gBAAgB,CAACS,MAAM;EAC1C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;IACjC,MAAMb,MAAM,GAAGlB,aAAa,CAACmB,gBAAgB,CAACY,CAAC,CAAC,EAAEJ,aAAa,CAACL,WAAW,CAAC,EAAEE,UAAU,CAACF,WAAW,CAAC,EAAEC,KAAK,CAACF,IAAI,CAAC,CAAC;IACnH,IAAI,CAACQ,UAAU,IAAIX,MAAM,KAAKF,IAAI,CAACK,IAAI,CAAC,CAACW,mBAAmB,CAACD,CAAC,CAAC,EAAE;MAC7DF,UAAU,GAAG,IAAI;IACrB;IACAb,IAAI,CAACK,IAAI,CAAC,CAACH,MAAM,CAACa,CAAC,CAAC,GAAGb,MAAM;EACjC;EACA;AACJ;AACA;AACA;EACI,IAAIW,UAAU,EAAE;IACZb,IAAI,CAACK,IAAI,CAAC,CAACpB,WAAW,GAAGA,WAAW,CAACe,IAAI,CAACK,IAAI,CAAC,CAACH,MAAM,EAAEhB,aAAa,CAACiB,gBAAgB,CAAC,CAAC;IACxFH,IAAI,CAACK,IAAI,CAAC,CAACW,mBAAmB,GAAG,CAAC,GAAGhB,IAAI,CAACK,IAAI,CAAC,CAACH,MAAM,CAAC;EAC3D;EACAF,IAAI,CAACK,IAAI,CAAC,CAACY,QAAQ,GAAGjB,IAAI,CAACK,IAAI,CAAC,CAACpB,WAAW,CAACe,IAAI,CAACK,IAAI,CAAC,CAACa,OAAO,CAAC;AACpE;AAEA,SAASpB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}